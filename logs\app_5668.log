2025-06-18 23:08:56,793 INFO: 应用启动 - PID: 5668 [in C:\studentscmssp\app\__init__.py:831]
2025-06-18 23:09:19,517 INFO: 自动填充菜单数据: 早餐=None, 午餐=None, 晚餐=None [in C:\studentscmssp\app\services\daily_management_service.py:341]
2025-06-18 23:09:19,564 INFO: 自动创建日志成功: 用户=guest_demo, 学校=海淀区中关村第一小学, 日期=2025-06-18 [in C:\studentscmssp\app\services\auto_daily_log_service.py:66]
2025-06-18 23:11:39,350 INFO: 查询菜谱：日期=2025-06-18, 星期=2(0=周一), day_of_week=3, 餐次=午餐, 区域ID=44 [in C:\studentscmssp\app\routes\food_trace.py:324]
2025-06-18 23:11:39,365 INFO: 找到 1 个周菜单 [in C:\studentscmssp\app\routes\food_trace.py:334]
2025-06-18 23:11:39,365 INFO: 匹配条件的食谱有 0 个 [in C:\studentscmssp\app\routes\food_trace.py:346]
2025-06-18 23:11:39,365 INFO: 未找到 2025-06-18 午餐 的菜谱信息 [in C:\studentscmssp\app\routes\food_trace.py:365]
2025-06-18 23:11:39,375 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\studentscmssp\app\routes\food_trace.py:531]
2025-06-18 23:56:34,153 INFO: === 开始生成应付账款 === [in C:\studentscmssp\app\routes\financial\payables.py:299]
2025-06-18 23:56:34,153 INFO: 请求数据: {'stock_in_id': 103} [in C:\studentscmssp\app\routes\financial\payables.py:303]
2025-06-18 23:56:34,153 INFO: 入库单ID: 103 [in C:\studentscmssp\app\routes\financial\payables.py:306]
2025-06-18 23:56:34,153 INFO: 查询入库单: ID=103, area_id=44 [in C:\studentscmssp\app\routes\financial\payables.py:313]
2025-06-18 23:56:34,153 INFO: 找到入库单: RK20250614230014, 状态: 已入库, 总金额: 10248.40 [in C:\studentscmssp\app\routes\financial\payables.py:323]
2025-06-18 23:56:34,153 INFO: 检查是否已生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:326]
2025-06-18 23:56:34,169 INFO: 检查入库单状态: 已入库 [in C:\studentscmssp\app\routes\financial\payables.py:333]
2025-06-18 23:56:34,169 INFO: 开始生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:339]
2025-06-18 23:56:34,169 INFO: 应付账款编号前缀: AP20250618 [in C:\studentscmssp\app\routes\financial\payables.py:344]
2025-06-18 23:56:34,169 INFO: 生成首个应付账款编号: AP20250618001 [in C:\studentscmssp\app\routes\financial\payables.py:357]
2025-06-18 23:56:34,179 INFO: 开始查询会计科目... [in C:\studentscmssp\app\routes\financial\payables.py:399]
2025-06-18 23:56:34,179 INFO: 查询原材料科目 (1201)... [in C:\studentscmssp\app\routes\financial\payables.py:401]
2025-06-18 23:56:34,185 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\studentscmssp\app\routes\financial\payables.py:411]
2025-06-18 23:56:34,185 INFO: 查询应付账款科目 (2001)... [in C:\studentscmssp\app\routes\financial\payables.py:415]
2025-06-18 23:56:34,185 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\studentscmssp\app\routes\financial\payables.py:425]
2025-06-18 23:56:34,185 INFO: 准备凭证明细SQL... [in C:\studentscmssp\app\routes\financial\payables.py:437]
2025-06-18 23:56:34,185 INFO: 准备更新入库单SQL... [in C:\studentscmssp\app\routes\financial\payables.py:440]
2025-06-18 23:56:34,185 INFO: 开始执行数据库事务... [in C:\studentscmssp\app\routes\financial\payables.py:443]
2025-06-18 23:56:34,185 INFO: 执行应付账款SQL... [in C:\studentscmssp\app\routes\financial\payables.py:447]
2025-06-18 23:56:34,185 INFO: 应付账款创建成功，ID: 10 [in C:\studentscmssp\app\routes\financial\payables.py:457]
2025-06-18 23:56:34,185 INFO: 执行财务凭证SQL... [in C:\studentscmssp\app\routes\financial\payables.py:460]
2025-06-18 23:56:34,185 INFO: 财务凭证创建成功，ID: 39 [in C:\studentscmssp\app\routes\financial\payables.py:470]
2025-06-18 23:56:34,185 INFO: 创建凭证明细... [in C:\studentscmssp\app\routes\financial\payables.py:474]
2025-06-18 23:56:34,201 INFO: 借方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:484]
2025-06-18 23:56:34,201 INFO: 贷方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:494]
2025-06-18 23:56:34,201 INFO: 更新入库单关联信息... [in C:\studentscmssp\app\routes\financial\payables.py:499]
2025-06-18 23:56:34,217 INFO: 入库单更新成功 [in C:\studentscmssp\app\routes\financial\payables.py:506]
2025-06-18 23:56:34,217 INFO: 事务提交成功 [in C:\studentscmssp\app\routes\financial\payables.py:510]
2025-06-18 23:56:34,217 INFO: === 应付账款生成成功 === [in C:\studentscmssp\app\routes\financial\payables.py:512]
2025-06-18 23:56:34,217 INFO: 应付账款ID: 10 [in C:\studentscmssp\app\routes\financial\payables.py:513]
2025-06-18 23:56:34,217 INFO: 财务凭证ID: 39 [in C:\studentscmssp\app\routes\financial\payables.py:514]
2025-06-18 23:56:34,845 INFO: === 开始生成应付账款 === [in C:\studentscmssp\app\routes\financial\payables.py:299]
2025-06-18 23:56:34,845 INFO: 请求数据: {'stock_in_id': 102} [in C:\studentscmssp\app\routes\financial\payables.py:303]
2025-06-18 23:56:34,845 INFO: 入库单ID: 102 [in C:\studentscmssp\app\routes\financial\payables.py:306]
2025-06-18 23:56:34,845 INFO: 查询入库单: ID=102, area_id=44 [in C:\studentscmssp\app\routes\financial\payables.py:313]
2025-06-18 23:56:34,845 INFO: 找到入库单: RK20250614225320, 状态: 已入库, 总金额: 11625.80 [in C:\studentscmssp\app\routes\financial\payables.py:323]
2025-06-18 23:56:34,845 INFO: 检查是否已生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:326]
2025-06-18 23:56:34,845 INFO: 检查入库单状态: 已入库 [in C:\studentscmssp\app\routes\financial\payables.py:333]
2025-06-18 23:56:34,845 INFO: 开始生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:339]
2025-06-18 23:56:34,845 INFO: 应付账款编号前缀: AP20250618 [in C:\studentscmssp\app\routes\financial\payables.py:344]
2025-06-18 23:56:34,845 INFO: 基于最后编号 AP20250618001 生成新编号: AP20250618002 [in C:\studentscmssp\app\routes\financial\payables.py:354]
2025-06-18 23:56:34,845 INFO: 开始查询会计科目... [in C:\studentscmssp\app\routes\financial\payables.py:399]
2025-06-18 23:56:34,845 INFO: 查询原材料科目 (1201)... [in C:\studentscmssp\app\routes\financial\payables.py:401]
2025-06-18 23:56:34,860 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\studentscmssp\app\routes\financial\payables.py:411]
2025-06-18 23:56:34,860 INFO: 查询应付账款科目 (2001)... [in C:\studentscmssp\app\routes\financial\payables.py:415]
2025-06-18 23:56:34,860 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\studentscmssp\app\routes\financial\payables.py:425]
2025-06-18 23:56:34,860 INFO: 准备凭证明细SQL... [in C:\studentscmssp\app\routes\financial\payables.py:437]
2025-06-18 23:56:34,860 INFO: 准备更新入库单SQL... [in C:\studentscmssp\app\routes\financial\payables.py:440]
2025-06-18 23:56:34,860 INFO: 开始执行数据库事务... [in C:\studentscmssp\app\routes\financial\payables.py:443]
2025-06-18 23:56:34,860 INFO: 执行应付账款SQL... [in C:\studentscmssp\app\routes\financial\payables.py:447]
2025-06-18 23:56:34,860 INFO: 应付账款创建成功，ID: 11 [in C:\studentscmssp\app\routes\financial\payables.py:457]
2025-06-18 23:56:34,860 INFO: 执行财务凭证SQL... [in C:\studentscmssp\app\routes\financial\payables.py:460]
2025-06-18 23:56:34,860 INFO: 财务凭证创建成功，ID: 40 [in C:\studentscmssp\app\routes\financial\payables.py:470]
2025-06-18 23:56:34,860 INFO: 创建凭证明细... [in C:\studentscmssp\app\routes\financial\payables.py:474]
2025-06-18 23:56:34,860 INFO: 借方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:484]
2025-06-18 23:56:34,860 INFO: 贷方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:494]
2025-06-18 23:56:34,860 INFO: 更新入库单关联信息... [in C:\studentscmssp\app\routes\financial\payables.py:499]
2025-06-18 23:56:34,860 INFO: 入库单更新成功 [in C:\studentscmssp\app\routes\financial\payables.py:506]
2025-06-18 23:56:34,860 INFO: 事务提交成功 [in C:\studentscmssp\app\routes\financial\payables.py:510]
2025-06-18 23:56:34,860 INFO: === 应付账款生成成功 === [in C:\studentscmssp\app\routes\financial\payables.py:512]
2025-06-18 23:56:34,860 INFO: 应付账款ID: 11 [in C:\studentscmssp\app\routes\financial\payables.py:513]
2025-06-18 23:56:34,860 INFO: 财务凭证ID: 40 [in C:\studentscmssp\app\routes\financial\payables.py:514]
2025-06-18 23:56:35,536 INFO: === 开始生成应付账款 === [in C:\studentscmssp\app\routes\financial\payables.py:299]
2025-06-18 23:56:35,552 INFO: 请求数据: {'stock_in_id': 101} [in C:\studentscmssp\app\routes\financial\payables.py:303]
2025-06-18 23:56:35,552 INFO: 入库单ID: 101 [in C:\studentscmssp\app\routes\financial\payables.py:306]
2025-06-18 23:56:35,552 INFO: 查询入库单: ID=101, area_id=44 [in C:\studentscmssp\app\routes\financial\payables.py:313]
2025-06-18 23:56:35,552 INFO: 找到入库单: RK20250614224624, 状态: 已入库, 总金额: 12376.00 [in C:\studentscmssp\app\routes\financial\payables.py:323]
2025-06-18 23:56:35,552 INFO: 检查是否已生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:326]
2025-06-18 23:56:35,552 INFO: 检查入库单状态: 已入库 [in C:\studentscmssp\app\routes\financial\payables.py:333]
2025-06-18 23:56:35,552 INFO: 开始生成应付账款... [in C:\studentscmssp\app\routes\financial\payables.py:339]
2025-06-18 23:56:35,552 INFO: 应付账款编号前缀: AP20250618 [in C:\studentscmssp\app\routes\financial\payables.py:344]
2025-06-18 23:56:35,552 INFO: 基于最后编号 AP20250618002 生成新编号: AP20250618003 [in C:\studentscmssp\app\routes\financial\payables.py:354]
2025-06-18 23:56:35,552 INFO: 开始查询会计科目... [in C:\studentscmssp\app\routes\financial\payables.py:399]
2025-06-18 23:56:35,552 INFO: 查询原材料科目 (1201)... [in C:\studentscmssp\app\routes\financial\payables.py:401]
2025-06-18 23:56:35,552 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\studentscmssp\app\routes\financial\payables.py:411]
2025-06-18 23:56:35,552 INFO: 查询应付账款科目 (2001)... [in C:\studentscmssp\app\routes\financial\payables.py:415]
2025-06-18 23:56:35,552 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\studentscmssp\app\routes\financial\payables.py:425]
2025-06-18 23:56:35,552 INFO: 准备凭证明细SQL... [in C:\studentscmssp\app\routes\financial\payables.py:437]
2025-06-18 23:56:35,552 INFO: 准备更新入库单SQL... [in C:\studentscmssp\app\routes\financial\payables.py:440]
2025-06-18 23:56:35,552 INFO: 开始执行数据库事务... [in C:\studentscmssp\app\routes\financial\payables.py:443]
2025-06-18 23:56:35,552 INFO: 执行应付账款SQL... [in C:\studentscmssp\app\routes\financial\payables.py:447]
2025-06-18 23:56:35,552 INFO: 应付账款创建成功，ID: 12 [in C:\studentscmssp\app\routes\financial\payables.py:457]
2025-06-18 23:56:35,552 INFO: 执行财务凭证SQL... [in C:\studentscmssp\app\routes\financial\payables.py:460]
2025-06-18 23:56:35,552 INFO: 财务凭证创建成功，ID: 41 [in C:\studentscmssp\app\routes\financial\payables.py:470]
2025-06-18 23:56:35,552 INFO: 创建凭证明细... [in C:\studentscmssp\app\routes\financial\payables.py:474]
2025-06-18 23:56:35,567 INFO: 借方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:484]
2025-06-18 23:56:35,567 INFO: 贷方明细创建成功 [in C:\studentscmssp\app\routes\financial\payables.py:494]
2025-06-18 23:56:35,567 INFO: 更新入库单关联信息... [in C:\studentscmssp\app\routes\financial\payables.py:499]
2025-06-18 23:56:35,567 INFO: 入库单更新成功 [in C:\studentscmssp\app\routes\financial\payables.py:506]
2025-06-18 23:56:35,567 INFO: 事务提交成功 [in C:\studentscmssp\app\routes\financial\payables.py:510]
2025-06-18 23:56:35,567 INFO: === 应付账款生成成功 === [in C:\studentscmssp\app\routes\financial\payables.py:512]
2025-06-18 23:56:35,567 INFO: 应付账款ID: 12 [in C:\studentscmssp\app\routes\financial\payables.py:513]
2025-06-18 23:56:35,567 INFO: 财务凭证ID: 41 [in C:\studentscmssp\app\routes\financial\payables.py:514]
2025-06-19 10:31:50,559 ERROR: 创建财务凭证失败: local variable 'date' referenced before assignment [in C:\studentscmssp\app\routes\financial\vouchers.py:266]
2025-06-19 13:27:10,825 INFO: 自动填充菜单数据: 早餐=None, 午餐=None, 晚餐=None [in C:\studentscmssp\app\services\daily_management_service.py:341]
2025-06-19 13:27:10,841 INFO: 自动创建日志成功: 用户=guest_demo, 学校=海淀区中关村第一小学, 日期=2025-06-19 [in C:\studentscmssp\app\services\auto_daily_log_service.py:66]
2025-06-19 13:27:31,281 INFO: 取消发布周菜单成功: id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:571]
2025-06-19 13:29:38,553 INFO: 获取副表数据用于补全主表: weekly_menu_id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:342]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 刨花青笋, ID=390 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 炒包菜, ID=391 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 洞庭蚕豆, ID=383 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 红烧肉, ID=400 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 长豇豆烧茄子, ID=380 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=397 [in C:\studentscmssp\app\services\weekly_menu_service.py:368]
2025-06-19 13:29:38,568 INFO: 副表数据映射构建完成: 2 天, 24 个菜品 [in C:\studentscmssp\app\services\weekly_menu_service.py:370]
2025-06-19 13:29:38,568 WARNING: 跳过无效日期: 1 [in C:\studentscmssp\app\services\weekly_menu_service.py:380]
2025-06-19 13:29:38,568 WARNING: 跳过无效日期: 2 [in C:\studentscmssp\app\services\weekly_menu_service.py:380]
2025-06-19 13:29:38,568 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 包子 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,568 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,568 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 刨花青笋 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 刨花青笋, ID=390 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 炒包菜 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 炒包菜, ID=391 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 洞庭蚕豆 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 洞庭蚕豆, ID=383 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 红烧肉 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 红烧肉, ID=400 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 包子 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 红薯米饭 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 炒包菜 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,578 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,578 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,584 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 麻婆豆腐 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,584 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,584 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 长豇豆烧茄子 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,584 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 长豇豆烧茄子, ID=380 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,584 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤 [in C:\studentscmssp\app\services\weekly_menu_service.py:389]
2025-06-19 13:29:38,584 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=397 [in C:\studentscmssp\app\services\weekly_menu_service.py:401]
2025-06-19 13:29:38,584 INFO: 主表数据补全完成，准备保存: 总菜品数=100, 已补全=100, 未补全=0 [in C:\studentscmssp\app\services\weekly_menu_service.py:419]
2025-06-19 13:29:38,584 INFO: 删除现有菜单食谱(主表): weekly_menu_id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:428]
2025-06-19 13:29:38,584 INFO: 删除现有菜单食谱(副表): weekly_menu_id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:437]
2025-06-19 13:29:38,584 WARNING: 跳过无效日期: 1 [in C:\studentscmssp\app\services\weekly_menu_service.py:446]
2025-06-19 13:29:38,584 WARNING: 跳过无效日期: 2 [in C:\studentscmssp\app\services\weekly_menu_service.py:446]
2025-06-19 13:29:38,678 INFO: 保存周菜单成功(主表和副表): id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:500]
2025-06-19 13:29:38,678 INFO: 菜单缓存已清理 [in C:\studentscmssp\app\services\weekly_menu_service.py:20]
2025-06-19 13:29:53,828 INFO: 发布周菜单成功: id=42 [in C:\studentscmssp\app\services\weekly_menu_service.py:537]
2025-06-19 13:29:53,844 INFO: 更新了日期 2025-06-16 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:29:53,844 INFO: 更新了日期 2025-06-17 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:29:53,860 INFO: 更新了日期 2025-06-19 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:29:53,876 INFO: 更新了日期 2025-06-20 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:29:53,876 INFO: 更新了日期 2025-06-21 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:29:53,891 INFO: 更新了日期 2025-06-22 的工作日志菜单 [in C:\studentscmssp\app\services\menu_sync_service.py:150]
2025-06-19 13:30:35,406 INFO: 成功生成二维码，数据: http://xiaoyuanst.com/food-trace/qr/B20250614286e56/10... [in C:\studentscmssp\app\routes\stock_in.py:2986]
2025-06-19 13:32:22,094 ERROR: 查看入库食材详情失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'menu_plans' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT
                cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
                a.name as area_name, a.id as area_id
            FROM
                stock_out_items soi
            JOIN
                stock_outs so ON soi.stock_out_id = so.id
            JOIN
                consumption_plans cp ON so.consumption_plan_id = cp.id
            JOIN
                menu_plans mp ON cp.menu_plan_id = mp.id
            JOIN
                administrative_areas a ON mp.area_id = a.id
            WHERE
                soi.batch_number = ?
            ORDER BY
                cp.consumption_date DESC
        ]
[parameters: ('B20250614ffcd54',)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\studentscmssp\app\routes\stock_in_detail.py:124]
2025-06-19 13:35:04,797 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:35:04,798 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:35:04,800 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:35:04,801 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:35:43,255 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:35:43,255 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:35:43,257 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:35:43,257 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:35:54,865 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:35:54,866 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:35:54,867 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:35:54,869 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:36:14,273 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:36:14,274 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:36:14,275 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:36:14,275 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:36:27,836 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:36:27,836 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:36:27,848 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:36:27,849 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:36:30,758 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:36:30,759 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:36:30,761 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:36:30,761 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:36:34,872 INFO: 当前用户: guest_demo [in C:\studentscmssp\app\routes\consumption_plan.py:94]
2025-06-19 13:36:34,873 INFO: 用户区域ID: 44 [in C:\studentscmssp\app\routes\consumption_plan.py:95]
2025-06-19 13:36:34,876 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\studentscmssp\app\routes\consumption_plan.py:96]
2025-06-19 13:36:34,876 INFO: 是否管理员: 0 [in C:\studentscmssp\app\routes\consumption_plan.py:97]
2025-06-19 13:36:58,225 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\studentscmssp\app\routes\inventory.py:151]
2025-06-19 13:36:58,392 ERROR: Exception on /inventory [GET] [in C:\studentscmssp\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\studentscmssp\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\studentscmssp\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\studentscmssp\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\studentscmssp\app\routes\inventory.py", line 264, in index
    return render_template('inventory/index.html',
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\studentscmssp\app\templates\inventory\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\studentscmssp\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\studentscmssp\app\templates\inventory\index.html", line 321, in block 'content'
    <small>{{ inventory.warehouse.name }}</small>
  File "C:\studentscmssp\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
2025-06-19 13:37:01,063 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\studentscmssp\app\routes\inventory.py:151]
2025-06-19 13:37:01,146 ERROR: Exception on /inventory [GET] [in C:\studentscmssp\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\studentscmssp\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\studentscmssp\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\studentscmssp\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\studentscmssp\app\routes\inventory.py", line 264, in index
    return render_template('inventory/index.html',
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\studentscmssp\app\templates\inventory\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\studentscmssp\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\studentscmssp\app\templates\inventory\index.html", line 321, in block 'content'
    <small>{{ inventory.warehouse.name }}</small>
  File "C:\studentscmssp\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
2025-06-19 13:37:02,714 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\studentscmssp\app\routes\inventory.py:151]
2025-06-19 13:37:02,803 ERROR: Exception on /inventory [GET] [in C:\studentscmssp\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\studentscmssp\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\studentscmssp\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\studentscmssp\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\studentscmssp\app\routes\inventory.py", line 264, in index
    return render_template('inventory/index.html',
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\studentscmssp\app\templates\inventory\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\studentscmssp\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\studentscmssp\app\templates\inventory\index.html", line 321, in block 'content'
    <small>{{ inventory.warehouse.name }}</small>
  File "C:\studentscmssp\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
2025-06-19 13:38:11,822 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\studentscmssp\app\routes\inventory.py:151]
2025-06-19 13:38:11,901 ERROR: Exception on /inventory [GET] [in C:\studentscmssp\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\studentscmssp\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\studentscmssp\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\studentscmssp\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\studentscmssp\app\routes\inventory.py", line 264, in index
    return render_template('inventory/index.html',
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\studentscmssp\app\templates\inventory\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\studentscmssp\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\studentscmssp\app\templates\inventory\index.html", line 321, in block 'content'
    <small>{{ inventory.warehouse.name }}</small>
  File "C:\studentscmssp\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
2025-06-19 13:38:21,194 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\studentscmssp\app\routes\inventory.py:151]
2025-06-19 13:38:21,273 ERROR: Exception on /inventory [GET] [in C:\studentscmssp\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\studentscmssp\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\studentscmssp\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\studentscmssp\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\studentscmssp\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\studentscmssp\app\routes\inventory.py", line 264, in index
    return render_template('inventory/index.html',
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\studentscmssp\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\studentscmssp\app\templates\inventory\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\studentscmssp\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\studentscmssp\app\templates\inventory\index.html", line 321, in block 'content'
    <small>{{ inventory.warehouse.name }}</small>
  File "C:\studentscmssp\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\studentscmssp\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
2025-06-19 13:39:25,756 ERROR: 查看入库食材详情失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'menu_plans' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT
                cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
                a.name as area_name, a.id as area_id
            FROM
                stock_out_items soi
            JOIN
                stock_outs so ON soi.stock_out_id = so.id
            JOIN
                consumption_plans cp ON so.consumption_plan_id = cp.id
            JOIN
                menu_plans mp ON cp.menu_plan_id = mp.id
            JOIN
                administrative_areas a ON mp.area_id = a.id
            WHERE
                soi.batch_number = ?
            ORDER BY
                cp.consumption_date DESC
        ]
[parameters: ('B20250614286e56',)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\studentscmssp\app\routes\stock_in_detail.py:124]
