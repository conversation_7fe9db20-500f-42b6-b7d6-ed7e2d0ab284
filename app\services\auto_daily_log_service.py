"""
自动日常管理日志服务

当用户登录时自动生成当天的日常管理日志
"""

from datetime import datetime, date
from flask import current_app
from flask_login import current_user
from app import db
from app.models_daily_management import DailyLog
from app.services.daily_management_service import DailyLogService
from sqlalchemy import text


class AutoDailyLogService:
    """自动日常管理日志服务类"""

    @staticmethod
    def auto_create_today_log(user):
        """
        为用户自动创建今天的日常管理日志
        
        Args:
            user: 用户对象
            
        Returns:
            tuple: (success: bool, message: str, log: DailyLog or None)
        """
        try:
            # 检查用户是否有关联的学校/区域
            user_area = user.get_current_area()
            if not user_area:
                return False, "用户没有关联到任何学校，无法创建日志", None
            
            # 获取今天的日期
            today = date.today()
            
            # 检查今天是否已经有日志
            existing_log = DailyLog.query.filter_by(
                log_date=today,
                area_id=user_area.id
            ).first()
            
            if existing_log:
                # 日志已存在，返回现有日志
                return True, f"今日日志已存在", existing_log
            
            # 创建新的日志
            log_data = {
                'log_date': today.strftime('%Y-%m-%d'),
                'area_id': user_area.id,
                'manager': user.real_name or user.username,
                'student_count': 0,
                'teacher_count': 0,
                'other_count': 0,
                'created_by': user.id,
                'weather': AutoDailyLogService._get_default_weather(),
                'operation_summary': f'由系统自动创建于用户登录时 - {datetime.now().strftime("%Y-%m-%d %H:%M")}'
            }
            
            # 使用 DailyLogService 创建日志
            new_log = DailyLogService.create_daily_log(log_data)
            
            if new_log:
                current_app.logger.info(f"自动创建日志成功: 用户={user.username}, 学校={user_area.name}, 日期={today}")
                return True, f"已自动创建{user_area.name}今日工作日志", new_log
            else:
                current_app.logger.error(f"自动创建日志失败: 用户={user.username}, 学校={user_area.name}")
                return False, "自动创建日志失败", None
                
        except Exception as e:
            current_app.logger.error(f"自动创建日志异常: 用户={user.username if user else 'Unknown'}, 错误={str(e)}")
            return False, f"自动创建日志时发生错误: {str(e)}", None

    @staticmethod
    def auto_create_logs_for_all_areas():
        """
        为所有活跃的学校/区域自动创建今天的日志
        
        Returns:
            dict: 创建结果统计
        """
        try:
            from app.models import AdministrativeArea
            
            # 获取所有活跃的学校/区域
            active_areas = AdministrativeArea.query.filter_by(is_active=True).all()
            
            today = date.today()
            created_count = 0
            existing_count = 0
            failed_count = 0
            results = []
            
            for area in active_areas:
                try:
                    # 检查是否已有今日日志
                    existing_log = DailyLog.query.filter_by(
                        log_date=today,
                        area_id=area.id
                    ).first()
                    
                    if existing_log:
                        existing_count += 1
                        results.append({
                            'area_name': area.name,
                            'status': 'existing',
                            'message': '日志已存在'
                        })
                        continue
                    
                    # 创建新日志
                    log_data = {
                        'log_date': today.strftime('%Y-%m-%d'),
                        'area_id': area.id,
                        'manager': '系统自动创建',
                        'student_count': 0,
                        'teacher_count': 0,
                        'other_count': 0,
                        'created_by': 1,  # 系统用户ID
                        'weather': AutoDailyLogService._get_default_weather(),
                        'operation_summary': f'系统自动创建于 {datetime.now().strftime("%Y-%m-%d %H:%M")}'
                    }
                    
                    new_log = DailyLogService.create_daily_log(log_data)
                    
                    if new_log:
                        created_count += 1
                        results.append({
                            'area_name': area.name,
                            'status': 'created',
                            'message': '日志创建成功'
                        })
                    else:
                        failed_count += 1
                        results.append({
                            'area_name': area.name,
                            'status': 'failed',
                            'message': '日志创建失败'
                        })
                        
                except Exception as e:
                    failed_count += 1
                    results.append({
                        'area_name': area.name,
                        'status': 'error',
                        'message': f'创建时发生错误: {str(e)}'
                    })
            
            summary = {
                'total_areas': len(active_areas),
                'created_count': created_count,
                'existing_count': existing_count,
                'failed_count': failed_count,
                'results': results
            }
            
            current_app.logger.info(f"批量创建日志完成: 总计={len(active_areas)}, 新建={created_count}, 已存在={existing_count}, 失败={failed_count}")
            
            return summary
            
        except Exception as e:
            current_app.logger.error(f"批量创建日志异常: {str(e)}")
            return {
                'total_areas': 0,
                'created_count': 0,
                'existing_count': 0,
                'failed_count': 0,
                'error': str(e),
                'results': []
            }

    @staticmethod
    def _get_default_weather():
        """
        获取默认天气信息
        
        Returns:
            str: 默认天气描述
        """
        # 可以根据季节或其他逻辑返回默认天气
        # 这里简单返回"晴"
        return "晴"

    @staticmethod
    def check_and_create_missing_logs(days_back=7):
        """
        检查并创建缺失的日志（过去几天）
        
        Args:
            days_back: 检查过去多少天的日志
            
        Returns:
            dict: 检查和创建结果
        """
        try:
            from app.models import AdministrativeArea
            from datetime import timedelta
            
            # 获取所有活跃的学校/区域
            active_areas = AdministrativeArea.query.filter_by(is_active=True).all()
            
            created_logs = []
            existing_logs = []
            
            # 检查过去几天的日志
            for i in range(days_back):
                check_date = date.today() - timedelta(days=i)
                
                for area in active_areas:
                    # 检查该日期是否有日志
                    existing_log = DailyLog.query.filter_by(
                        log_date=check_date,
                        area_id=area.id
                    ).first()
                    
                    if existing_log:
                        existing_logs.append({
                            'date': check_date.strftime('%Y-%m-%d'),
                            'area_name': area.name,
                            'log_id': existing_log.id
                        })
                    else:
                        # 创建缺失的日志
                        try:
                            log_data = {
                                'log_date': check_date.strftime('%Y-%m-%d'),
                                'area_id': area.id,
                                'manager': '系统补充创建',
                                'student_count': 0,
                                'teacher_count': 0,
                                'other_count': 0,
                                'created_by': 1,  # 系统用户ID
                                'weather': AutoDailyLogService._get_default_weather(),
                                'operation_summary': f'系统补充创建于 {datetime.now().strftime("%Y-%m-%d %H:%M")}'
                            }
                            
                            new_log = DailyLogService.create_daily_log(log_data)
                            
                            if new_log:
                                created_logs.append({
                                    'date': check_date.strftime('%Y-%m-%d'),
                                    'area_name': area.name,
                                    'log_id': new_log.id
                                })
                                
                        except Exception as e:
                            current_app.logger.error(f"补充创建日志失败: 日期={check_date}, 学校={area.name}, 错误={str(e)}")
            
            result = {
                'days_checked': days_back,
                'areas_checked': len(active_areas),
                'created_count': len(created_logs),
                'existing_count': len(existing_logs),
                'created_logs': created_logs,
                'existing_logs': existing_logs
            }
            
            current_app.logger.info(f"检查缺失日志完成: 检查{days_back}天, {len(active_areas)}个区域, 新建{len(created_logs)}个日志")
            
            return result
            
        except Exception as e:
            current_app.logger.error(f"检查缺失日志异常: {str(e)}")
            return {
                'days_checked': days_back,
                'areas_checked': 0,
                'created_count': 0,
                'existing_count': 0,
                'error': str(e),
                'created_logs': [],
                'existing_logs': []
            }
