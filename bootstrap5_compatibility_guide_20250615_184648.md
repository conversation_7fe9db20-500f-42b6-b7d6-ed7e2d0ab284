================================================================================
🔧 Bootstrap 5 + jQuery 兼容性指南
================================================================================

📋 主要兼容性问题:
1. Bootstrap 5移除了jQuery依赖
2. data-*属性命名变更
3. 某些jQuery插件可能不兼容

🔧 解决方案:

### 1. 保持jQuery支持
```html
<!-- 确保jQuery在Bootstrap之前加载 -->
<script src="/static/vendor/jquery/jquery.min.js"></script>
<script src="/static/bootstrap/js/bootstrap.bundle.min.js"></script>
```

### 2. 更新data属性
```html
<!-- Bootstrap 4 -->
<button data-toggle="modal" data-target="#myModal">打开</button>

<!-- Bootstrap 5 -->
<button data-bs-toggle="modal" data-bs-target="#myModal">打开</button>
```

### 3. jQuery插件兼容性
```javascript
// 确保jQuery插件在Bootstrap加载后初始化
$(document).ready(function() {
    // DataTables初始化
    $('#myTable').DataTable();
    
    // Select2初始化
    $('.select2').select2();
});
```

### 4. 事件处理兼容性
```javascript
// Bootstrap 5事件命名
$('#myModal').on('show.bs.modal', function() {
    console.log('模态框显示');
});
```

🎯 最佳实践:
1. 渐进式迁移：先确保基本功能正常
2. 测试驱动：每个修改都要测试
3. 文档更新：更新开发文档
4. 团队培训：确保团队了解变更

================================================================================