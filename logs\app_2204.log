2025-06-19 21:03:47,924 INFO: 应用启动 - PID: 2204 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 21:03:51,840 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 21:03:53,324 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 21:04:01,886 INFO: 查看库存详情: ID=81, 批次号=B20250613e30b2c [in C:\StudentsCMSSP\app\routes\inventory.py:451]
2025-06-19 21:04:32,403 INFO: 成功生成二维码，数据: http://xiaoyuanst.com/food-trace/recipe/395/2025-06-19/%E5%8D%88%E9%A4%90/44... [in C:\StudentsCMSSP\app\routes\stock_in.py:2986]
2025-06-19 21:07:37,925 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-19 21:07:37,926 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-19 21:07:37,936 INFO: 找到有发生额的科目数量: 4 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
2025-06-19 21:07:37,936 INFO: 生成明细账: subject_id=206 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-19 21:07:37,953 INFO: 生成明细账: subject_id=207 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-19 21:07:37,964 INFO: 生成明细账: subject_id=208 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-19 21:07:37,978 INFO: 生成明细账: subject_id=220 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-19 21:07:37,988 INFO: 批量生成明细账完成: {'success': True, 'message': '批量生成完成，成功 4 个科目', 'results': [{'subject_id': 206, 'result': {'success': True, 'message': '成功生成 1201-原材料 2025年6月明细账', 'records_count': 11, 'opening_balance': 0.0, 'closing_balance': 174800.59999999998, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 207, 'result': {'success': True, 'message': '成功生成 120101-蔬菜类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 19574.449999999997, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 208, 'result': {'success': True, 'message': '成功生成 120102-肉类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 56871.15, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 220, 'result': {'success': True, 'message': '成功生成 2001-应付账款 2025年6月明细账', 'records_count': 11, 'opening_balance': 0.0, 'closing_balance': -251246.19999999998, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}]} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:186]
2025-06-19 21:10:32,940 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-19 21:15:57,053 ERROR: 创建财务凭证失败: local variable 'date' referenced before assignment [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:266]
