/* 用友财务软件专业主题样式 v2.0 */
:root {
    /* 用友企业级色彩体系 */
    --uf-primary: #0066cc;
    --uf-primary-dark: #004499;
    --uf-primary-light: #3388dd;
    --uf-success: #1e7e34;
    --uf-warning: #e0a800;
    --uf-danger: #bd2130;
    --uf-info: #138496;
    --uf-light: #f8f9fa;
    --uf-dark: #343a40;
    --uf-muted: #6c757d;
    --uf-border: #c0c0c0;
    --uf-grid-border: #999999;
    --uf-header-bg: #e6f2ff;
    --uf-toolbar-bg: #f0f8ff;
    --uf-row-hover: #f0f8ff;
    --uf-selected: #cce5ff;
    --uf-input-focus: #fffef7;
    --uf-disabled-bg: #f5f5f5;
    --uf-disabled-text: #999999;
    
    /* 用友标准字体 */
    --uf-font-family: 'Microsoft YaHei', 'SimSun', '宋体', Arial, sans-serif;
    --uf-font-size: 13px;
    --uf-font-size-large: 14px;
    --uf-font-size-small: 13px;
    --uf-line-height: 1.4;
    
    /* 用友专业尺寸 */
    --uf-border-radius: 1px;
    --uf-btn-height: 36px;
    --uf-btn-height-sm: 32px;
    --uf-btn-height-lg: 44px;
    --uf-table-row-height: 36px;
    --uf-table-header-height: 36px;
    --uf-card-padding: 12px;
    --uf-form-label-width: 90px;
    
    /* 用友专业阴影和过渡 */
    --uf-box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    --uf-box-shadow-hover: 0 2px 4px rgba(0,102,204,0.2);
    --uf-box-shadow-focus: 0 0 0 2px rgba(0,102,204,0.3);
    --uf-transition: all 0.15s ease;
}

/* 用友财务软件基础样式 */
body {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: #333;
    background-color: var(--uf-light);
    margin: 0;
    padding: 0;
}

/* 用友专业按钮样式 */
.uf-btn {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: 400;
    padding: 0 16px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    text-decoration: none;
    transition: var(--uf-transition);
    min-height: var(--uf-btn-height);
    box-shadow: var(--uf-box-shadow);
    vertical-align: middle;
    user-select: none;
    -webkit-user-select: none;
    line-height: 1.4;
}

.uf-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-btn:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    transform: translateY(1px);
}

.uf-btn:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
}

.uf-btn:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    border-color: #ddd;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.7;
}

/* 用友按钮颜色变体 - 统一灰白色 */
.uf-btn-primary {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

.uf-btn-primary:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-btn-primary:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
}

.uf-btn-success {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

.uf-btn-warning {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

.uf-btn-danger {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

.uf-btn-info {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

/* 用友按钮尺寸变体 */
.uf-btn-sm {
    font-size: var(--uf-font-size-small);
    padding: 0 12px;
    min-height: var(--uf-btn-height-sm);
}

.uf-btn-lg {
    font-size: var(--uf-font-size-large);
    padding: 0 20px;
    min-height: var(--uf-btn-height-lg);
    font-weight: 500;
}

/* 用友按钮组样式 */
.uf-btn-group {
    display: inline-flex;
    gap: 0;
    position: relative;
}

.uf-btn-group .uf-btn {
    border-radius: 0;
    margin-left: -1px;
}

.uf-btn-group .uf-btn:first-child {
    border-radius: var(--uf-border-radius) 0 0 var(--uf-border-radius);
    margin-left: 0;
}

.uf-btn-group .uf-btn:last-child {
    border-radius: 0 var(--uf-border-radius) var(--uf-border-radius) 0;
}

.uf-btn-group .uf-btn:only-child {
    border-radius: var(--uf-border-radius);
}

.uf-btn-group .uf-btn:hover:not(:disabled) {
    z-index: 1;
}

.uf-btn-group .uf-btn:focus:not(:disabled) {
    z-index: 2;
}

/* 用友专业表格样式 */
.uf-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--uf-font-size);
    background: white;
    font-family: var(--uf-font-family);
    border: 1px solid var(--uf-grid-border);
}

.uf-table th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    text-align: center;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: var(--uf-font-size);
    white-space: nowrap;
    height: var(--uf-table-header-height);
    vertical-align: middle;
    position: relative;
}

.uf-table th.sortable {
    cursor: pointer;
}

.uf-table th.sortable:hover {
    background: linear-gradient(to bottom, #e6f2ff 0%, #d9e8ff 100%);
}

.uf-table th.sort-asc::after {
    content: "↑";
    position: absolute;
    right: 4px;
    font-size: 10px;
}

.uf-table th.sort-desc::after {
    content: "↓";
    position: absolute;
    right: 4px;
    font-size: 10px;
}

.uf-table td {
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    vertical-align: middle;
    font-size: var(--uf-font-size);
    height: var(--uf-table-row-height);
    line-height: 1.4;
}

.uf-table tbody tr:hover {
    background: var(--uf-row-hover);
}

.uf-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.uf-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover);
}

.uf-table tbody tr.selected {
    background: var(--uf-selected);
}

.uf-table .empty-row td {
    text-align: center;
    color: var(--uf-muted);
    padding: 16px;
}

/* 用友表格对齐样式 */
.uf-table .text-right {
    text-align: right;
}

.uf-table .text-center {
    text-align: center;
}

.uf-table .text-left {
    text-align: left;
}

/* 用友金额列样式 */
.uf-amount-col {
    text-align: right;
    padding-right: 12px !important;
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: var(--uf-font-size);
    white-space: nowrap;
}

.uf-amount {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-weight: 500;
    font-size: var(--uf-font-size);
    text-align: right;
    color: #333;
    white-space: nowrap;
    letter-spacing: 0.5px;
}

.uf-currency {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    margin-right: 1px;
}

/* 用友表单样式 */
.uf-form-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.uf-form-group {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;
}

.uf-form-label {
    font-weight: 500;
    color: #333;
    font-size: var(--uf-font-size) !important;
    margin-right: 6px;
    min-width: var(--uf-form-label-width);
    text-align: right;
    white-space: nowrap;
}

.uf-form-control {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size) !important;
    padding: 6px 12px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #333;
    flex: 1;
    min-height: var(--uf-btn-height);
    line-height: 1.4;
}

/* 确保所有表单控件都使用正确的字体大小 */
input.uf-form-control,
select.uf-form-control,
textarea.uf-form-control {
    font-size: var(--uf-font-size) !important;
    font-family: var(--uf-font-family) !important;
}

.uf-form-control:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
    background: var(--uf-input-focus);
}

.uf-form-control:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    cursor: not-allowed;
}

.uf-form-control[readonly] {
    background: #f9f9f9;
    cursor: default;
}

.uf-form-hint {
    font-size: var(--uf-font-size-small);
    color: var(--uf-muted);
    margin-left: 8px;
}

.uf-form-error {
    font-size: var(--uf-font-size-small);
    color: var(--uf-danger);
    margin-left: 8px;
}

.uf-form-control.error {
    border-color: var(--uf-danger);
    box-shadow: 0 0 2px rgba(189, 33, 48, 0.3);
}

/* 用友卡片样式 */
.uf-card {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    margin-bottom: 8px;
    overflow: hidden;
}

.uf-card:hover {
    box-shadow: var(--uf-box-shadow-hover);
    border-color: var(--uf-primary);
}

.uf-card-header {
    background: var(--uf-header-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: 8px 12px;
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    color: var(--uf-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-card-header-title {
    display: flex;
    align-items: center;
    gap: 4px;
}

.uf-card-header-icon {
    color: var(--uf-primary);
}

.uf-card-header-actions {
    display: flex;
    gap: 4px;
}

.uf-card-body {
    padding: var(--uf-card-padding);
}

.uf-card-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 用友专业分页样式 */
.uf-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    padding: 8px;
    background: #f8f9fa;
    border-top: 1px solid var(--uf-grid-border);
}

.uf-page-item {
    display: inline-block;
}

.uf-page-link {
    display: block;
    padding: 2px 6px;
    font-size: var(--uf-font-size);
    color: var(--uf-primary);
    text-decoration: none;
    border: 1px solid var(--uf-border);
    background: white;
    border-radius: var(--uf-border-radius);
    min-width: 20px;
    text-align: center;
    transition: var(--uf-transition);
}

.uf-page-link:hover {
    background: var(--uf-row-hover);
    border-color: var(--uf-primary);
    text-decoration: none;
}

.uf-page-item.active .uf-page-link {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary);
}

.uf-page-item.disabled .uf-page-link {
    color: #999;
    background: #f5f5f5;
    cursor: not-allowed;
}

.uf-pagination-info {
    margin-left: 16px;
    color: var(--uf-muted);
}

/* 用友状态标签样式 */
.uf-status {
    display: inline-block;
    padding: 1px 4px;
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--uf-border-radius);
    border: 1px solid;
    white-space: nowrap;
}

.uf-status-approved {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.uf-status-pending {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.uf-status-rejected {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* 用友代码样式 */
.uf-code {
    font-family: 'Courier New', monospace;
    background: #f0f8ff;
    padding: 1px 3px;
    border-radius: var(--uf-border-radius);
    font-size: 12px;
    border: 1px solid #e6f2ff;
}

.uf-code-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.uf-code-success {
    background: #d4edda;
    border-color: #c3e6cb;
}

/* 用友空状态样式 */
.uf-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #fafafa;
    border: 1px dashed var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
    display: block;
}

.uf-empty-state p {
    margin: 0;
    font-size: var(--uf-font-size);
    color: #999;
}

/* 用友工具栏样式 */
.uf-toolbar {
    background: var(--uf-toolbar-bg);
    border: 1px solid var(--uf-border);
    padding: 6px 8px;
    margin-bottom: 8px;
    border-radius: var(--uf-border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--uf-font-size);
}

.uf-toolbar-left,
.uf-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-toolbar-title {
    font-weight: 600;
    color: var(--uf-primary);
    margin-right: 8px;
}

/* 用友面包屑导航 */
.uf-breadcrumb {
    background: var(--uf-light);
    border: 1px solid var(--uf-border);
    padding: 6px 12px;
    margin-bottom: 8px;
    border-radius: var(--uf-border-radius);
    font-size: var(--uf-font-size);
}

.uf-breadcrumb-item {
    display: inline;
    color: #666;
}

.uf-breadcrumb-item + .uf-breadcrumb-item::before {
    content: " > ";
    color: #999;
    margin: 0 4px;
}

.uf-breadcrumb-item.active {
    color: var(--uf-primary);
    font-weight: 500;
}

.uf-breadcrumb-item a {
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-breadcrumb-item a:hover {
    text-decoration: underline;
}

/* 用友专业链接样式 */
a {
    color: var(--uf-primary);
    text-decoration: none;
    transition: var(--uf-transition);
    font-size: inherit;
}

a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
    border-radius: 1px;
}

a:active {
    color: var(--uf-primary-dark);
}

/* 用友链接变体 */
.uf-link {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: var(--uf-font-size);
    transition: var(--uf-transition);
    cursor: pointer;
}

.uf-link:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

.uf-link:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
    border-radius: 1px;
}

.uf-link-success {
    color: var(--uf-success);
}

.uf-link-success:hover {
    color: #155724;
}

.uf-link-warning {
    color: var(--uf-warning);
}

.uf-link-warning:hover {
    color: #b8860b;
}

.uf-link-danger {
    color: var(--uf-danger);
}

.uf-link-danger:hover {
    color: #a71e2a;
}

.uf-link-info {
    color: var(--uf-info);
}

.uf-link-info:hover {
    color: #0f5460;
}

/* 用友链接尺寸 */
.uf-link-sm {
    font-size: var(--uf-font-size-small);
}

.uf-link-lg {
    font-size: var(--uf-font-size-large);
    font-weight: 500;
}

/* 用友禁用链接 */
.uf-link:disabled,
.uf-link.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: none;
}

.uf-link:disabled:hover,
.uf-link.disabled:hover {
    color: #999;
    text-decoration: none;
}

/* 用友表格内链接 */
.uf-table a {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: inherit;
}

.uf-table a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

/* 用友导航链接 */
.uf-nav-link {
    color: #333;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: var(--uf-border-radius);
    transition: var(--uf-transition);
    font-size: var(--uf-font-size);
}

.uf-nav-link:hover {
    background: var(--uf-row-hover);
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-nav-link.active {
    background: var(--uf-primary);
    color: white;
}

.uf-nav-link.active:hover {
    background: var(--uf-primary-dark);
    color: white;
}

/* 用友按钮式链接 */
.uf-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 3px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    text-decoration: none;
    font-size: var(--uf-font-size);
    transition: var(--uf-transition);
    min-height: var(--uf-btn-height);
    box-shadow: var(--uf-box-shadow);
}

.uf-link-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-link-btn:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* 用友文本链接（无下划线） */
.uf-link-text {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: var(--uf-font-size);
    cursor: pointer;
    transition: var(--uf-transition);
}

.uf-link-text:hover {
    color: var(--uf-primary-dark);
    text-decoration: none;
}

/* 用友外部链接样式 */
.uf-link-external::after {
    content: " ↗";
    font-size: var(--uf-font-size-small);
    color: #999;
    margin-left: 2px;
}

/* 用友下载链接样式 */
.uf-link-download::before {
    content: "⬇ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-success);
    margin-right: 2px;
}

/* 用友邮件链接样式 */
.uf-link-email::before {
    content: "✉ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-info);
    margin-right: 2px;
}

/* 用友电话链接样式 */
.uf-link-phone::before {
    content: "☎ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-warning);
    margin-right: 2px;
}

/* ========================================
   用友财务专业业务样式
   ======================================== */

/* 财务金额显示样式 */
.uf-financial-amount {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-weight: 600;
    font-size: 12px;
    text-align: right;
    color: #000;
    white-space: nowrap;
    letter-spacing: 0.3px;
    padding-right: 8px;
}

.uf-financial-amount.positive {
    color: #000;
}

.uf-financial-amount.negative {
    color: #d32f2f;
}

.uf-financial-amount.zero {
    color: #666;
}

.uf-financial-amount.large {
    font-size: 14px;
    font-weight: 700;
}

.uf-financial-amount.small {
    font-size: 11px;
}

/* 财务金额输入框 */
.uf-amount-input {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 12px;
    text-align: right;
    padding: 3px 8px 3px 5px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #000;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.uf-amount-input:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
    background: var(--uf-input-focus);
}

.uf-amount-input.error {
    border-color: var(--uf-danger);
    background: #fff8f8;
}

.uf-amount-input::-webkit-inner-spin-button,
.uf-amount-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.uf-amount-input[type="number"] {
    -moz-appearance: textfield;
}

/* 财务科目代码样式 */
.uf-subject-code {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
    background: #f0f8ff;
    padding: 1px 4px;
    border-radius: 2px;
    border: 1px solid #e6f2ff;
    white-space: nowrap;
}

/* 财务科目名称样式 */
.uf-subject-name {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

/* 财务凭证号样式 */
.uf-voucher-number {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
    background: #f0f8ff;
    padding: 2px 6px;
    border-radius: 2px;
    border: 1px solid #e6f2ff;
    white-space: nowrap;
}

/* 财务日期样式 */
.uf-financial-date {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

/* 财务摘要样式 */
.uf-financial-summary {
    font-size: 13px;
    color: #333;
    line-height: 1.4;
    max-width: 200px;
    word-wrap: break-word;
}

/* 财务状态标签 */
.uf-financial-status {
    display: inline-block;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 2px;
    border: 1px solid;
    white-space: nowrap;
    text-align: center;
    min-width: 50px;
}

.uf-financial-status.draft {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.uf-financial-status.approved {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.uf-financial-status.posted {
    background: #cce5ff;
    color: #004499;
    border-color: #99ccff;
}

.uf-financial-status.cancelled {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.uf-financial-status.pending {
    background: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

.uf-financial-status.verified {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* 财务表格专用样式 */
.uf-financial-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--uf-font-size);
    background: white;
    font-family: var(--uf-font-family);
    border: 1px solid var(--uf-grid-border);
}

.uf-financial-table th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    text-align: center;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: var(--uf-font-size);
    white-space: nowrap;
    height: var(--uf-table-header-height);
}

.uf-financial-table td {
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    vertical-align: middle;
    font-size: var(--uf-font-size);
    height: var(--uf-table-row-height);
    line-height: 1.4;
}

.uf-financial-table tbody tr:hover {
    background: var(--uf-row-hover);
}

.uf-financial-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.uf-financial-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover);
}

.uf-financial-table tbody tr.selected {
    background: var(--uf-selected);
}

/* 财务表格列宽控制 */
.uf-financial-table .col-date {
    width: 100px;
}

.uf-financial-table .col-voucher {
    width: 120px;
}

.uf-financial-table .col-subject-code {
    width: 80px;
}

.uf-financial-table .col-subject-name {
    width: 150px;
}

.uf-financial-table .col-amount {
    width: 120px;
    text-align: right;
}

.uf-financial-table .col-summary {
    min-width: 200px;
}

.uf-financial-table .col-status {
    width: 80px;
    text-align: center;
}

.uf-financial-table .col-actions {
    width: 120px;
    text-align: center;
}

/* 财务按钮专用样式 */
.uf-financial-btn {
    font-family: var(--uf-font-family);
    font-size: 12px;
    font-weight: 500;
    padding: 4px 10px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    text-decoration: none;
    transition: var(--uf-transition);
    min-height: 24px;
    box-shadow: var(--uf-box-shadow);
    vertical-align: middle;
    white-space: nowrap;
}

.uf-financial-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-financial-btn.generate {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-financial-btn.generate:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-financial-btn.approve {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-financial-btn.approve:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-financial-btn.cancel {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-financial-btn.cancel:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-financial-btn.export {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-financial-btn.export:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-financial-btn.import {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-financial-btn.import:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

/* 财务表单专用样式 */
.uf-financial-form {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 16px;
    box-shadow: var(--uf-box-shadow);
}

.uf-financial-form-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 16px;
}

.uf-financial-form-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-financial-form-label {
    font-weight: 600;
    color: #333;
    font-size: 12px;
    min-width: 80px;
    text-align: right;
    white-space: nowrap;
}

.uf-financial-form-control {
    font-family: var(--uf-font-family);
    font-size: 12px;
    padding: 4px 6px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #333;
    min-height: 28px;
    line-height: 1.2;
}

.uf-financial-form-control:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
    background: var(--uf-input-focus);
}

.uf-financial-form-control:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    cursor: not-allowed;
}

.uf-financial-form-control[readonly] {
    background: #f9f9f9;
    cursor: default;
}

/* 财务汇总信息样式 */
.uf-financial-summary-card {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border: 1px solid var(--uf-primary);
    border-radius: var(--uf-border-radius);
    padding: 12px;
    margin-bottom: 16px;
}

.uf-financial-summary-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    margin-bottom: 8px;
}

.uf-financial-summary-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.uf-financial-summary-item {
    text-align: center;
}

.uf-financial-summary-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
}

.uf-financial-summary-value {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

/* 财务图标样式 */
.uf-financial-icon {
    font-size: 14px;
    color: var(--uf-primary);
    margin-right: 4px;
    vertical-align: middle;
}

.uf-financial-icon.success {
    color: var(--uf-success);
}

.uf-financial-icon.warning {
    color: var(--uf-warning);
}

.uf-financial-icon.danger {
    color: var(--uf-danger);
}

.uf-financial-icon.info {
    color: var(--uf-info);
}

/* 财务工具栏样式 */
.uf-financial-toolbar {
    background: var(--uf-toolbar-bg);
    border: 1px solid var(--uf-border);
    padding: 8px 12px;
    margin-bottom: 12px;
    border-radius: var(--uf-border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.uf-financial-toolbar-left,
.uf-financial-toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-financial-toolbar-title {
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 13px;
}

/* 财务分页样式 */
.uf-financial-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 12px;
    background: #f8f9fa;
    border-top: 1px solid var(--uf-grid-border);
    font-size: 12px;
}

.uf-financial-page-info {
    color: #666;
    margin: 0 16px;
    font-size: 12px;
}

/* 财务审批流程样式 */
.uf-approval-flow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    margin: 16px 0;
    border-top: 1px solid var(--uf-border);
    border-bottom: 1px solid var(--uf-border);
}

.uf-approval-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}

.uf-approval-step:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: #e9ecef;
    z-index: 0;
}

.uf-approval-step.active:not(:last-child)::after {
    background: var(--uf-primary);
}

.uf-approval-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    z-index: 1;
}

.uf-approval-step.active .uf-approval-icon {
    background: var(--uf-primary);
    color: white;
}

.uf-approval-step.completed .uf-approval-icon {
    background: var(--uf-success);
    color: white;
}

.uf-approval-step.rejected .uf-approval-icon {
    background: var(--uf-danger);
    color: white;
}

.uf-approval-label {
    margin-top: 8px;
    font-size: 11px;
    text-align: center;
    color: #6c757d;
}

.uf-approval-step.active .uf-approval-label,
.uf-approval-step.completed .uf-approval-label {
    color: var(--uf-primary);
    font-weight: 500;
}

.uf-approval-step.rejected .uf-approval-label {
    color: var(--uf-danger);
    font-weight: 500;
}

/* 财务数字键盘样式 */
.uf-numeric-keyboard {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    padding: 8px;
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    width: 200px;
    position: absolute;
    z-index: 10;
}

.uf-numeric-key {
    padding: 8px;
    font-size: 14px;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    cursor: pointer;
    user-select: none;
}

.uf-numeric-key:hover {
    background: var(--uf-row-hover);
    border-color: var(--uf-primary);
}

.uf-numeric-key:active {
    background: var(--uf-selected);
}

.uf-numeric-key.zero {
    grid-column: span 2;
}

.uf-numeric-key.clear,
.uf-numeric-key.backspace {
    background: #e9ecef;
    color: #6c757d;
}

.uf-numeric-key.enter {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary-dark);
}

/* 财务弹窗样式 */
.uf-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.uf-modal.show {
    opacity: 1;
    visibility: visible;
}

.uf-modal-content {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    width: 600px;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    transform: translateY(-20px);
    transition: transform 0.2s ease;
}

.uf-modal.show .uf-modal-content {
    transform: translateY(0);
}

.uf-modal-header {
    background: var(--uf-header-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-modal-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-modal-close {
    font-size: 16px;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.uf-modal-close:hover {
    color: #333;
}

.uf-modal-body {
    padding: 12px;
    overflow-y: auto;
    flex: 1;
}

.uf-modal-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 财务进度条样式 */
.uf-progress {
    height: 10px;
    background: #e9ecef;
    border-radius: var(--uf-border-radius);
    overflow: hidden;
    margin: 8px 0;
}

.uf-progress-bar {
    height: 100%;
    background: var(--uf-primary);
    transition: width 0.3s ease;
}

.uf-progress-bar.success {
    background: var(--uf-success);
}

.uf-progress-bar.warning {
    background: var(--uf-warning);
}

.uf-progress-bar.danger {
    background: var(--uf-danger);
}

/* 财务时间轴样式 */
.uf-timeline {
    position: relative;
    padding: 16px 0;
    margin: 16px 0;
}

.uf-timeline::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 15px;
    width: 2px;
    background: #e9ecef;
}

.uf-timeline-item {
    position: relative;
    margin-bottom: 16px;
    padding-left: 40px;
}

.uf-timeline-dot {
    position: absolute;
    left: 8px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #e9ecef;
    border: 2px solid white;
    z-index: 1;
}

.uf-timeline-item.completed .uf-timeline-dot {
    background: var(--uf-success);
}

.uf-timeline-item.active .uf-timeline-dot {
    background: var(--uf-primary);
}

.uf-timeline-item.pending .uf-timeline-dot {
    background: #e9ecef;
}

.uf-timeline-content {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 8px 12px;
}

.uf-timeline-title {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.uf-timeline-time {
    font-size: 11px;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --uf-font-size: 13px;
        --uf-font-size-large: 14px;
        --uf-font-size-small: 13px;
        --uf-btn-height: 28px;
        --uf-btn-height-sm: 24px;
        --uf-btn-height-lg: 32px;
        --uf-form-label-width: 80px;
    }

    .uf-table th,
    .uf-table td {
        padding: 2px 4px;
        font-size: 13px;
    }

    .uf-btn {
        padding: 4px 8px;
        min-height: var(--uf-btn-height);
    }

    .uf-btn-group {
        flex-direction: column;
        gap: 1px;
    }

    .uf-btn-group .uf-btn {
        border-radius: var(--uf-border-radius);
    }

    .uf-form-control {
        padding: 4px 6px;
    }

    .uf-card-body {
        padding: 8px;
    }

    .uf-financial-form-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-financial-form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }

    .uf-financial-form-label {
        text-align: left;
        min-width: auto;
    }

    .uf-financial-summary-content {
        flex-direction: column;
        gap: 8px;
    }

    .uf-financial-toolbar {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .uf-financial-toolbar-left,
    .uf-financial-toolbar-right {
        justify-content: center;
    }
    
    .uf-modal-content {
        width: 90%;
    }
    
    .uf-approval-flow {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .uf-approval-step {
        flex-direction: row;
        align-items: center;
        gap: 8px;
        width: auto;
    }
    
    .uf-approval-step:not(:last-child)::after {
        top: 50%;
        left: 30px;
        width: 2px;
        height: calc(100% + 16px);
    }
    
    .uf-approval-label {
        margin-top: 0;
        text-align: left;
    }
}

/* 用友表格容器样式 */
.uf-table-container {
    overflow-x: auto;
    border: 1px solid var(--uf-grid-border);
    border-radius: var(--uf-border-radius);
    background: white;
}

/* 用友二级按钮样式 - 统一灰白色 */
.uf-btn-secondary {
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    border-color: var(--uf-border);
    color: #333;
    font-weight: 500;
}

.uf-btn-secondary:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
}

.uf-btn-secondary:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
}

/* 用友图标样式 */
.uf-icon {
    margin-right: 3px;
    font-size: inherit;
    vertical-align: middle;
}

.uf-icon-only {
    margin-right: 0;
}

.uf-icon-left {
    margin-right: 4px;
    margin-left: 0;
}

.uf-icon-right {
    margin-left: 4px;
    margin-right: 0;
}

/* ========================================
   用友布局和工具类样式
   ======================================== */

/* 用友布局系统 */
.uf-row {
    display: flex;
    flex-wrap: wrap;
    margin: -4px;
}

.uf-col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 4px;
}

.uf-col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 4px;
}

.uf-col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 4px;
}

.uf-col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 4px;
}

/* 用友统计卡片 */
.uf-stat-card {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--uf-box-shadow);
    transition: var(--uf-transition);
    height: 100%;
}

.uf-stat-card:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--uf-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.uf-stat-content {
    flex: 1;
    min-width: 0;
}

.uf-stat-title {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.uf-stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    font-family: 'Times New Roman', var(--uf-font-family);
}

/* 用友文本对齐工具类 */
.uf-text-left { text-align: left !important; }
.uf-text-center { text-align: center !important; }
.uf-text-right { text-align: right !important; }
.uf-text-muted { color: #666 !important; }

/* 用友卡片工具栏 */
.uf-card-tools {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
}

/* 用友表单布局 */
.uf-form {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-end;
}

.uf-form .uf-form-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 0;
}

.uf-form .uf-form-label {
    margin-bottom: 2px;
    margin-right: 0;
    text-align: left;
    min-width: auto;
}

/* 用友按钮组 */
.uf-btn-group {
    display: inline-flex;
    gap: 2px;
}

.uf-btn-group .uf-btn {
    border-radius: 0;
}

.uf-btn-group .uf-btn:first-child {
    border-radius: var(--uf-border-radius) 0 0 var(--uf-border-radius);
}

.uf-btn-group .uf-btn:last-child {
    border-radius: 0 var(--uf-border-radius) var(--uf-border-radius) 0;
}

.uf-btn-group .uf-btn:only-child {
    border-radius: var(--uf-border-radius);
}

/* 用友响应式工具类 */
@media (max-width: 768px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .uf-row {
        margin: -2px;
    }

    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6,
    .uf-col-md-12 {
        padding: 2px;
    }

    .uf-stat-card {
        padding: 8px;
        gap: 8px;
    }

    .uf-stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .uf-stat-value {
        font-size: 14px;
    }
}

/* ========================================
   科目余额表专用样式
   ======================================== */

/* 科目余额表容器 */
.uf-balance-sheet-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 科目余额表表格样式 */
.uf-balance-sheet-table {
    font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
    font-size: 11px;
    border-collapse: collapse;
}

.uf-balance-sheet-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%) !important;
    color: white !important;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 11px;
    border: 1px solid var(--uf-grid-border);
    padding: 4px 6px;
    height: 24px;
    cursor: pointer;
}

.uf-balance-sheet-table th.sortable:hover {
    background: linear-gradient(to bottom, var(--uf-primary-dark) 0%, var(--uf-primary) 100%) !important;
}

.uf-balance-sheet-table th.sort-asc::after {
    content: ' ↑';
    color: #fff;
}

.uf-balance-sheet-table th.sort-desc::after {
    content: ' ↓';
    color: #fff;
}

.uf-balance-sheet-table td {
    vertical-align: middle;
    padding: 3px 6px;
    font-size: 11px;
    border: 1px solid var(--uf-grid-border);
    height: 22px;
    line-height: 1.2;
}

.uf-balance-sheet-table .uf-level-1-row {
    background: #f8f9fa;
    font-weight: 600;
}

.uf-balance-sheet-table .uf-level-1-row:hover {
    background: var(--uf-row-hover) !important;
}

/* 科目层级样式 */
.uf-subject-code-cell,
.uf-subject-name-cell {
    position: relative;
}

.uf-level-indent {
    display: inline-block;
}

.uf-level-1-name {
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-level-badge {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    min-width: 16px;
}

.uf-level-1 {
    background: var(--uf-primary);
    color: white;
}

.uf-level-2 {
    background: var(--uf-info);
    color: white;
}

.uf-level-3 {
    background: var(--uf-success);
    color: white;
}

.uf-level-4 {
    background: var(--uf-warning);
    color: white;
}

.uf-level-5 {
    background: var(--uf-danger);
    color: white;
}

/* 余额金额样式 */
.uf-balance-positive {
    color: var(--uf-success);
}

.uf-balance-negative {
    color: var(--uf-danger);
}

/* 合计行样式 */
.uf-total-row {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%) !important;
    font-weight: 600;
    border-top: 2px solid var(--uf-primary) !important;
}

.uf-total-row th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%) !important;
    color: var(--uf-primary) !important;
    border: 1px solid var(--uf-primary) !important;
}

.uf-amount-total {
    font-weight: 700;
    color: var(--uf-primary);
}

/* 分析网格布局 */
.uf-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
}

/* 统计网格布局 */
.uf-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.uf-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-stat-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
}

.uf-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-amount-large {
    font-size: 16px;
    font-weight: 700;
}

/* 类型统计样式 */
.uf-type-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-type-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-type-name {
    font-weight: 600;
}

.uf-type-details {
    font-size: 11px;
    color: #666;
}

.uf-count-badge {
    display: inline-block;
    padding: 1px 4px;
    background: var(--uf-primary);
    color: white;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

/* 信息卡片样式 */
.uf-info-card {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.uf-info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #333;
}

.uf-info-icon {
    color: var(--uf-success);
    font-size: 12px;
    flex-shrink: 0;
}

/* 查询表单样式 */
.uf-search-form {
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    padding: 12px;
    margin-bottom: 10px;
    border-radius: var(--uf-border-radius);
}

.uf-query-form {
    margin: 0;
}

.uf-record-count {
    font-size: 11px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 2px;
    border: 1px solid var(--uf-border);
}

/* 科目余额表响应式设计 */
@media (max-width: 768px) {
    .uf-analysis-grid {
        grid-template-columns: 1fr;
    }

    .uf-stats-grid {
        grid-template-columns: 1fr;
    }

    .uf-balance-sheet-table {
        font-size: 10px;
    }

    .uf-balance-sheet-table th,
    .uf-balance-sheet-table td {
        padding: 2px 4px;
        font-size: 10px;
    }

    .uf-type-stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* 打印样式优化 */
@media print {
    .uf-table,
    .uf-financial-table {
        font-size: 12px;
    }
    
    .uf-table th,
    .uf-table td,
    .uf-financial-table th,
    .uf-financial-table td {
        font-size: 12px;
        padding: 4px 6px;
    }
}
