#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSRF语法错误修复工具
==================

专门修复模板中的CSRF令牌语法错误，如：
- {{ csrf_token() }}<div> → {{ csrf_token() }}\n<div>
- 其他格式错误
"""

import os
import re
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import logging


class CSRFSyntaxFixer:
    """CSRF语法错误修复工具"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        
        # CSRF语法修复规则
        self.syntax_fix_rules = [
            {
                "name": "CSRF令牌后直接跟HTML标签",
                "pattern": r'(\{\{\s*csrf_token\(\)\s*\}\})(<[^>]+>)',
                "replacement": r'\1\n                \2',
                "description": "在CSRF令牌和HTML标签之间添加换行和缩进"
            },
            {
                "name": "CSRF令牌后直接跟文本",
                "pattern": r'(\{\{\s*csrf_token\(\)\s*\}\})([A-Za-z\u4e00-\u9fa5])',
                "replacement": r'\1\n                \2',
                "description": "在CSRF令牌和文本之间添加换行和缩进"
            },
            {
                "name": "form.csrf_token后直接跟HTML标签",
                "pattern": r'(\{\{\s*form\.csrf_token\s*\}\})(<[^>]+>)',
                "replacement": r'\1\n                \2',
                "description": "在form.csrf_token和HTML标签之间添加换行和缩进"
            },
            {
                "name": "修复错误的CSRF语法(缺少括号)",
                "pattern": r'\{\{\s*csrf_token\s*\}\}',
                "replacement": r'{{ csrf_token() }}',
                "description": "修复csrf_token语法错误（缺少括号）"
            },
            {
                "name": "清理多余空白的CSRF",
                "pattern": r'\{\{\s{2,}csrf_token\(\)\s{2,}\}\}',
                "replacement": r'{{ csrf_token() }}',
                "description": "清理CSRF令牌周围的多余空白"
            },
            {
                "name": "清理多余空白的form.csrf_token",
                "pattern": r'\{\{\s{2,}form\.csrf_token\s{2,}\}\}',
                "replacement": r'{{ form.csrf_token }}',
                "description": "清理form.csrf_token周围的多余空白"
            }
        ]
        
        # 修复结果
        self.fix_results = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "files_processed": [],
            "fixes_applied": [],
            "statistics": {
                "total_files": 0,
                "files_modified": 0,
                "total_fixes": 0,
                "syntax_fixes": 0
            }
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_template_files(self) -> List[Path]:
        """扫描模板文件"""
        if not self.templates_root.exists():
            self.logger.error(f"❌ 模板目录不存在: {self.templates_root}")
            return []
        
        template_files = list(self.templates_root.rglob("*.html"))
        self.fix_results["statistics"]["total_files"] = len(template_files)
        self.logger.info(f"📁 发现 {len(template_files)} 个模板文件")
        
        return template_files
    
    def create_backup(self, file_path: Path) -> Path:
        """创建文件备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.project_root / "csrf_syntax_fixes_backup" / timestamp
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        relative_path = file_path.relative_to(self.templates_root)
        backup_file = backup_dir / relative_path
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(file_path, backup_file)
        return backup_file
    
    def has_csrf_tokens(self, content: str) -> bool:
        """检查内容是否包含CSRF令牌"""
        return bool(re.search(r'csrf_token', content, re.IGNORECASE))
    
    def fix_csrf_syntax_in_file(self, file_path: Path) -> Dict:
        """修复单个文件中的CSRF语法错误"""
        file_result = {
            "file": str(file_path.relative_to(self.project_root)),
            "backup_created": False,
            "fixes_applied": [],
            "total_changes": 0
        }
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 检查是否有CSRF令牌
            if not self.has_csrf_tokens(original_content):
                # 没有CSRF令牌，跳过
                return file_result
            
            modified_content = original_content
            total_fixes = 0
            
            # 应用CSRF语法修复规则
            for fix_rule in self.syntax_fix_rules:
                new_content, count = self._apply_fix(modified_content, fix_rule)
                if count > 0:
                    modified_content = new_content
                    total_fixes += count
                    self.fix_results["statistics"]["syntax_fixes"] += count
                    file_result["fixes_applied"].append({
                        "name": fix_rule["name"],
                        "count": count,
                        "description": fix_rule["description"]
                    })
            
            file_result["total_changes"] = total_fixes
            
            # 如果有修改，保存文件
            if modified_content != original_content:
                # 创建备份
                backup_file = self.create_backup(file_path)
                file_result["backup_created"] = True
                
                # 保存修改后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                self.fix_results["statistics"]["files_modified"] += 1
                self.fix_results["statistics"]["total_fixes"] += total_fixes
                
                self.logger.info(f"✅ 修复完成: {file_path.relative_to(self.templates_root)} ({total_fixes} 处语法修改)")
            
        except Exception as e:
            error_msg = f"修复文件失败: {str(e)}"
            self.logger.error(f"❌ {file_path}: {error_msg}")
        
        return file_result
    
    def _apply_fix(self, content: str, fix_rule: Dict) -> Tuple[str, int]:
        """应用单个修复规则"""
        pattern = fix_rule["pattern"]
        replacement = fix_rule["replacement"]
        
        # 计算匹配次数
        matches = re.findall(pattern, content)
        count = len(matches)
        
        if count > 0:
            # 应用替换
            new_content = re.sub(pattern, replacement, content)
            return new_content, count
        
        return content, 0
    
    def run_csrf_syntax_fixes(self) -> Dict:
        """运行CSRF语法修复"""
        self.logger.info("🔧 开始CSRF语法修复...")
        
        # 扫描模板文件
        template_files = self.scan_template_files()
        
        if not template_files:
            self.logger.warning("⚠️ 未找到模板文件")
            return self.fix_results
        
        # 修复每个文件
        for i, file_path in enumerate(template_files, 1):
            self.logger.info(f"🔍 [{i}/{len(template_files)}] 检查: {file_path.relative_to(self.templates_root)}")
            
            file_result = self.fix_csrf_syntax_in_file(file_path)
            self.fix_results["files_processed"].append(file_result)
            
            if file_result["fixes_applied"]:
                self.fix_results["fixes_applied"].append(file_result)
        
        self.logger.info("✅ CSRF语法修复完成")
        return self.fix_results
    
    def generate_fix_report(self) -> str:
        """生成修复报告"""
        stats = self.fix_results["statistics"]
        
        report = []
        report.append("=" * 80)
        report.append("🔧 CSRF语法修复工具报告")
        report.append("=" * 80)
        report.append(f"修复时间: {self.fix_results['timestamp'][:19]}")
        report.append(f"项目路径: {self.fix_results['project_root']}")
        report.append("")
        
        # 修复统计
        report.append("📊 修复统计:")
        report.append(f"  • 处理文件: {stats['total_files']} 个")
        report.append(f"  • 修改文件: {stats['files_modified']} 个")
        report.append(f"  • 语法修复: {stats['syntax_fixes']} 处")
        report.append("")
        
        # 修复详情
        if self.fix_results["fixes_applied"]:
            report.append("✅ 修复的文件:")
            for file_result in self.fix_results["fixes_applied"]:
                report.append(f"\n📄 {file_result['file']}:")
                for fix in file_result["fixes_applied"]:
                    report.append(f"  • {fix['name']}: {fix['count']} 处 - {fix['description']}")
        else:
            report.append("ℹ️ 未发现需要修复的CSRF语法错误")
        
        report.append("")
        report.append("🎉 CSRF语法修复完成!")
        report.append("建议:")
        report.append("1. 测试所有修复的表单功能")
        report.append("2. 验证CSRF保护正常工作")
        report.append("3. 检查模板渲染是否正常")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_results(self):
        """保存修复结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"csrf_syntax_fix_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.fix_results, f, indent=2, ensure_ascii=False)
        
        # 保存文本报告
        text_file = self.project_root / f"csrf_syntax_fix_summary_{timestamp}.txt"
        summary_report = self.generate_fix_report()
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        self.logger.info(f"📊 详细报告已保存: {json_file}")
        self.logger.info(f"📊 摘要报告已保存: {text_file}")
        
        # 显示摘要
        print(summary_report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CSRF语法修复工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 创建修复器
    fixer = CSRFSyntaxFixer(args.project_root)
    
    # 运行修复
    results = fixer.run_csrf_syntax_fixes()
    
    # 保存结果
    fixer.save_results()
    
    # 返回状态
    if results["statistics"]["syntax_fixes"] > 0:
        print(f"\n🎉 成功修复 {results['statistics']['syntax_fixes']} 个CSRF语法问题")
    else:
        print("\n✅ 未发现需要修复的CSRF语法问题")


if __name__ == "__main__":
    main()
