2025-06-19 21:16:49,849 INFO: 应用启动 - PID: 10656 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 21:17:33,988 ERROR: Exception on /financial/payables [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\payables.py", line 124, in payables_index
    payable.supplier = SupplierMock(row.supplier_id, row.supplier_name)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 537, in __set__
    self.impl.set(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1467, in set
    value = self.fire_replace_event(state, dict_, value, old, initiator)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1506, in fire_replace_event
    value = fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 2161, in emit_backref_from_scalar_set_event
    instance_state(child),
AttributeError: 'SupplierMock' object has no attribute '_sa_instance_state'
2025-06-19 21:17:37,183 ERROR: Exception on /financial/payables [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\payables.py", line 124, in payables_index
    payable.supplier = SupplierMock(row.supplier_id, row.supplier_name)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 537, in __set__
    self.impl.set(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1467, in set
    value = self.fire_replace_event(state, dict_, value, old, initiator)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1506, in fire_replace_event
    value = fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 2161, in emit_backref_from_scalar_set_event
    instance_state(child),
AttributeError: 'SupplierMock' object has no attribute '_sa_instance_state'
2025-06-19 21:19:14,627 ERROR: Exception on /financial/payables [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\payables.py", line 124, in payables_index
    payable.supplier = SupplierMock(row.supplier_id, row.supplier_name)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 537, in __set__
    self.impl.set(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1467, in set
    value = self.fire_replace_event(state, dict_, value, old, initiator)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1506, in fire_replace_event
    value = fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 2161, in emit_backref_from_scalar_set_event
    instance_state(child),
AttributeError: 'SupplierMock' object has no attribute '_sa_instance_state'
2025-06-19 21:20:18,215 ERROR: Exception on /financial/payables [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\payables.py", line 124, in payables_index
    payable.supplier = SupplierMock(row.supplier_id, row.supplier_name)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 537, in __set__
    self.impl.set(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1467, in set
    value = self.fire_replace_event(state, dict_, value, old, initiator)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 1506, in fire_replace_event
    value = fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\attributes.py", line 2161, in emit_backref_from_scalar_set_event
    instance_state(child),
AttributeError: 'SupplierMock' object has no attribute '_sa_instance_state'
2025-06-19 21:20:54,098 ERROR: 创建财务凭证失败: local variable 'date' referenced before assignment [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:266]
