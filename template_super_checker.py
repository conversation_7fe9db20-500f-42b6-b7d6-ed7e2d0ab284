#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板超级检查工具
===============

全面检查StudentsCMSSP项目中所有模板文件的工具

功能特性:
1. 扫描所有HTML模板文件
2. 检查Bootstrap版本和依赖
3. 分析CSS/JS引用
4. 检测模板继承关系
5. 验证Jinja2语法
6. 检查静态资源引用
7. 分析表单和组件使用
8. 生成详细报告
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Optional, Tuple
import logging
from collections import defaultdict, Counter


class TemplateSuperChecker:
    """模板超级检查工具"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        self.static_root = self.project_root / "app" / "static"
        
        # 检查结果
        self.results = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "templates_root": str(self.templates_root),
                "total_files": 0,
                "total_lines": 0
            },
            "files": {},
            "bootstrap_analysis": {
                "version_detected": None,
                "css_files": [],
                "js_files": [],
                "classes_used": Counter(),
                "attributes_used": Counter()
            },
            "template_hierarchy": {},
            "static_resources": {
                "css_files": [],
                "js_files": [],
                "images": [],
                "missing_files": []
            },
            "issues": {
                "syntax_errors": [],
                "missing_resources": [],
                "deprecated_features": [],
                "security_concerns": [],
                "performance_issues": []
            },
            "statistics": {
                "by_directory": defaultdict(int),
                "by_extension": defaultdict(int),
                "template_inheritance": defaultdict(list),
                "component_usage": defaultdict(int)
            }
        }
        
        # Bootstrap相关模式
        self.bootstrap_patterns = {
            "css_files": [
                r'bootstrap[^"\']*\.css',
                r'dataTables\.bootstrap[^"\']*\.css',
                r'select2-bootstrap[^"\']*\.css'
            ],
            "js_files": [
                r'bootstrap[^"\']*\.js',
                r'dataTables\.bootstrap[^"\']*\.js'
            ],
            "classes": [
                r'\bcontainer\b', r'\brow\b', r'\bcol-\w+\b',
                r'\bbtn\b', r'\bbtn-\w+\b', r'\bcard\b', r'\bmodal\b',
                r'\bnavbar\b', r'\btable\b', r'\bform-\w+\b',
                r'\balert\b', r'\bbadge\b', r'\bbreadcrumb\b'
            ],
            "attributes": [
                r'data-toggle', r'data-target', r'data-dismiss',
                r'data-bs-toggle', r'data-bs-target', r'data-bs-dismiss'
            ]
        }
        
        # Jinja2模式
        self.jinja2_patterns = {
            "variables": r'\{\{\s*([^}]+)\s*\}\}',
            "blocks": r'\{\%\s*block\s+(\w+)\s*\%\}',
            "extends": r'\{\%\s*extends\s+["\']([^"\']+)["\']\s*\%\}',
            "includes": r'\{\%\s*include\s+["\']([^"\']+)["\']\s*\%\}',
            "macros": r'\{\%\s*macro\s+(\w+)\s*\([^)]*\)\s*\%\}',
            "filters": r'\|\s*(\w+)',
            "comments": r'\{#.*?#\}'
        }
        
        # 安全检查模式
        self.security_patterns = {
            "xss_risk": [
                r'\{\{\s*[^|]*\s*\|\s*safe\s*\}\}',  # |safe filter
                r'innerHTML\s*=',  # innerHTML assignment
                r'document\.write\s*\('  # document.write
            ],
            "csrf_missing": [
                r'<form[^>]*method\s*=\s*["\']post["\'][^>]*>(?![^<]*csrf_token)'
            ]
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_template_files(self) -> List[Path]:
        """扫描所有模板文件"""
        template_files = []
        
        if not self.templates_root.exists():
            self.logger.error(f"❌ 模板目录不存在: {self.templates_root}")
            return template_files
        
        # 扫描所有HTML文件
        for html_file in self.templates_root.rglob("*.html"):
            template_files.append(html_file)
            
            # 统计目录分布
            relative_path = html_file.relative_to(self.templates_root)
            directory = str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
            self.results["statistics"]["by_directory"][directory] += 1
            
            # 统计扩展名
            self.results["statistics"]["by_extension"][html_file.suffix] += 1
        
        self.results["scan_info"]["total_files"] = len(template_files)
        self.logger.info(f"📁 发现 {len(template_files)} 个模板文件")
        
        return template_files
    
    def analyze_template_file(self, file_path: Path) -> Dict:
        """分析单个模板文件"""
        analysis = {
            "path": str(file_path.relative_to(self.project_root)),
            "size": 0,
            "lines": 0,
            "encoding": "utf-8",
            "bootstrap": {
                "css_files": [],
                "js_files": [],
                "classes": [],
                "attributes": [],
                "version_hints": []
            },
            "jinja2": {
                "extends": None,
                "blocks": [],
                "includes": [],
                "macros": [],
                "variables": [],
                "filters": []
            },
            "static_resources": {
                "css": [],
                "js": [],
                "images": []
            },
            "forms": {
                "count": 0,
                "methods": [],
                "csrf_protected": False
            },
            "issues": []
        }
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis["size"] = len(content)
            analysis["lines"] = content.count('\n') + 1
            self.results["scan_info"]["total_lines"] += analysis["lines"]
            
            # 分析Bootstrap使用
            self._analyze_bootstrap_usage(content, analysis)
            
            # 分析Jinja2语法
            self._analyze_jinja2_syntax(content, analysis)
            
            # 分析静态资源引用
            self._analyze_static_resources(content, analysis)
            
            # 分析表单
            self._analyze_forms(content, analysis)
            
            # 安全检查
            self._security_check(content, analysis)
            
            # 性能检查
            self._performance_check(content, analysis)
            
        except Exception as e:
            error_msg = f"分析文件失败: {file_path} - {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            analysis["issues"].append({
                "type": "file_error",
                "message": error_msg,
                "severity": "error"
            })
        
        return analysis
    
    def _analyze_bootstrap_usage(self, content: str, analysis: Dict):
        """分析Bootstrap使用情况"""
        # 检查CSS文件
        for pattern in self.bootstrap_patterns["css_files"]:
            matches = re.findall(pattern, content, re.IGNORECASE)
            analysis["bootstrap"]["css_files"].extend(matches)
            self.results["bootstrap_analysis"]["css_files"].extend(matches)
        
        # 检查JS文件
        for pattern in self.bootstrap_patterns["js_files"]:
            matches = re.findall(pattern, content, re.IGNORECASE)
            analysis["bootstrap"]["js_files"].extend(matches)
            self.results["bootstrap_analysis"]["js_files"].extend(matches)
        
        # 检查Bootstrap类
        for pattern in self.bootstrap_patterns["classes"]:
            matches = re.findall(pattern, content)
            analysis["bootstrap"]["classes"].extend(matches)
            for match in matches:
                self.results["bootstrap_analysis"]["classes_used"][match] += 1
        
        # 检查Bootstrap属性
        for pattern in self.bootstrap_patterns["attributes"]:
            matches = re.findall(pattern, content)
            analysis["bootstrap"]["attributes"].extend(matches)
            for match in matches:
                self.results["bootstrap_analysis"]["attributes_used"][match] += 1
        
        # 检测Bootstrap版本
        if 'bootstrap@4' in content or 'bootstrap/4' in content:
            analysis["bootstrap"]["version_hints"].append("Bootstrap 4")
        if 'bootstrap@5' in content or 'bootstrap/5' in content:
            analysis["bootstrap"]["version_hints"].append("Bootstrap 5")
        if 'data-bs-' in content:
            analysis["bootstrap"]["version_hints"].append("Bootstrap 5 (data-bs-* attributes)")
        if 'data-toggle' in content and 'data-bs-toggle' not in content:
            analysis["bootstrap"]["version_hints"].append("Bootstrap 4 (data-toggle attributes)")
    
    def _analyze_jinja2_syntax(self, content: str, analysis: Dict):
        """分析Jinja2语法"""
        # 模板继承
        extends_match = re.search(self.jinja2_patterns["extends"], content)
        if extends_match:
            analysis["jinja2"]["extends"] = extends_match.group(1)
            self.results["statistics"]["template_inheritance"][extends_match.group(1)].append(
                analysis["path"]
            )
        
        # 块定义
        blocks = re.findall(self.jinja2_patterns["blocks"], content)
        analysis["jinja2"]["blocks"] = blocks
        
        # 包含文件
        includes = re.findall(self.jinja2_patterns["includes"], content)
        analysis["jinja2"]["includes"] = includes
        
        # 宏定义
        macros = re.findall(self.jinja2_patterns["macros"], content)
        analysis["jinja2"]["macros"] = macros
        
        # 变量使用
        variables = re.findall(self.jinja2_patterns["variables"], content)
        analysis["jinja2"]["variables"] = variables[:10]  # 只保留前10个
        
        # 过滤器使用
        filters = re.findall(self.jinja2_patterns["filters"], content)
        analysis["jinja2"]["filters"] = list(set(filters))
    
    def _analyze_static_resources(self, content: str, analysis: Dict):
        """分析静态资源引用"""
        # CSS文件
        css_pattern = r'<link[^>]*href=["\']([^"\']+\.css)["\']'
        css_files = re.findall(css_pattern, content, re.IGNORECASE)
        analysis["static_resources"]["css"] = css_files
        
        # JS文件
        js_pattern = r'<script[^>]*src=["\']([^"\']+\.js)["\']'
        js_files = re.findall(js_pattern, content, re.IGNORECASE)
        analysis["static_resources"]["js"] = js_files
        
        # 图片文件
        img_pattern = r'<img[^>]*src=["\']([^"\']+\.(png|jpg|jpeg|gif|svg|webp))["\']'
        images = re.findall(img_pattern, content, re.IGNORECASE)
        analysis["static_resources"]["images"] = [img[0] for img in images]
        
        # 检查资源是否存在
        for resource_type, resources in analysis["static_resources"].items():
            for resource in resources:
                if resource.startswith(('http://', 'https://', '//')):
                    continue  # 跳过外部资源
                
                # 构建资源路径
                if resource.startswith('/static/'):
                    resource_path = self.project_root / "app" / resource[1:]
                elif resource.startswith('static/'):
                    resource_path = self.project_root / "app" / resource
                else:
                    resource_path = self.static_root / resource
                
                if not resource_path.exists():
                    analysis["issues"].append({
                        "type": "missing_resource",
                        "message": f"缺少{resource_type}资源: {resource}",
                        "severity": "warning"
                    })
    
    def _analyze_forms(self, content: str, analysis: Dict):
        """分析表单"""
        # 查找表单
        form_pattern = r'<form[^>]*>'
        forms = re.findall(form_pattern, content, re.IGNORECASE)
        analysis["forms"]["count"] = len(forms)
        
        # 检查表单方法
        method_pattern = r'<form[^>]*method\s*=\s*["\'](\w+)["\']'
        methods = re.findall(method_pattern, content, re.IGNORECASE)
        analysis["forms"]["methods"] = methods
        
        # 检查CSRF保护
        csrf_pattern = r'csrf_token|{{ csrf_token() }}|hidden.*csrf'
        if re.search(csrf_pattern, content, re.IGNORECASE):
            analysis["forms"]["csrf_protected"] = True
        elif 'method="post"' in content.lower() or "method='post'" in content.lower():
            analysis["issues"].append({
                "type": "security",
                "message": "POST表单缺少CSRF保护",
                "severity": "warning"
            })
    
    def _security_check(self, content: str, analysis: Dict):
        """安全检查"""
        # XSS风险检查
        for pattern in self.security_patterns["xss_risk"]:
            if re.search(pattern, content, re.IGNORECASE):
                analysis["issues"].append({
                    "type": "security",
                    "message": "潜在XSS风险：使用了|safe过滤器或innerHTML",
                    "severity": "warning"
                })
                break
    
    def _performance_check(self, content: str, analysis: Dict):
        """性能检查"""
        # 检查大量内联样式
        inline_style_count = len(re.findall(r'style\s*=\s*["\'][^"\']+["\']', content))
        if inline_style_count > 10:
            analysis["issues"].append({
                "type": "performance",
                "message": f"发现{inline_style_count}个内联样式，建议使用CSS类",
                "severity": "info"
            })
        
        # 检查大量内联脚本
        inline_script_count = len(re.findall(r'<script[^>]*>.*?</script>', content, re.DOTALL))
        if inline_script_count > 5:
            analysis["issues"].append({
                "type": "performance",
                "message": f"发现{inline_script_count}个内联脚本，建议外部化",
                "severity": "info"
            })
    
    def build_template_hierarchy(self):
        """构建模板继承层次结构"""
        hierarchy = {}
        
        for file_path, file_data in self.results["files"].items():
            extends = file_data["jinja2"]["extends"]
            if extends:
                if extends not in hierarchy:
                    hierarchy[extends] = []
                hierarchy[extends].append(file_path)
        
        self.results["template_hierarchy"] = hierarchy
    
    def detect_bootstrap_version(self):
        """检测Bootstrap版本"""
        version_hints = []
        
        for file_data in self.results["files"].values():
            version_hints.extend(file_data["bootstrap"]["version_hints"])
        
        # 分析版本提示
        if version_hints:
            version_counter = Counter(version_hints)
            most_common = version_counter.most_common(1)[0]
            self.results["bootstrap_analysis"]["version_detected"] = most_common[0]
        
        # 基于属性判断
        bs4_attrs = self.results["bootstrap_analysis"]["attributes_used"].get("data-toggle", 0)
        bs5_attrs = self.results["bootstrap_analysis"]["attributes_used"].get("data-bs-toggle", 0)
        
        if bs5_attrs > bs4_attrs:
            self.results["bootstrap_analysis"]["version_detected"] = "Bootstrap 5 (主要)"
        elif bs4_attrs > 0:
            self.results["bootstrap_analysis"]["version_detected"] = "Bootstrap 4 (主要)"
    
    def generate_summary_report(self) -> str:
        """生成摘要报告"""
        report = []
        report.append("=" * 80)
        report.append("📋 StudentsCMSSP 模板超级检查报告")
        report.append("=" * 80)
        
        # 基本统计
        scan_info = self.results["scan_info"]
        report.append(f"📁 扫描目录: {scan_info['templates_root']}")
        report.append(f"📄 模板文件: {scan_info['total_files']} 个")
        report.append(f"📝 总行数: {scan_info['total_lines']:,} 行")
        report.append(f"🕐 扫描时间: {scan_info['timestamp'][:19]}")
        report.append("")
        
        # 目录分布
        report.append("📂 目录分布:")
        for directory, count in sorted(self.results["statistics"]["by_directory"].items()):
            report.append(f"  • {directory}: {count} 个文件")
        report.append("")
        
        # Bootstrap分析
        bootstrap = self.results["bootstrap_analysis"]
        report.append("🎨 Bootstrap 分析:")
        report.append(f"  • 检测版本: {bootstrap['version_detected'] or '未检测到'}")
        report.append(f"  • CSS文件: {len(set(bootstrap['css_files']))} 个")
        report.append(f"  • JS文件: {len(set(bootstrap['js_files']))} 个")
        
        if bootstrap["classes_used"]:
            top_classes = bootstrap["classes_used"].most_common(5)
            report.append("  • 常用类:")
            for class_name, count in top_classes:
                report.append(f"    - {class_name}: {count} 次")
        report.append("")
        
        # 模板继承
        hierarchy = self.results["template_hierarchy"]
        if hierarchy:
            report.append("🏗️ 模板继承:")
            for base_template, children in hierarchy.items():
                report.append(f"  • {base_template}: {len(children)} 个子模板")
        report.append("")
        
        # 问题统计
        all_issues = []
        for file_data in self.results["files"].values():
            all_issues.extend(file_data["issues"])
        
        if all_issues:
            issue_types = Counter(issue["type"] for issue in all_issues)
            severity_counts = Counter(issue["severity"] for issue in all_issues)
            
            report.append("⚠️ 问题统计:")
            report.append(f"  • 总问题数: {len(all_issues)}")
            for issue_type, count in issue_types.most_common():
                report.append(f"  • {issue_type}: {count} 个")
            
            report.append("  • 严重程度:")
            for severity, count in severity_counts.most_common():
                icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(severity, "•")
                report.append(f"    {icon} {severity}: {count} 个")
        else:
            report.append("✅ 未发现问题")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_full_check(self) -> Dict:
        """运行完整检查"""
        self.logger.info("🚀 开始模板超级检查...")
        
        # 扫描模板文件
        template_files = self.scan_template_files()
        
        if not template_files:
            self.logger.warning("⚠️ 未找到模板文件")
            return self.results
        
        # 分析每个文件
        self.logger.info(f"🔍 分析 {len(template_files)} 个模板文件...")
        for i, file_path in enumerate(template_files, 1):
            self.logger.info(f"  [{i}/{len(template_files)}] {file_path.relative_to(self.templates_root)}")
            
            analysis = self.analyze_template_file(file_path)
            self.results["files"][analysis["path"]] = analysis
        
        # 构建模板层次结构
        self.build_template_hierarchy()
        
        # 检测Bootstrap版本
        self.detect_bootstrap_version()
        
        self.logger.info("✅ 模板检查完成")
        return self.results
    
    def save_results(self, output_dir: str = None):
        """保存检查结果"""
        if output_dir is None:
            output_dir = self.project_root
        
        output_path = Path(output_dir)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = output_path / f"template_check_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # 保存摘要报告
        summary_file = output_path / f"template_check_summary_{timestamp}.txt"
        summary_report = self.generate_summary_report()
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        self.logger.info(f"📊 详细报告已保存: {json_file}")
        self.logger.info(f"📊 摘要报告已保存: {summary_file}")
        
        # 显示摘要
        print(summary_report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模板超级检查工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--output-dir", help="输出目录路径")
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = TemplateSuperChecker(args.project_root)
    
    # 运行检查
    results = checker.run_full_check()
    
    # 保存结果
    checker.save_results(args.output_dir)


if __name__ == "__main__":
    main()
