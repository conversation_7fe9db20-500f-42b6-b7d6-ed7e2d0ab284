#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap升级日志生成器
====================

根据您的建议，生成详细的UPGRADE_LOG.md文件，记录所有变更内容

功能:
1. 记录替换的类名和组件
2. 记录移除的jQuery依赖
3. 记录自定义CSS的调整内容
4. 生成回滚指南
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, List
import json


class UpgradeLogGenerator:
    """升级日志生成器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.log_data = {
            "upgrade_info": {
                "date": datetime.now().isoformat(),
                "from_version": "Bootstrap 4.6.2",
                "to_version": "Bootstrap 5.3.6",
                "project": "StudentsCMSSP"
            },
            "file_changes": [],
            "class_mappings": {},
            "attribute_mappings": {},
            "js_changes": [],
            "warnings": [],
            "manual_tasks": []
        }
    
    def add_file_change(self, file_path: str, changes: Dict):
        """添加文件变更记录"""
        self.log_data["file_changes"].append({
            "file": file_path,
            "timestamp": datetime.now().isoformat(),
            "changes": changes
        })
    
    def add_class_mapping(self, old_class: str, new_class: str, count: int = 1):
        """添加类名映射记录"""
        if old_class not in self.log_data["class_mappings"]:
            self.log_data["class_mappings"][old_class] = {
                "new_class": new_class,
                "count": 0,
                "files": []
            }
        self.log_data["class_mappings"][old_class]["count"] += count
    
    def add_warning(self, warning: str, file_path: str = "", line: int = 0):
        """添加警告信息"""
        self.log_data["warnings"].append({
            "message": warning,
            "file": file_path,
            "line": line,
            "timestamp": datetime.now().isoformat()
        })
    
    def add_manual_task(self, task: str, priority: str = "medium", component: str = ""):
        """添加手动任务"""
        self.log_data["manual_tasks"].append({
            "task": task,
            "priority": priority,
            "component": component,
            "status": "pending"
        })
    
    def generate_markdown_log(self) -> str:
        """生成Markdown格式的升级日志"""
        log = []
        
        # 标题和基本信息
        log.append("# Bootstrap 5.3.6 升级日志")
        log.append("")
        log.append(f"**升级日期**: {self.log_data['upgrade_info']['date'][:19]}")
        log.append(f"**项目**: {self.log_data['upgrade_info']['project']}")
        log.append(f"**版本变更**: {self.log_data['upgrade_info']['from_version']} → {self.log_data['upgrade_info']['to_version']}")
        log.append("")
        log.append("---")
        log.append("")
        
        # 升级摘要
        log.append("## 📊 升级摘要")
        log.append("")
        log.append(f"- **修改文件数量**: {len(self.log_data['file_changes'])}")
        log.append(f"- **类名映射数量**: {len(self.log_data['class_mappings'])}")
        log.append(f"- **属性映射数量**: {len(self.log_data['attribute_mappings'])}")
        log.append(f"- **JavaScript变更**: {len(self.log_data['js_changes'])}")
        log.append(f"- **警告数量**: {len(self.log_data['warnings'])}")
        log.append(f"- **手动任务**: {len(self.log_data['manual_tasks'])}")
        log.append("")
        
        # 主要变更内容
        log.append("## 🔄 主要变更内容")
        log.append("")
        
        # 类名映射
        if self.log_data["class_mappings"]:
            log.append("### 类名映射")
            log.append("")
            log.append("| Bootstrap 4 | Bootstrap 5 | 替换次数 |")
            log.append("|-------------|-------------|----------|")
            
            for old_class, info in sorted(self.log_data["class_mappings"].items()):
                log.append(f"| `{old_class}` | `{info['new_class']}` | {info['count']} |")
            log.append("")
        
        # 属性映射
        if self.log_data["attribute_mappings"]:
            log.append("### 属性映射")
            log.append("")
            log.append("| Bootstrap 4 | Bootstrap 5 | 说明 |")
            log.append("|-------------|-------------|------|")
            
            for old_attr, new_attr in self.log_data["attribute_mappings"].items():
                log.append(f"| `{old_attr}` | `{new_attr}` | JavaScript事件属性 |")
            log.append("")
        
        # JavaScript变更
        if self.log_data["js_changes"]:
            log.append("### JavaScript变更")
            log.append("")
            log.append("#### 移除的jQuery依赖")
            log.append("")
            log.append("```javascript")
            log.append("// Bootstrap 4 (jQuery)")
            log.append("$('.tooltip').tooltip();")
            log.append("$('#myModal').modal();")
            log.append("")
            log.append("// Bootstrap 5 (原生JavaScript)")
            log.append("new bootstrap.Tooltip(document.querySelector('.tooltip'));")
            log.append("new bootstrap.Modal(document.getElementById('myModal'));")
            log.append("```")
            log.append("")
        
        # 文件变更详情
        if self.log_data["file_changes"]:
            log.append("## 📁 文件变更详情")
            log.append("")
            
            for file_change in self.log_data["file_changes"]:
                log.append(f"### {file_change['file']}")
                log.append("")
                log.append(f"**修改时间**: {file_change['timestamp'][:19]}")
                log.append("")
                
                changes = file_change['changes']
                if changes.get('css_replacements', 0) > 0:
                    log.append(f"- CSS引用替换: {changes['css_replacements']} 处")
                if changes.get('js_replacements', 0) > 0:
                    log.append(f"- JS引用替换: {changes['js_replacements']} 处")
                if changes.get('class_replacements', 0) > 0:
                    log.append(f"- 类名替换: {changes['class_replacements']} 处")
                if changes.get('attribute_replacements', 0) > 0:
                    log.append(f"- 属性替换: {changes['attribute_replacements']} 处")
                
                log.append("")
        
        # 警告和注意事项
        if self.log_data["warnings"]:
            log.append("## ⚠️ 警告和注意事项")
            log.append("")
            
            for warning in self.log_data["warnings"]:
                if warning["file"]:
                    log.append(f"- **{warning['file']}** (第{warning['line']}行): {warning['message']}")
                else:
                    log.append(f"- {warning['message']}")
            log.append("")
        
        # 手动任务清单
        if self.log_data["manual_tasks"]:
            log.append("## 📋 手动任务清单")
            log.append("")
            log.append("以下任务需要手动完成:")
            log.append("")
            
            # 按优先级分组
            high_priority = [t for t in self.log_data["manual_tasks"] if t["priority"] == "high"]
            medium_priority = [t for t in self.log_data["manual_tasks"] if t["priority"] == "medium"]
            low_priority = [t for t in self.log_data["manual_tasks"] if t["priority"] == "low"]
            
            if high_priority:
                log.append("### 🔴 高优先级")
                for task in high_priority:
                    log.append(f"- [ ] **{task['component']}**: {task['task']}")
                log.append("")
            
            if medium_priority:
                log.append("### 🟡 中优先级")
                for task in medium_priority:
                    log.append(f"- [ ] **{task['component']}**: {task['task']}")
                log.append("")
            
            if low_priority:
                log.append("### 🟢 低优先级")
                for task in low_priority:
                    log.append(f"- [ ] **{task['component']}**: {task['task']}")
                log.append("")
        
        # 测试清单
        log.append("## 🧪 测试清单")
        log.append("")
        log.append("升级完成后，请逐项测试以下功能:")
        log.append("")
        log.append("### 基础组件测试")
        log.append("- [ ] 导航栏 - 检查下拉菜单和响应式折叠")
        log.append("- [ ] 模态框 - 验证打开/关闭功能")
        log.append("- [ ] 工具提示 - 检查hover效果")
        log.append("- [ ] 表单 - 验证样式和验证状态")
        log.append("- [ ] 按钮 - 检查所有按钮样式")
        log.append("- [ ] 卡片 - 验证布局和响应式")
        log.append("")
        log.append("### 页面级测试")
        log.append("- [ ] 首页布局")
        log.append("- [ ] 登录/注册页面")
        log.append("- [ ] 仪表板")
        log.append("- [ ] 财务模块")
        log.append("- [ ] 数据表格功能")
        log.append("- [ ] 移动端响应式")
        log.append("")
        log.append("### 浏览器兼容性")
        log.append("- [ ] Chrome (最新版)")
        log.append("- [ ] Firefox (最新版)")
        log.append("- [ ] Safari (最新版)")
        log.append("- [ ] Edge (最新版)")
        log.append("- [ ] 移动端浏览器")
        log.append("")
        
        # 回滚指南
        log.append("## 🔙 回滚指南")
        log.append("")
        log.append("如果升级后发现问题，可以使用以下方法回滚:")
        log.append("")
        log.append("### 使用迁移工具回滚")
        log.append("```bash")
        log.append("python run_bootstrap_migration.py rollback")
        log.append("```")
        log.append("")
        log.append("### 手动回滚")
        log.append("1. 恢复备份的文件")
        log.append("2. 重置Git分支: `git checkout main && git branch -D upgrade-bs5`")
        log.append("3. 清除浏览器缓存")
        log.append("4. 重启应用服务")
        log.append("")
        
        # 性能优化建议
        log.append("## 🚀 性能优化建议")
        log.append("")
        log.append("升级完成后，建议进行以下优化:")
        log.append("")
        log.append("1. **CSS优化**")
        log.append("   - 移除未使用的Bootstrap组件")
        log.append("   - 合并自定义CSS文件")
        log.append("   - 启用CSS压缩")
        log.append("")
        log.append("2. **JavaScript优化**")
        log.append("   - 考虑移除jQuery依赖（如果项目允许）")
        log.append("   - 使用Bootstrap 5的模块化导入")
        log.append("   - 启用JavaScript压缩")
        log.append("")
        log.append("3. **缓存策略**")
        log.append("   - 更新CSS/JS文件的版本号")
        log.append("   - 配置适当的缓存头")
        log.append("   - 使用CDN加速静态资源")
        log.append("")
        
        # 联系信息
        log.append("## 📞 技术支持")
        log.append("")
        log.append("如有问题，请参考:")
        log.append("- [Bootstrap 5 官方迁移指南](https://getbootstrap.com/docs/5.3/migration/)")
        log.append("- [项目升级指南](./BOOTSTRAP_5_3_6_UPGRADE_GUIDE.md)")
        log.append("- 迁移工具日志文件")
        log.append("")
        log.append("---")
        log.append("")
        log.append(f"*此日志由Bootstrap迁移工具自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n".join(log)
    
    def save_log(self, filename: str = None):
        """保存升级日志"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"UPGRADE_LOG_{timestamp}.md"
        
        log_content = self.generate_markdown_log()
        log_file = self.project_root / filename
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(log_content)
            
            # 同时保存JSON格式的原始数据
            json_file = self.project_root / filename.replace('.md', '_data.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.log_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 升级日志已保存: {log_file}")
            print(f"✅ 原始数据已保存: {json_file}")
            
        except Exception as e:
            print(f"❌ 保存升级日志失败: {e}")


def main():
    """主函数 - 生成示例升级日志"""
    generator = UpgradeLogGenerator(".")
    
    # 添加示例数据
    generator.add_class_mapping("text-left", "text-start", 15)
    generator.add_class_mapping("ml-3", "ms-3", 8)
    generator.add_class_mapping("form-group", "mb-3", 25)
    
    generator.add_warning("需要手动处理 input-group-append 组件", "app/templates/base.html", 45)
    generator.add_warning("检查自定义CSS与Bootstrap 5的兼容性", "app/static/css/custom.css")
    
    generator.add_manual_task("重构 input-group-append 组件", "high", "表单组件")
    generator.add_manual_task("测试DataTables兼容性", "medium", "数据表格")
    generator.add_manual_task("优化移动端响应式布局", "low", "响应式设计")
    
    # 生成并保存日志
    generator.save_log("UPGRADE_LOG.md")


if __name__ == "__main__":
    main()
