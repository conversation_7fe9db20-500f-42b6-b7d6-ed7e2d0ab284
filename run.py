from app import create_app
from werkzeug.middleware.proxy_fix import ProxyFix

app = create_app()

# 配置代理支持，处理反向代理的头信息
app.wsgi_app = ProxyFix(
    app.wsgi_app,
    x_for=1,  # 信任1个代理的X-Forwarded-For头
    x_proto=1,  # 信任1个代理的X-Forwarded-Proto头
    x_host=1,  # 信任1个代理的X-Forwarded-Host头
    x_prefix=1  # 信任1个代理的X-Forwarded-Prefix头
)

if __name__ == '__main__':
    # 绑定到8080端口，允许外部访问
    # 优化性能参数：关闭调试模式，禁用重载器，启用多线程
    app.run(debug=False, host='0.0.0.0', port=8080, use_reloader=False, threaded=True, processes=1)
