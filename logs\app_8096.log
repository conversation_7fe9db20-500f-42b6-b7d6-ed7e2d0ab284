2025-06-19 13:40:38,152 INFO: 应用启动 - PID: 8096 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 13:40:47,102 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 13:40:51,255 ERROR: Exception on /inventory/check-expiry [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\inventory.py", line 767, in check_expiry
    return render_template('inventory/expiry.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\inventory\expiry.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\inventory\expiry.html", line 78, in block 'content'
    <td>{{ inventory.warehouse.name }}</td>
  File "C:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'app.models.Inventory object' has no attribute 'warehouse'
