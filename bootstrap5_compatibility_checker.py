#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5 兼容性检查和修复工具
===============================

检查和修复Bootstrap 4到5升级后的兼容性问题

功能特性:
1. 检查JavaScript文件的Bootstrap版本兼容性
2. 识别jQuery依赖问题
3. 检查过时的API调用
4. 生成修复建议
5. 自动修复常见问题
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
import logging


class Bootstrap5CompatibilityChecker:
    """Bootstrap 5兼容性检查器"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.static_root = self.project_root / "app" / "static"
        
        # Bootstrap 4到5的变化映射
        self.compatibility_issues = {
            "jquery_dependencies": [
                r'\$\s*\(\s*["\'].*["\'].*\)',  # jQuery选择器
                r'\$\.fn\.',  # jQuery插件
                r'jQuery\s*\(',  # jQuery函数调用
            ],
            "bootstrap4_api": [
                r'\.tooltip\s*\(\s*["\']show["\']',  # Bootstrap 4 tooltip API
                r'\.modal\s*\(\s*["\']show["\']',    # Bootstrap 4 modal API
                r'\.dropdown\s*\(\s*["\']toggle["\']', # Bootstrap 4 dropdown API
                r'\.collapse\s*\(\s*["\']show["\']',   # Bootstrap 4 collapse API
            ],
            "event_names": [
                r'show\.bs\.',     # Bootstrap 4事件名
                r'shown\.bs\.',
                r'hide\.bs\.',
                r'hidden\.bs\.',
                r'inserted\.bs\.',
                r'click\.bs\.',
            ],
            "data_attributes": [
                r'data-toggle',    # Bootstrap 4属性
                r'data-target',
                r'data-dismiss',
                r'data-slide',
                r'data-ride',
            ]
        }
        
        # Bootstrap 5的修复映射
        self.fixes = {
            "data_attributes": {
                r'\bdata-toggle\b': 'data-bs-toggle',
                r'\bdata-target\b': 'data-bs-target',
                r'\bdata-dismiss\b': 'data-bs-dismiss',
                r'\bdata-slide\b': 'data-bs-slide',
                r'\bdata-slide-to\b': 'data-bs-slide-to',
                r'\bdata-ride\b': 'data-bs-ride',
            },
            "api_calls": {
                r"\.tooltip\s*\(\s*['\"]show['\"]\s*\)": ".tooltip('show')",
                r"\.modal\s*\(\s*['\"]show['\"]\s*\)": ".modal('show')",
                r"\.dropdown\s*\(\s*['\"]toggle['\"]\s*\)": ".dropdown('toggle')",
            }
        }
        
        # 检查结果
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "files_checked": [],
            "issues_found": [],
            "fixes_applied": [],
            "summary": {
                "total_files": 0,
                "files_with_issues": 0,
                "total_issues": 0,
                "jquery_dependencies": 0,
                "bootstrap4_api_calls": 0,
                "outdated_events": 0,
                "outdated_attributes": 0
            }
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_javascript_files(self):
        """扫描JavaScript文件"""
        js_files = []
        
        # 扫描static目录下的所有JS文件
        for js_file in self.static_root.rglob("*.js"):
            # 跳过node_modules和vendor目录中的第三方文件
            if 'node_modules' in str(js_file) or 'vendor' in str(js_file):
                continue
            js_files.append(js_file)
        
        # 扫描templates目录中的内联JavaScript
        templates_root = self.project_root / "app" / "templates"
        if templates_root.exists():
            for html_file in templates_root.rglob("*.html"):
                js_files.append(html_file)
        
        self.logger.info(f"📁 发现 {len(js_files)} 个文件需要检查")
        return js_files
    
    def check_file_compatibility(self, file_path):
        """检查单个文件的兼容性"""
        file_issues = {
            "file": str(file_path.relative_to(self.project_root)),
            "type": "js" if file_path.suffix == ".js" else "html",
            "issues": [],
            "suggestions": []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查jQuery依赖
            jquery_issues = self._check_jquery_dependencies(content)
            if jquery_issues:
                file_issues["issues"].extend(jquery_issues)
                self.results["summary"]["jquery_dependencies"] += len(jquery_issues)
            
            # 检查Bootstrap 4 API调用
            api_issues = self._check_bootstrap4_api(content)
            if api_issues:
                file_issues["issues"].extend(api_issues)
                self.results["summary"]["bootstrap4_api_calls"] += len(api_issues)
            
            # 检查过时的事件名
            event_issues = self._check_outdated_events(content)
            if event_issues:
                file_issues["issues"].extend(event_issues)
                self.results["summary"]["outdated_events"] += len(event_issues)
            
            # 检查过时的data属性
            attr_issues = self._check_outdated_attributes(content)
            if attr_issues:
                file_issues["issues"].extend(attr_issues)
                self.results["summary"]["outdated_attributes"] += len(attr_issues)
            
            # 生成修复建议
            if file_issues["issues"]:
                file_issues["suggestions"] = self._generate_suggestions(file_issues["issues"])
                self.results["summary"]["files_with_issues"] += 1
            
        except Exception as e:
            error_issue = {
                "type": "file_error",
                "message": f"读取文件失败: {str(e)}",
                "line": 0,
                "severity": "error"
            }
            file_issues["issues"].append(error_issue)
            self.logger.error(f"❌ 检查文件失败: {file_path} - {str(e)}")
        
        self.results["summary"]["total_issues"] += len(file_issues["issues"])
        return file_issues
    
    def _check_jquery_dependencies(self, content):
        """检查jQuery依赖"""
        issues = []
        
        for pattern in self.compatibility_issues["jquery_dependencies"]:
            matches = list(re.finditer(pattern, content, re.MULTILINE))
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append({
                    "type": "jquery_dependency",
                    "message": f"发现jQuery依赖: {match.group()}",
                    "line": line_num,
                    "code": match.group(),
                    "severity": "warning"
                })
        
        return issues
    
    def _check_bootstrap4_api(self, content):
        """检查Bootstrap 4 API调用"""
        issues = []
        
        for pattern in self.compatibility_issues["bootstrap4_api"]:
            matches = list(re.finditer(pattern, content, re.MULTILINE))
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append({
                    "type": "bootstrap4_api",
                    "message": f"Bootstrap 4 API调用: {match.group()}",
                    "line": line_num,
                    "code": match.group(),
                    "severity": "error"
                })
        
        return issues
    
    def _check_outdated_events(self, content):
        """检查过时的事件名"""
        issues = []
        
        for pattern in self.compatibility_issues["event_names"]:
            matches = list(re.finditer(pattern, content, re.MULTILINE))
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append({
                    "type": "outdated_event",
                    "message": f"过时的事件名: {match.group()}",
                    "line": line_num,
                    "code": match.group(),
                    "severity": "warning"
                })
        
        return issues
    
    def _check_outdated_attributes(self, content):
        """检查过时的data属性"""
        issues = []
        
        for pattern in self.compatibility_issues["data_attributes"]:
            matches = list(re.finditer(pattern, content, re.MULTILINE))
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append({
                    "type": "outdated_attribute",
                    "message": f"过时的data属性: {match.group()}",
                    "line": line_num,
                    "code": match.group(),
                    "severity": "warning"
                })
        
        return issues
    
    def _generate_suggestions(self, issues):
        """生成修复建议"""
        suggestions = []
        
        for issue in issues:
            if issue["type"] == "jquery_dependency":
                suggestions.append("将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API")
            elif issue["type"] == "bootstrap4_api":
                suggestions.append("更新为Bootstrap 5的API调用方式")
            elif issue["type"] == "outdated_event":
                suggestions.append("更新事件名为Bootstrap 5的格式")
            elif issue["type"] == "outdated_attribute":
                suggestions.append("将data-*属性更新为data-bs-*格式")
        
        return list(set(suggestions))  # 去重
    
    def run_compatibility_check(self):
        """运行完整的兼容性检查"""
        self.logger.info("🚀 开始Bootstrap 5兼容性检查...")
        
        # 扫描文件
        files_to_check = self.scan_javascript_files()
        self.results["summary"]["total_files"] = len(files_to_check)
        
        # 检查每个文件
        for i, file_path in enumerate(files_to_check, 1):
            self.logger.info(f"🔍 [{i}/{len(files_to_check)}] 检查: {file_path.relative_to(self.project_root)}")
            
            file_result = self.check_file_compatibility(file_path)
            if file_result["issues"]:
                self.results["issues_found"].append(file_result)
            
            self.results["files_checked"].append(file_result["file"])
        
        self.logger.info("✅ Bootstrap 5兼容性检查完成")
        return self.results
    
    def generate_report(self):
        """生成检查报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"bootstrap5_compatibility_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # 生成文本摘要
        summary_file = self.project_root / f"bootstrap5_compatibility_summary_{timestamp}.txt"
        summary = self._generate_text_summary()
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        self.logger.info(f"📊 兼容性报告已保存: {json_file}")
        self.logger.info(f"📊 摘要报告已保存: {summary_file}")
        
        # 显示摘要
        print(summary)
        
        return json_file, summary_file
    
    def _generate_text_summary(self):
        """生成文本摘要"""
        summary = self.results["summary"]
        
        report = []
        report.append("=" * 70)
        report.append("Bootstrap 5 兼容性检查报告")
        report.append("=" * 70)
        report.append(f"检查时间: {self.results['timestamp'][:19]}")
        report.append(f"项目路径: {self.results['project_root']}")
        report.append("")
        
        # 统计信息
        report.append("📊 检查统计:")
        report.append(f"  • 检查文件: {summary['total_files']} 个")
        report.append(f"  • 有问题文件: {summary['files_with_issues']} 个")
        report.append(f"  • 总问题数: {summary['total_issues']} 个")
        report.append("")
        
        # 问题分类
        report.append("🔍 问题分类:")
        report.append(f"  • jQuery依赖: {summary['jquery_dependencies']} 个")
        report.append(f"  • Bootstrap 4 API: {summary['bootstrap4_api_calls']} 个")
        report.append(f"  • 过时事件: {summary['outdated_events']} 个")
        report.append(f"  • 过时属性: {summary['outdated_attributes']} 个")
        report.append("")
        
        # 问题详情
        if self.results["issues_found"]:
            report.append("⚠️ 发现的问题:")
            for file_issue in self.results["issues_found"]:
                report.append(f"\n📄 {file_issue['file']}:")
                for issue in file_issue["issues"]:
                    severity_icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(issue["severity"], "•")
                    report.append(f"  {severity_icon} 第{issue['line']}行: {issue['message']}")
                
                if file_issue["suggestions"]:
                    report.append("  💡 建议:")
                    for suggestion in file_issue["suggestions"]:
                        report.append(f"    - {suggestion}")
        else:
            report.append("✅ 未发现兼容性问题")
        
        report.append("")
        report.append("🔧 修复建议:")
        report.append("1. 更新bootstrap-zh-CN.js为Bootstrap 5兼容版本")
        report.append("2. 将jQuery代码重写为原生JavaScript")
        report.append("3. 更新data-*属性为data-bs-*格式")
        report.append("4. 更新Bootstrap API调用方式")
        report.append("5. 测试所有交互功能")
        
        report.append("")
        report.append("=" * 70)
        
        return "\n".join(report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Bootstrap 5兼容性检查工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = Bootstrap5CompatibilityChecker(args.project_root)
    
    # 运行检查
    results = checker.run_compatibility_check()
    
    # 生成报告
    checker.generate_report()
    
    # 返回状态码
    if results["summary"]["total_issues"] > 0:
        print(f"\n⚠️ 发现 {results['summary']['total_issues']} 个兼容性问题")
        return 1
    else:
        print("\n✅ 未发现兼容性问题")
        return 0


if __name__ == "__main__":
    exit(main())
