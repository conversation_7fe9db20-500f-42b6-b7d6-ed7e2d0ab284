#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5.3.6 升级工具
=======================

自动升级StudentsCMSSP项目中的Bootstrap到5.3.6版本

功能特性:
1. 自动备份现有文件
2. 从官方发布包复制文件
3. 验证文件完整性
4. 生成升级报告
5. 支持回滚操作
"""

import os
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
import json
import logging


class Bootstrap536Upgrader:
    """Bootstrap 5.3.6 升级器"""
    
    def __init__(self, project_root=".", bootstrap_source="D:/bootstrap-5.3.6/bootstrap-5.3.6"):
        self.project_root = Path(project_root).resolve()
        self.bootstrap_source = Path(bootstrap_source)
        self.bootstrap_target = self.project_root / "app" / "static" / "bootstrap"
        
        # 升级配置
        self.upgrade_config = {
            "backup_enabled": True,
            "include_source_maps": True,
            "include_rtl": True,
            "include_modular": True,
            "verify_checksums": True
        }
        
        # 文件映射
        self.file_mappings = {
            "css": {
                "bootstrap.min.css": "必需 - 主要CSS文件",
                "bootstrap.css": "可选 - 开发版CSS",
                "bootstrap.min.css.map": "可选 - CSS source map",
                "bootstrap.css.map": "可选 - CSS source map",
                "bootstrap.rtl.min.css": "可选 - RTL支持",
                "bootstrap.rtl.css": "可选 - RTL开发版",
                "bootstrap-grid.min.css": "可选 - 网格系统",
                "bootstrap-utilities.min.css": "可选 - 工具类",
                "bootstrap-reboot.min.css": "可选 - CSS重置"
            },
            "js": {
                "bootstrap.bundle.min.js": "必需 - 主要JS文件",
                "bootstrap.bundle.js": "可选 - 开发版JS",
                "bootstrap.bundle.min.js.map": "可选 - JS source map",
                "bootstrap.bundle.js.map": "可选 - JS source map",
                "bootstrap.min.js": "可选 - 不含Popper版本",
                "bootstrap.js": "可选 - 开发版不含Popper",
                "bootstrap.esm.min.js": "可选 - ES模块版本"
            }
        }
        
        # 升级结果
        self.upgrade_result = {
            "timestamp": datetime.now().isoformat(),
            "source_path": str(self.bootstrap_source),
            "target_path": str(self.bootstrap_target),
            "backup_path": None,
            "files_copied": [],
            "files_skipped": [],
            "errors": [],
            "checksums": {}
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def verify_source(self):
        """验证Bootstrap源文件"""
        if not self.bootstrap_source.exists():
            raise FileNotFoundError(f"Bootstrap源目录不存在: {self.bootstrap_source}")
        
        dist_dir = self.bootstrap_source / "dist"
        if not dist_dir.exists():
            raise FileNotFoundError(f"Bootstrap dist目录不存在: {dist_dir}")
        
        css_dir = dist_dir / "css"
        js_dir = dist_dir / "js"
        
        if not css_dir.exists() or not js_dir.exists():
            raise FileNotFoundError("Bootstrap CSS或JS目录不存在")
        
        # 检查关键文件
        required_files = [
            css_dir / "bootstrap.min.css",
            js_dir / "bootstrap.bundle.min.js"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                raise FileNotFoundError(f"关键文件不存在: {file_path}")
        
        self.logger.info(f"✅ Bootstrap源文件验证通过: {self.bootstrap_source}")
        return True
    
    def create_backup(self):
        """创建备份"""
        if not self.upgrade_config["backup_enabled"]:
            return None
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.project_root / "bootstrap_backup" / f"backup_{timestamp}"
        
        if self.bootstrap_target.exists():
            backup_dir.mkdir(parents=True, exist_ok=True)
            shutil.copytree(self.bootstrap_target, backup_dir / "bootstrap", dirs_exist_ok=True)
            
            self.upgrade_result["backup_path"] = str(backup_dir)
            self.logger.info(f"📋 备份已创建: {backup_dir}")
            return backup_dir
        
        return None
    
    def calculate_checksum(self, file_path):
        """计算文件校验和"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def copy_files(self):
        """复制Bootstrap文件"""
        dist_dir = self.bootstrap_source / "dist"
        
        # 确保目标目录存在
        self.bootstrap_target.mkdir(parents=True, exist_ok=True)
        (self.bootstrap_target / "css").mkdir(exist_ok=True)
        (self.bootstrap_target / "js").mkdir(exist_ok=True)
        
        # 复制CSS文件
        css_source = dist_dir / "css"
        css_target = self.bootstrap_target / "css"
        
        for file_name, description in self.file_mappings["css"].items():
            source_file = css_source / file_name
            target_file = css_target / file_name
            
            if source_file.exists():
                # 应用过滤规则
                if self._should_copy_file(file_name):
                    try:
                        shutil.copy2(source_file, target_file)
                        
                        # 计算校验和
                        if self.upgrade_config["verify_checksums"]:
                            checksum = self.calculate_checksum(target_file)
                            self.upgrade_result["checksums"][str(target_file)] = checksum
                        
                        self.upgrade_result["files_copied"].append({
                            "file": file_name,
                            "type": "css",
                            "description": description,
                            "size": target_file.stat().st_size
                        })
                        
                        self.logger.info(f"✅ 已复制: {file_name} ({description})")
                        
                    except Exception as e:
                        error_msg = f"复制CSS文件失败 {file_name}: {str(e)}"
                        self.upgrade_result["errors"].append(error_msg)
                        self.logger.error(f"❌ {error_msg}")
                else:
                    self.upgrade_result["files_skipped"].append({
                        "file": file_name,
                        "type": "css",
                        "reason": "配置过滤"
                    })
            else:
                self.logger.warning(f"⚠️ 源文件不存在: {file_name}")
        
        # 复制JS文件
        js_source = dist_dir / "js"
        js_target = self.bootstrap_target / "js"
        
        for file_name, description in self.file_mappings["js"].items():
            source_file = js_source / file_name
            target_file = js_target / file_name
            
            if source_file.exists():
                if self._should_copy_file(file_name):
                    try:
                        shutil.copy2(source_file, target_file)
                        
                        # 计算校验和
                        if self.upgrade_config["verify_checksums"]:
                            checksum = self.calculate_checksum(target_file)
                            self.upgrade_result["checksums"][str(target_file)] = checksum
                        
                        self.upgrade_result["files_copied"].append({
                            "file": file_name,
                            "type": "js",
                            "description": description,
                            "size": target_file.stat().st_size
                        })
                        
                        self.logger.info(f"✅ 已复制: {file_name} ({description})")
                        
                    except Exception as e:
                        error_msg = f"复制JS文件失败 {file_name}: {str(e)}"
                        self.upgrade_result["errors"].append(error_msg)
                        self.logger.error(f"❌ {error_msg}")
                else:
                    self.upgrade_result["files_skipped"].append({
                        "file": file_name,
                        "type": "js",
                        "reason": "配置过滤"
                    })
            else:
                self.logger.warning(f"⚠️ 源文件不存在: {file_name}")
    
    def _should_copy_file(self, file_name):
        """判断是否应该复制文件"""
        # 必需文件总是复制
        if file_name in ["bootstrap.min.css", "bootstrap.bundle.min.js"]:
            return True
        
        # Source maps
        if file_name.endswith(".map") and not self.upgrade_config["include_source_maps"]:
            return False
        
        # RTL文件
        if "rtl" in file_name and not self.upgrade_config["include_rtl"]:
            return False
        
        # 模块化文件
        if file_name.startswith("bootstrap-") and not self.upgrade_config["include_modular"]:
            return False
        
        # 开发版文件 (非.min版本)
        if not file_name.endswith(".min.css") and not file_name.endswith(".min.js") and not file_name.endswith(".map"):
            if file_name.endswith(".css") or file_name.endswith(".js"):
                return False
        
        return True
    
    def verify_upgrade(self):
        """验证升级结果"""
        errors = []
        
        # 检查关键文件
        required_files = [
            self.bootstrap_target / "css" / "bootstrap.min.css",
            self.bootstrap_target / "js" / "bootstrap.bundle.min.js"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                errors.append(f"关键文件缺失: {file_path}")
            elif file_path.stat().st_size == 0:
                errors.append(f"文件为空: {file_path}")
        
        if errors:
            self.upgrade_result["errors"].extend(errors)
            return False
        
        self.logger.info("✅ 升级验证通过")
        return True
    
    def generate_report(self):
        """生成升级报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.project_root / f"bootstrap_upgrade_report_{timestamp}.json"
        
        # 添加统计信息
        self.upgrade_result["statistics"] = {
            "total_files_copied": len(self.upgrade_result["files_copied"]),
            "total_files_skipped": len(self.upgrade_result["files_skipped"]),
            "total_errors": len(self.upgrade_result["errors"]),
            "css_files": len([f for f in self.upgrade_result["files_copied"] if f["type"] == "css"]),
            "js_files": len([f for f in self.upgrade_result["files_copied"] if f["type"] == "js"]),
            "total_size": sum(f["size"] for f in self.upgrade_result["files_copied"])
        }
        
        # 保存JSON报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.upgrade_result, f, indent=2, ensure_ascii=False)
        
        # 生成文本摘要
        summary_file = self.project_root / f"bootstrap_upgrade_summary_{timestamp}.txt"
        summary = self._generate_text_summary()
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        self.logger.info(f"📊 升级报告已保存: {report_file}")
        self.logger.info(f"📊 升级摘要已保存: {summary_file}")
        
        return report_file, summary_file
    
    def _generate_text_summary(self):
        """生成文本摘要"""
        stats = self.upgrade_result["statistics"]
        
        summary = []
        summary.append("=" * 60)
        summary.append("Bootstrap 5.3.6 升级报告")
        summary.append("=" * 60)
        summary.append(f"升级时间: {self.upgrade_result['timestamp'][:19]}")
        summary.append(f"源路径: {self.upgrade_result['source_path']}")
        summary.append(f"目标路径: {self.upgrade_result['target_path']}")
        
        if self.upgrade_result["backup_path"]:
            summary.append(f"备份路径: {self.upgrade_result['backup_path']}")
        
        summary.append("")
        summary.append("📊 升级统计:")
        summary.append(f"  • 复制文件: {stats['total_files_copied']} 个")
        summary.append(f"  • 跳过文件: {stats['total_files_skipped']} 个")
        summary.append(f"  • CSS文件: {stats['css_files']} 个")
        summary.append(f"  • JS文件: {stats['js_files']} 个")
        summary.append(f"  • 总大小: {stats['total_size']:,} 字节")
        summary.append(f"  • 错误数量: {stats['total_errors']} 个")
        
        if self.upgrade_result["files_copied"]:
            summary.append("")
            summary.append("✅ 已复制的文件:")
            for file_info in self.upgrade_result["files_copied"]:
                size_kb = file_info["size"] / 1024
                summary.append(f"  • {file_info['file']} ({size_kb:.1f}KB) - {file_info['description']}")
        
        if self.upgrade_result["errors"]:
            summary.append("")
            summary.append("❌ 错误信息:")
            for error in self.upgrade_result["errors"]:
                summary.append(f"  • {error}")
        
        summary.append("")
        summary.append("🎉 升级完成！")
        summary.append("建议:")
        summary.append("1. 重启应用程序")
        summary.append("2. 测试关键页面功能")
        summary.append("3. 检查浏览器控制台")
        summary.append("4. 验证响应式布局")
        
        return "\n".join(summary)
    
    def run_upgrade(self):
        """运行完整升级流程"""
        try:
            self.logger.info("🚀 开始Bootstrap 5.3.6升级...")
            
            # 1. 验证源文件
            self.verify_source()
            
            # 2. 创建备份
            backup_path = self.create_backup()
            
            # 3. 复制文件
            self.copy_files()
            
            # 4. 验证升级
            if not self.verify_upgrade():
                raise Exception("升级验证失败")
            
            # 5. 生成报告
            report_file, summary_file = self.generate_report()
            
            self.logger.info("✅ Bootstrap 5.3.6升级完成！")
            
            # 显示摘要
            with open(summary_file, 'r', encoding='utf-8') as f:
                print(f.read())
            
            return True
            
        except Exception as e:
            error_msg = f"升级失败: {str(e)}"
            self.upgrade_result["errors"].append(error_msg)
            self.logger.error(f"❌ {error_msg}")
            
            # 即使失败也生成报告
            try:
                self.generate_report()
            except:
                pass
            
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Bootstrap 5.3.6 升级工具")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--bootstrap-source", default="D:/bootstrap-5.3.6/bootstrap-5.3.6", help="Bootstrap源目录")
    parser.add_argument("--no-backup", action="store_true", help="不创建备份")
    parser.add_argument("--no-source-maps", action="store_true", help="不包含source maps")
    parser.add_argument("--no-rtl", action="store_true", help="不包含RTL文件")
    parser.add_argument("--no-modular", action="store_true", help="不包含模块化文件")
    
    args = parser.parse_args()
    
    # 创建升级器
    upgrader = Bootstrap536Upgrader(args.project_root, args.bootstrap_source)
    
    # 配置选项
    if args.no_backup:
        upgrader.upgrade_config["backup_enabled"] = False
    if args.no_source_maps:
        upgrader.upgrade_config["include_source_maps"] = False
    if args.no_rtl:
        upgrader.upgrade_config["include_rtl"] = False
    if args.no_modular:
        upgrader.upgrade_config["include_modular"] = False
    
    # 运行升级
    success = upgrader.run_upgrade()
    
    if success:
        print("\n🎉 升级成功完成！")
    else:
        print("\n❌ 升级失败，请查看错误信息")
        exit(1)


if __name__ == "__main__":
    main()
