2025-06-19 18:22:34,972 INFO: 应用启动 - PID: 11808 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 18:22:39,273 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:22:42,421 INFO: 查看库存详情: ID=81, 批次号=B20250613e30b2c [in C:\StudentsCMSSP\app\routes\inventory.py:444]
2025-06-19 18:23:00,649 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-19 18:23:00,649 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-19 18:23:00,649 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-19 18:23:00,649 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-19 18:23:00,649 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-19 18:23:00,668 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-19 18:23:00,669 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,669 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,669 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,670 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,670 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,670 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,670 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,670 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,671 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,674 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,674 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,675 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,676 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,676 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,677 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,677 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,678 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,678 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,679 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,679 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,682 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,683 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,683 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,683 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,683 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,684 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,684 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,684 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,684 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,685 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,685 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,685 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,685 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,685 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,686 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,686 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,686 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,686 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,686 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,687 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,687 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,687 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,687 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,688 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,688 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,688 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,688 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,689 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,689 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,689 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,690 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,696 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,697 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,697 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,697 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,697 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,698 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:23:00,704 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-19 18:23:00,706 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-19 18:23:00,707 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-19 18:23:00,714 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-19 18:23:00,730 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-19 18:23:00,736 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,739 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,742 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,746 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,746 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,746 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,751 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,752 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,755 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,756 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,771 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,773 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,776 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:23:00,776 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-19 18:23:03,747 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-19 18:23:03,747 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-19 18:23:03,755 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-19 18:23:03,755 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-19 18:26:14,648 INFO: 用户 guest_demo 正在上传入库单据，不进行权限检查 [in C:\StudentsCMSSP\app\routes\stock_in.py:2221]
2025-06-19 18:26:14,889 INFO: 文档 19 关联了 1 个批次 [in C:\StudentsCMSSP\app\routes\stock_in.py:2346]
2025-06-19 18:34:10,639 INFO: 批次编辑器保存 - 入库单ID: 104 [in C:\StudentsCMSSP\app\routes\stock_in.py:924]
2025-06-19 18:34:10,639 INFO: 选中的项目: ['300', '301', '302'] [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-19 18:34:10,639 INFO: 单价字段 - unit_price_300: 7 [in C:\StudentsCMSSP\app\routes\stock_in.py:928]
2025-06-19 18:34:10,639 INFO: 单价字段 - unit_price_301: 8 [in C:\StudentsCMSSP\app\routes\stock_in.py:928]
2025-06-19 18:34:10,639 INFO: 单价字段 - unit_price_302: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:928]
2025-06-19 18:34:10,639 INFO: 项目 300 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-19 18:34:10,639 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-19 18:34:10,639 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-19 18:34:10,639 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-19 18:34:10,639 INFO:   - 单价: 7 [in C:\StudentsCMSSP\app\routes\stock_in.py:959]
2025-06-19 18:34:10,654 INFO:   - 生产日期: 2025-06-19 [in C:\StudentsCMSSP\app\routes\stock_in.py:960]
2025-06-19 18:34:10,654 INFO:   - 过期日期: 2025-12-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:961]
2025-06-19 18:34:10,675 INFO: 项目 301 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-19 18:34:10,681 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-19 18:34:10,681 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-19 18:34:10,682 INFO:   - 数量: 47.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-19 18:34:10,682 INFO:   - 单价: 8 [in C:\StudentsCMSSP\app\routes\stock_in.py:959]
2025-06-19 18:34:10,683 INFO:   - 生产日期: 2025-06-19 [in C:\StudentsCMSSP\app\routes\stock_in.py:960]
2025-06-19 18:34:10,685 INFO:   - 过期日期: 2025-07-19 [in C:\StudentsCMSSP\app\routes\stock_in.py:961]
2025-06-19 18:34:10,695 INFO: 项目 302 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-19 18:34:10,696 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-19 18:34:10,696 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-19 18:34:10,696 INFO:   - 数量: 108.3 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-19 18:34:10,697 INFO:   - 单价: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:959]
2025-06-19 18:34:10,698 INFO:   - 生产日期: 2025-06-19 [in C:\StudentsCMSSP\app\routes\stock_in.py:960]
2025-06-19 18:34:10,698 INFO:   - 过期日期: 2025-12-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:961]
2025-06-19 18:36:34,271 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-19 18:36:34,272 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-19 18:36:34,272 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-19 18:36:34,274 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-19 18:36:34,274 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-19 18:36:34,277 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-19 18:36:34,277 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,277 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,277 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,278 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,278 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,278 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,279 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,279 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,279 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,280 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,280 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,281 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,281 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,281 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,281 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,285 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,285 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,285 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,286 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,286 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,286 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,289 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,290 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,292 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,292 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,293 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,293 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,293 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,294 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,294 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,295 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,295 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,296 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,296 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,297 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,297 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,298 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,298 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,299 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,299 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,299 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,299 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,300 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,300 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,304 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,305 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,307 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,307 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,309 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,310 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,310 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,311 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,311 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,312 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,313 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,313 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,314 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:36:34,317 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-19 18:36:34,317 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-19 18:36:34,318 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-19 18:36:34,322 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-19 18:36:34,327 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-19 18:36:34,330 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,333 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,334 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,336 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,337 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,337 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,337 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,343 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,345 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,345 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,346 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,346 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,350 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,355 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,355 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,355 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,374 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,376 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,379 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,381 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,384 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:36:34,386 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-19 18:37:16,522 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-19 18:37:16,522 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-19 18:37:16,524 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-19 18:37:16,525 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-19 18:37:25,967 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:37:40,092 INFO: 查看库存详情: ID=81, 批次号=B20250613e30b2c [in C:\StudentsCMSSP\app\routes\inventory.py:444]
2025-06-19 18:37:45,500 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:04,078 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-19 18:38:04,078 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-19 18:38:04,080 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-19 18:38:04,080 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-19 18:38:10,419 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-19', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-19 18:38:10,419 INFO: 查询日期: 2025-06-19, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-19 18:38:10,419 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-19 18:38:10,419 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-19 18:38:20,100 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-19 18:38:20,100 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-19 18:38:20,109 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-19 18:38:20,109 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-19 18:38:26,894 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:34,272 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:36,038 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:37,812 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:39,526 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:41,818 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:44,569 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:47,547 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:56,227 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:38:58,955 INFO: 库存统计页面：为区域 [44] 找到 13 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:993]
2025-06-19 18:39:20,434 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:39:38,023 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:39:42,917 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:40:00,038 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:40:02,708 INFO: 库存汇总查询条件: 状态=正常, 最小数量=0.001 [in C:\StudentsCMSSP\app\routes\inventory.py:310]
2025-06-19 18:40:49,053 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:40:51,662 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 21:03:31,904 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
