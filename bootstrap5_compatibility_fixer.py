#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5 兼容性修复工具
========================

解决Bootstrap 5 + jQuery混合架构的兼容性问题
优化静态资源加载顺序和依赖关系
"""

import os
import re
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List
import logging


class Bootstrap5CompatibilityFixer:
    """Bootstrap 5 兼容性修复工具"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        self.static_root = self.project_root / "app" / "static"
        
        # 修复规则
        self.compatibility_fixes = {
            # jQuery依赖检查和修复
            "jquery_compatibility": [
                {
                    "name": "确保jQuery在Bootstrap之前加载",
                    "pattern": r'(<script[^>]*bootstrap[^>]*></script>)\s*(<script[^>]*jquery[^>]*></script>)',
                    "replacement": r'\2\n\1',
                    "description": "调整jQuery和Bootstrap的加载顺序"
                }
            ],
            
            # Bootstrap 5特定修复
            "bootstrap5_fixes": [
                {
                    "name": "移除过时的data-toggle属性",
                    "pattern": r'data-toggle="([^"]*)"',
                    "replacement": r'data-bs-toggle="\1"',
                    "description": "Bootstrap 5: data-toggle → data-bs-toggle"
                },
                {
                    "name": "移除过时的data-target属性",
                    "pattern": r'data-target="([^"]*)"',
                    "replacement": r'data-bs-target="\1"',
                    "description": "Bootstrap 5: data-target → data-bs-target"
                },
                {
                    "name": "移除过时的data-dismiss属性",
                    "pattern": r'data-dismiss="([^"]*)"',
                    "replacement": r'data-bs-dismiss="\1"',
                    "description": "Bootstrap 5: data-dismiss → data-bs-dismiss"
                }
            ],
            
            # jQuery插件兼容性修复
            "jquery_plugin_fixes": [
                {
                    "name": "修复jQuery事件命名空间",
                    "pattern": r'\.on\([\'"]([^\'\"]*)[\'\"]\s*,',
                    "replacement": r'.on("\1.bs5",',
                    "description": "为jQuery事件添加Bootstrap 5命名空间"
                }
            ]
        }
        
        # 修复结果
        self.fix_results = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "files_processed": [],
            "fixes_applied": [],
            "compatibility_issues_resolved": 0,
            "statistics": {
                "total_files": 0,
                "files_modified": 0,
                "total_fixes": 0
            }
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
    
    def scan_template_files(self) -> List[Path]:
        """扫描模板文件"""
        if not self.templates_root.exists():
            self.logger.error(f"❌ 模板目录不存在: {self.templates_root}")
            return []
        
        template_files = list(self.templates_root.rglob("*.html"))
        self.fix_results["statistics"]["total_files"] = len(template_files)
        self.logger.info(f"📁 发现 {len(template_files)} 个模板文件")
        
        return template_files
    
    def create_backup(self, file_path: Path) -> Path:
        """创建文件备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.project_root / "bootstrap5_compatibility_backup" / timestamp
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        relative_path = file_path.relative_to(self.templates_root)
        backup_file = backup_dir / relative_path
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(file_path, backup_file)
        return backup_file
    
    def check_bootstrap_jquery_usage(self, content: str) -> Dict:
        """检查Bootstrap和jQuery的使用情况"""
        usage = {
            "has_bootstrap": bool(re.search(r'bootstrap', content, re.IGNORECASE)),
            "has_jquery": bool(re.search(r'jquery', content, re.IGNORECASE)),
            "bootstrap_version": "unknown",
            "jquery_plugins": [],
            "data_attributes": []
        }
        
        # 检测Bootstrap版本
        if re.search(r'bootstrap[./]5', content):
            usage["bootstrap_version"] = "5"
        elif re.search(r'bootstrap[./]4', content):
            usage["bootstrap_version"] = "4"
        
        # 检测jQuery插件
        jquery_plugins = ["datatables", "select2", "toastr", "sweetalert"]
        for plugin in jquery_plugins:
            if re.search(plugin, content, re.IGNORECASE):
                usage["jquery_plugins"].append(plugin)
        
        # 检测过时的data属性
        old_attributes = re.findall(r'data-(toggle|target|dismiss)=', content)
        usage["data_attributes"] = list(set(old_attributes))
        
        return usage
    
    def fix_compatibility_in_file(self, file_path: Path) -> Dict:
        """修复单个文件中的兼容性问题"""
        file_result = {
            "file": str(file_path.relative_to(self.project_root)),
            "backup_created": False,
            "fixes_applied": [],
            "usage_info": {},
            "total_changes": 0
        }
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 检查Bootstrap和jQuery使用情况
            usage = self.check_bootstrap_jquery_usage(original_content)
            file_result["usage_info"] = usage
            
            # 如果文件不使用Bootstrap或jQuery，跳过
            if not usage["has_bootstrap"] and not usage["has_jquery"]:
                return file_result
            
            modified_content = original_content
            total_fixes = 0
            
            # 应用jQuery兼容性修复
            if usage["has_jquery"]:
                for fix_rule in self.compatibility_fixes["jquery_compatibility"]:
                    new_content, count = self._apply_fix(modified_content, fix_rule)
                    if count > 0:
                        modified_content = new_content
                        total_fixes += count
                        file_result["fixes_applied"].append({
                            "category": "jquery_compatibility",
                            "name": fix_rule["name"],
                            "count": count,
                            "description": fix_rule["description"]
                        })
            
            # 应用Bootstrap 5修复
            if usage["bootstrap_version"] == "5" or usage["data_attributes"]:
                for fix_rule in self.compatibility_fixes["bootstrap5_fixes"]:
                    new_content, count = self._apply_fix(modified_content, fix_rule)
                    if count > 0:
                        modified_content = new_content
                        total_fixes += count
                        file_result["fixes_applied"].append({
                            "category": "bootstrap5_fixes",
                            "name": fix_rule["name"],
                            "count": count,
                            "description": fix_rule["description"]
                        })
            
            # 应用jQuery插件修复
            if usage["jquery_plugins"]:
                for fix_rule in self.compatibility_fixes["jquery_plugin_fixes"]:
                    # 这个修复比较复杂，暂时跳过自动修复
                    pass
            
            file_result["total_changes"] = total_fixes
            
            # 如果有修改，保存文件
            if modified_content != original_content:
                # 创建备份
                backup_file = self.create_backup(file_path)
                file_result["backup_created"] = True
                
                # 保存修改后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                self.fix_results["statistics"]["files_modified"] += 1
                self.fix_results["statistics"]["total_fixes"] += total_fixes
                self.fix_results["compatibility_issues_resolved"] += total_fixes
                
                self.logger.info(f"✅ 修复完成: {file_path.relative_to(self.templates_root)} ({total_fixes} 处兼容性修改)")
            
        except Exception as e:
            error_msg = f"修复文件失败: {str(e)}"
            self.logger.error(f"❌ {file_path}: {error_msg}")
        
        return file_result
    
    def _apply_fix(self, content: str, fix_rule: Dict) -> tuple:
        """应用单个修复规则"""
        pattern = fix_rule["pattern"]
        replacement = fix_rule["replacement"]
        
        # 计算匹配次数
        matches = re.findall(pattern, content)
        count = len(matches)
        
        if count > 0:
            # 应用替换
            new_content = re.sub(pattern, replacement, content)
            return new_content, count
        
        return content, 0
    
    def generate_compatibility_guide(self) -> str:
        """生成兼容性指南"""
        guide = []
        guide.append("=" * 80)
        guide.append("🔧 Bootstrap 5 + jQuery 兼容性指南")
        guide.append("=" * 80)
        guide.append("")
        
        guide.append("📋 主要兼容性问题:")
        guide.append("1. Bootstrap 5移除了jQuery依赖")
        guide.append("2. data-*属性命名变更")
        guide.append("3. 某些jQuery插件可能不兼容")
        guide.append("")
        
        guide.append("🔧 解决方案:")
        guide.append("")
        guide.append("### 1. 保持jQuery支持")
        guide.append("```html")
        guide.append("<!-- 确保jQuery在Bootstrap之前加载 -->")
        guide.append('<script src="/static/vendor/jquery/jquery.min.js"></script>')
        guide.append('<script src="/static/bootstrap/js/bootstrap.bundle.min.js"></script>')
        guide.append("```")
        guide.append("")
        
        guide.append("### 2. 更新data属性")
        guide.append("```html")
        guide.append("<!-- Bootstrap 4 -->")
        guide.append('<button data-toggle="modal" data-target="#myModal">打开</button>')
        guide.append("")
        guide.append("<!-- Bootstrap 5 -->")
        guide.append('<button data-bs-toggle="modal" data-bs-target="#myModal">打开</button>')
        guide.append("```")
        guide.append("")
        
        guide.append("### 3. jQuery插件兼容性")
        guide.append("```javascript")
        guide.append("// 确保jQuery插件在Bootstrap加载后初始化")
        guide.append("$(document).ready(function() {")
        guide.append("    // DataTables初始化")
        guide.append("    $('#myTable').DataTable();")
        guide.append("    ")
        guide.append("    // Select2初始化")
        guide.append("    $('.select2').select2();")
        guide.append("});")
        guide.append("```")
        guide.append("")
        
        guide.append("### 4. 事件处理兼容性")
        guide.append("```javascript")
        guide.append("// Bootstrap 5事件命名")
        guide.append("$('#myModal').on('show.bs.modal', function() {")
        guide.append("    console.log('模态框显示');")
        guide.append("});")
        guide.append("```")
        guide.append("")
        
        guide.append("🎯 最佳实践:")
        guide.append("1. 渐进式迁移：先确保基本功能正常")
        guide.append("2. 测试驱动：每个修改都要测试")
        guide.append("3. 文档更新：更新开发文档")
        guide.append("4. 团队培训：确保团队了解变更")
        guide.append("")
        
        guide.append("=" * 80)
        
        return "\n".join(guide)
    
    def run_compatibility_fixes(self) -> Dict:
        """运行兼容性修复"""
        self.logger.info("🔧 开始Bootstrap 5兼容性修复...")
        
        # 扫描模板文件
        template_files = self.scan_template_files()
        
        if not template_files:
            self.logger.warning("⚠️ 未找到模板文件")
            return self.fix_results
        
        # 修复每个文件
        for i, file_path in enumerate(template_files, 1):
            self.logger.info(f"🔍 [{i}/{len(template_files)}] 检查: {file_path.relative_to(self.templates_root)}")
            
            file_result = self.fix_compatibility_in_file(file_path)
            self.fix_results["files_processed"].append(file_result)
            
            if file_result["fixes_applied"]:
                self.fix_results["fixes_applied"].append(file_result)
        
        self.logger.info("✅ Bootstrap 5兼容性修复完成")
        return self.fix_results
    
    def generate_fix_report(self) -> str:
        """生成修复报告"""
        stats = self.fix_results["statistics"]
        
        report = []
        report.append("=" * 80)
        report.append("🔧 Bootstrap 5兼容性修复报告")
        report.append("=" * 80)
        report.append(f"修复时间: {self.fix_results['timestamp'][:19]}")
        report.append(f"项目路径: {self.fix_results['project_root']}")
        report.append("")
        
        # 修复统计
        report.append("📊 修复统计:")
        report.append(f"  • 处理文件: {stats['total_files']} 个")
        report.append(f"  • 修改文件: {stats['files_modified']} 个")
        report.append(f"  • 兼容性修复: {self.fix_results['compatibility_issues_resolved']} 处")
        report.append("")
        
        # 修复详情
        if self.fix_results["fixes_applied"]:
            report.append("✅ 修复的文件:")
            for file_result in self.fix_results["fixes_applied"]:
                report.append(f"\n📄 {file_result['file']}:")
                usage = file_result["usage_info"]
                if usage.get("bootstrap_version"):
                    report.append(f"  Bootstrap版本: {usage['bootstrap_version']}")
                if usage.get("jquery_plugins"):
                    report.append(f"  jQuery插件: {', '.join(usage['jquery_plugins'])}")
                
                for fix in file_result["fixes_applied"]:
                    report.append(f"  • {fix['name']}: {fix['count']} 处 - {fix['description']}")
        else:
            report.append("ℹ️ 未发现需要修复的兼容性问题")
        
        report.append("")
        report.append("🎉 兼容性修复完成!")
        report.append("建议:")
        report.append("1. 测试所有修复的页面功能")
        report.append("2. 验证jQuery插件正常工作")
        report.append("3. 检查Bootstrap组件交互")
        report.append("4. 在不同浏览器中测试")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_results(self):
        """保存修复结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"bootstrap5_compatibility_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.fix_results, f, indent=2, ensure_ascii=False)
        
        # 保存修复报告
        report_file = self.project_root / f"bootstrap5_compatibility_summary_{timestamp}.txt"
        summary_report = self.generate_fix_report()
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        # 保存兼容性指南
        guide_file = self.project_root / f"bootstrap5_compatibility_guide_{timestamp}.md"
        compatibility_guide = self.generate_compatibility_guide()
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(compatibility_guide)
        
        self.logger.info(f"📊 详细报告已保存: {json_file}")
        self.logger.info(f"📊 修复报告已保存: {report_file}")
        self.logger.info(f"📚 兼容性指南已保存: {guide_file}")
        
        # 显示摘要
        print(summary_report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Bootstrap 5兼容性修复工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 创建修复器
    fixer = Bootstrap5CompatibilityFixer(args.project_root)
    
    # 运行修复
    results = fixer.run_compatibility_fixes()
    
    # 保存结果
    fixer.save_results()
    
    # 返回状态
    if results["compatibility_issues_resolved"] > 0:
        print(f"\n🎉 成功修复 {results['compatibility_issues_resolved']} 个兼容性问题")
    else:
        print("\n✅ 未发现需要修复的兼容性问题")


if __name__ == "__main__":
    main()
