{"timestamp": "2025-06-15T18:44:21.677776", "existing_resources": {"core_frameworks": {"bootstrap": ["bootstrap/js/bootstrap.bundle.min.js", "bootstrap/js/bootstrap-zh-CN.js"], "jquery": ["vendor/jquery/jquery.min.js", "vendor/jquery-ui/js/jquery-ui.min.js"], "jquery_plugins": ["vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js"]}, "ui_components": {"datatables": ["vendor/datatables/datatables-zh-CN.js"], "bootstrap_table": ["vendor/bootstrap-table/bootstrap-table.min.js", "vendor/bootstrap-table/bootstrap-table-zh-CN.js"], "notifications": ["vendor/toastr/toastr.min.js", "vendor/toastr/toastr-zh-CN.js", "vendor/sweetalert2/sweetalert2-zh-CN.js"], "date_picker": ["vendor/moment/moment.min.js", "vendor/moment/moment-zh-CN.js", "js/datepicker-zh-CN.js"], "select": ["vendor/select2/select2.min.js", "vendor/select2/select2-zh-CN.js"], "charts": ["vendor/chart-js/chart-zh-CN.js"]}, "custom_scripts": {"core": ["js/main.js", "js/critical-handler-simple.js", "js/event-handler-manager.js"], "forms": ["js/form-validation-zh-CN.js"], "theme": ["js/theme-switcher.js", "js/advanced-theme-features.js"], "mobile": ["js/mobile-enhancements.js", "js/mobile-table-cards.js"], "upload": ["js/file-upload-fix.js", "js/enhanced-image-uploader.js"], "i18n": ["js/i18n.js", "js/process_navigation.js"], "utils": ["js/mock-api-handler.js"]}}, "missing_resources": {"core_frameworks": {}, "ui_components": {}, "custom_scripts": {}}, "template_usage": {"js_references": {"/static/vendor/jquery/jquery.min.js": ["base_widget.html"], "/static/vendor/bootstrap/js/bootstrap.bundle.min.js": ["base_widget.html"], "/static/vendor/jquery-easing/jquery.easing.min.js": ["base_widget.html"], "/static/js/sb-admin-2.min.js": ["base_widget.html"], "https://cdn.jsdelivr.net/npm/chart.js": ["admin\\guide_management\\scenarios.html", "inventory\\statistics.html"], "/static/js/image_uploader.js": ["daily_management\\edit_inspection_new.html"], "https://cdn.jsdelivr.net/npm/sortablejs@1.10.2/Sortable.min.js": ["daily_management\\inspection_templates.html"], "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js": ["daily_management\\public_error.html", "daily_management\\public_inspection_select_date.html"], "https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js": ["food_trace\\qr_scan.html"], "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js": ["food_trace\\qr_test.html"]}, "css_references": {"/static/vendor/bootstrap/css/bootstrap.min.css": ["base_widget.html"], "/static/vendor/fontawesome-free/css/all.min.css": ["base_widget.html"], "/static/css/sb-admin-2.min.css": ["base_widget.html"], "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css": ["daily_management\\public_error.html", "food_trace\\qr_diagnosis.html", "food_trace\\qr_result.html", "food_trace\\qr_test.html", "food_trace\\recipe_trace_result.html"], "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css": ["daily_management\\public_error.html"], "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css": ["food_trace\\qr_diagnosis.html", "food_trace\\qr_result.html", "food_trace\\recipe_trace_result.html"]}, "duplicate_includes": [], "unused_resources": []}, "compatibility_issues": [{"type": "compatibility_warning", "severity": "medium", "title": "Bootstrap 5 + j<PERSON><PERSON>y 混合架构", "description": "Bootstrap 5已移除jQuery依赖，可能导致某些插件行为异常", "recommendation": "考虑迁移到纯Bootstrap 5组件或确保jQuery插件兼容性"}, {"type": "version_check", "severity": "low", "title": "语言包版本匹配", "description": "需确保bootstrap-zh-CN.js与bootstrap.bundle.min.js版本匹配", "recommendation": "验证语言包版本兼容性"}, {"type": "mobile_compatibility", "severity": "low", "title": "移动端适配检查", "description": "移动端增强脚本需要针对性测试", "recommendation": "在移动设备上测试触摸交互功能"}], "optimization_suggestions": [{"type": "caching", "priority": "medium", "title": "静态资源缓存优化", "description": "为静态资源设置长期缓存策略", "action": "配置nginx/Apache设置Cache-Control头", "expected_benefit": "提升回访用户加载速度"}, {"type": "cdn", "priority": "low", "title": "CDN加速优化", "description": "考虑将第三方库迁移到CDN", "action": "使用jsDelivr/unpkg等CDN服务", "expected_benefit": "减少服务器带宽压力，提升全球访问速度"}]}