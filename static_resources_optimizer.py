#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态资源优化分析工具
==================

基于日志分析，优化Flask应用的静态资源加载
解决Bootstrap 5 + jQuery混合架构的兼容性问题
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set
import logging


class StaticResourcesOptimizer:
    """静态资源优化分析工具"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.static_root = self.project_root / "app" / "static"
        self.templates_root = self.project_root / "app" / "templates"
        
        # 资源分类
        self.resource_categories = {
            "core_frameworks": {
                "bootstrap": ["bootstrap/js/bootstrap.bundle.min.js", "bootstrap/js/bootstrap-zh-CN.js"],
                "jquery": ["vendor/jquery/jquery.min.js", "vendor/jquery-ui/js/jquery-ui.min.js"],
                "jquery_plugins": ["vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js"]
            },
            "ui_components": {
                "datatables": ["vendor/datatables/datatables-zh-CN.js"],
                "bootstrap_table": ["vendor/bootstrap-table/bootstrap-table.min.js", "vendor/bootstrap-table/bootstrap-table-zh-CN.js"],
                "notifications": ["vendor/toastr/toastr.min.js", "vendor/toastr/toastr-zh-CN.js", "vendor/sweetalert2/sweetalert2-zh-CN.js"],
                "date_picker": ["vendor/moment/moment.min.js", "vendor/moment/moment-zh-CN.js", "js/datepicker-zh-CN.js"],
                "select": ["vendor/select2/select2.min.js", "vendor/select2/select2-zh-CN.js"],
                "charts": ["vendor/chart-js/chart-zh-CN.js"]
            },
            "custom_scripts": {
                "core": ["js/main.js", "js/critical-handler-simple.js", "js/event-handler-manager.js"],
                "forms": ["js/form-validation-zh-CN.js"],
                "theme": ["js/theme-switcher.js", "js/advanced-theme-features.js"],
                "mobile": ["js/mobile-enhancements.js", "js/mobile-table-cards.js"],
                "upload": ["js/file-upload-fix.js", "js/enhanced-image-uploader.js"],
                "i18n": ["js/i18n.js", "js/process_navigation.js"],
                "utils": ["js/mock-api-handler.js"]
            }
        }
        
        # 兼容性问题检查
        self.compatibility_issues = []
        self.optimization_suggestions = []
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
    
    def analyze_resource_usage(self) -> Dict:
        """分析资源使用情况"""
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "existing_resources": {},
            "missing_resources": {},
            "template_usage": {},
            "compatibility_issues": [],
            "optimization_suggestions": []
        }
        
        # 检查资源文件是否存在
        for category, subcategories in self.resource_categories.items():
            analysis["existing_resources"][category] = {}
            analysis["missing_resources"][category] = {}
            
            for subcategory, files in subcategories.items():
                existing = []
                missing = []
                
                for file_path in files:
                    full_path = self.static_root / file_path
                    if full_path.exists():
                        existing.append(file_path)
                    else:
                        missing.append(file_path)
                
                if existing:
                    analysis["existing_resources"][category][subcategory] = existing
                if missing:
                    analysis["missing_resources"][category][subcategory] = missing
        
        # 分析模板中的资源引用
        analysis["template_usage"] = self.analyze_template_usage()
        
        # 检查兼容性问题
        analysis["compatibility_issues"] = self.check_compatibility_issues()
        
        # 生成优化建议
        analysis["optimization_suggestions"] = self.generate_optimization_suggestions(analysis)
        
        return analysis
    
    def analyze_template_usage(self) -> Dict:
        """分析模板中的资源使用情况"""
        usage = {
            "js_references": {},
            "css_references": {},
            "duplicate_includes": [],
            "unused_resources": []
        }
        
        # 扫描所有模板文件
        template_files = list(self.templates_root.rglob("*.html"))
        
        for template_file in template_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找JS引用
                js_matches = re.findall(r'<script[^>]*src=["\']([^"\']*\.js)["\']', content)
                for js_file in js_matches:
                    if js_file not in usage["js_references"]:
                        usage["js_references"][js_file] = []
                    usage["js_references"][js_file].append(str(template_file.relative_to(self.templates_root)))
                
                # 查找CSS引用
                css_matches = re.findall(r'<link[^>]*href=["\']([^"\']*\.css)["\']', content)
                for css_file in css_matches:
                    if css_file not in usage["css_references"]:
                        usage["css_references"][css_file] = []
                    usage["css_references"][css_file].append(str(template_file.relative_to(self.templates_root)))
                
            except Exception as e:
                self.logger.warning(f"无法读取模板文件 {template_file}: {e}")
        
        # 检查重复引用
        for resource, templates in usage["js_references"].items():
            if len(templates) > 10:  # 如果超过10个模板引用同一资源
                usage["duplicate_includes"].append({
                    "resource": resource,
                    "count": len(templates),
                    "type": "js"
                })
        
        return usage
    
    def check_compatibility_issues(self) -> List[Dict]:
        """检查兼容性问题"""
        issues = []
        
        # 检查Bootstrap 5 + jQuery兼容性
        bootstrap5_exists = (self.static_root / "bootstrap/js/bootstrap.bundle.min.js").exists()
        jquery_exists = (self.static_root / "vendor/jquery/jquery.min.js").exists()
        
        if bootstrap5_exists and jquery_exists:
            issues.append({
                "type": "compatibility_warning",
                "severity": "medium",
                "title": "Bootstrap 5 + jQuery 混合架构",
                "description": "Bootstrap 5已移除jQuery依赖，可能导致某些插件行为异常",
                "recommendation": "考虑迁移到纯Bootstrap 5组件或确保jQuery插件兼容性"
            })
        
        # 检查语言包版本匹配
        bootstrap_zh_exists = (self.static_root / "bootstrap/js/bootstrap-zh-CN.js").exists()
        if bootstrap5_exists and bootstrap_zh_exists:
            issues.append({
                "type": "version_check",
                "severity": "low",
                "title": "语言包版本匹配",
                "description": "需确保bootstrap-zh-CN.js与bootstrap.bundle.min.js版本匹配",
                "recommendation": "验证语言包版本兼容性"
            })
        
        # 检查移动端适配
        mobile_js_exists = (self.static_root / "js/mobile-enhancements.js").exists()
        touch_punch_exists = (self.static_root / "vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js").exists()
        
        if mobile_js_exists or touch_punch_exists:
            issues.append({
                "type": "mobile_compatibility",
                "severity": "low",
                "title": "移动端适配检查",
                "description": "移动端增强脚本需要针对性测试",
                "recommendation": "在移动设备上测试触摸交互功能"
            })
        
        return issues
    
    def generate_optimization_suggestions(self, analysis: Dict) -> List[Dict]:
        """生成优化建议"""
        suggestions = []
        
        # 建议1: 资源合并
        js_count = len(analysis["template_usage"]["js_references"])
        if js_count > 15:
            suggestions.append({
                "type": "performance",
                "priority": "high",
                "title": "资源合并优化",
                "description": f"当前加载{js_count}个JS文件，建议合并减少HTTP请求",
                "action": "使用Webpack/Vite打包工具合并JS文件",
                "expected_benefit": "减少50-70%的HTTP请求量"
            })
        
        # 建议2: 按需加载
        heavy_plugins = ["datatables", "select2", "chart-js"]
        for plugin in heavy_plugins:
            if any(plugin in path for path in analysis["template_usage"]["js_references"].keys()):
                suggestions.append({
                    "type": "performance",
                    "priority": "medium",
                    "title": f"{plugin.title()} 按需加载",
                    "description": f"{plugin}插件较重，建议按需动态加载",
                    "action": f"在需要{plugin}的页面才加载相关JS",
                    "expected_benefit": "减少初始页面加载时间"
                })
        
        # 建议3: 缓存优化
        suggestions.append({
            "type": "caching",
            "priority": "medium",
            "title": "静态资源缓存优化",
            "description": "为静态资源设置长期缓存策略",
            "action": "配置nginx/Apache设置Cache-Control头",
            "expected_benefit": "提升回访用户加载速度"
        })
        
        # 建议4: CDN优化
        suggestions.append({
            "type": "cdn",
            "priority": "low",
            "title": "CDN加速优化",
            "description": "考虑将第三方库迁移到CDN",
            "action": "使用jsDelivr/unpkg等CDN服务",
            "expected_benefit": "减少服务器带宽压力，提升全球访问速度"
        })
        
        return suggestions
    
    def generate_optimization_report(self, analysis: Dict) -> str:
        """生成优化报告"""
        report = []
        report.append("=" * 80)
        report.append("🚀 静态资源优化分析报告")
        report.append("=" * 80)
        report.append(f"分析时间: {analysis['timestamp'][:19]}")
        report.append(f"项目路径: {self.project_root}")
        report.append("")
        
        # 资源概览
        report.append("📊 资源概览:")
        total_js = len(analysis["template_usage"]["js_references"])
        total_css = len(analysis["template_usage"]["css_references"])
        report.append(f"  • JS文件: {total_js} 个")
        report.append(f"  • CSS文件: {total_css} 个")
        report.append("")
        
        # 核心框架状态
        report.append("🔧 核心框架状态:")
        core = analysis["existing_resources"].get("core_frameworks", {})
        if "bootstrap" in core:
            report.append(f"  ✅ Bootstrap: {len(core['bootstrap'])} 个文件")
        if "jquery" in core:
            report.append(f"  ✅ jQuery: {len(core['jquery'])} 个文件")
        report.append("")
        
        # 兼容性问题
        if analysis["compatibility_issues"]:
            report.append("⚠️ 兼容性问题:")
            for issue in analysis["compatibility_issues"]:
                severity_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(issue["severity"], "ℹ️")
                report.append(f"  {severity_icon} {issue['title']}")
                report.append(f"     {issue['description']}")
                report.append(f"     建议: {issue['recommendation']}")
                report.append("")
        
        # 优化建议
        if analysis["optimization_suggestions"]:
            report.append("💡 优化建议:")
            for suggestion in analysis["optimization_suggestions"]:
                priority_icon = {"high": "🔥", "medium": "⚡", "low": "💡"}.get(suggestion["priority"], "📝")
                report.append(f"  {priority_icon} {suggestion['title']} ({suggestion['priority'].upper()})")
                report.append(f"     问题: {suggestion['description']}")
                report.append(f"     方案: {suggestion['action']}")
                report.append(f"     收益: {suggestion['expected_benefit']}")
                report.append("")
        
        # 重复引用检查
        duplicates = analysis["template_usage"]["duplicate_includes"]
        if duplicates:
            report.append("🔄 重复引用检查:")
            for dup in duplicates:
                report.append(f"  • {dup['resource']}: 被{dup['count']}个模板引用")
            report.append("")
        
        # 总结建议
        report.append("🎯 总结建议:")
        report.append("1. 优先解决Bootstrap 5 + jQuery兼容性问题")
        report.append("2. 实施资源合并，减少HTTP请求数量")
        report.append("3. 配置静态资源缓存策略")
        report.append("4. 考虑按需加载重型插件")
        report.append("5. 在移动设备上测试触摸交互功能")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_analysis_results(self, analysis: Dict):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"static_resources_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        # 保存文本报告
        text_file = self.project_root / f"static_resources_report_{timestamp}.txt"
        report = self.generate_optimization_report(analysis)
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"📊 详细分析已保存: {json_file}")
        self.logger.info(f"📊 优化报告已保存: {text_file}")
        
        # 显示报告
        print(report)
    
    def run_analysis(self):
        """运行完整分析"""
        self.logger.info("🚀 开始静态资源优化分析...")
        
        analysis = self.analyze_resource_usage()
        self.save_analysis_results(analysis)
        
        self.logger.info("✅ 静态资源分析完成")
        return analysis


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="静态资源优化分析工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 创建分析器
    optimizer = StaticResourcesOptimizer(args.project_root)
    
    # 运行分析
    results = optimizer.run_analysis()
    
    # 输出关键指标
    js_count = len(results["template_usage"]["js_references"])
    issues_count = len(results["compatibility_issues"])
    suggestions_count = len(results["optimization_suggestions"])
    
    print(f"\n📈 分析完成:")
    print(f"  • JS文件数量: {js_count}")
    print(f"  • 兼容性问题: {issues_count}")
    print(f"  • 优化建议: {suggestions_count}")


if __name__ == "__main__":
    main()
