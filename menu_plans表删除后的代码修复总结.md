# menu_plans表删除后的代码修复总结

## 问题描述

用户在查看入库食材详情时遇到数据库错误：
```
(pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'menu_plans' 无效。 (208)")
```

## 根本原因

`menu_plans`表已经被删除，但代码中仍有SQL查询引用该表，导致数据库查询失败。

## 修复的文件

### 1. app/routes/stock_in_detail.py

**问题**：查询关联的消耗计划时引用了已删除的`menu_plans`表

**修复前**：
```sql
SELECT
    cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
    a.name as area_name, a.id as area_id
FROM
    stock_out_items soi
JOIN
    stock_outs so ON soi.stock_out_id = so.id
JOIN
    consumption_plans cp ON so.consumption_plan_id = cp.id
JOIN
    menu_plans mp ON cp.menu_plan_id = mp.id  -- 这里引用了已删除的表
JOIN
    administrative_areas a ON mp.area_id = a.id
WHERE
    soi.batch_number = :batch_number
```

**修复后**：
```sql
SELECT
    cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
    a.name as area_name, a.id as area_id
FROM
    stock_out_items soi
JOIN
    stock_outs so ON soi.stock_out_id = so.id
JOIN
    consumption_plans cp ON so.consumption_plan_id = cp.id
JOIN
    administrative_areas a ON cp.area_id = a.id  -- 直接使用cp.area_id
WHERE
    soi.batch_number = :batch_number
```

### 2. app/routes/ingredient.py

**问题**：获取食材溯源信息时引用了已删除的`menu_plans`和`menu_recipes`表

**修复前**：
```sql
SELECT DISTINCT
    soi.batch_number,
    soi.quantity as consumed_quantity,
    soi.unit,
    so.stock_out_date,
    cp.consumption_date,
    cp.meal_type,
    cp.diners_count,
    mp.plan_date,
    mr.recipe_id,
    r.name as recipe_name,
    r.category as recipe_category,
    aa.name as area_name,
    s.name as supplier_name,
    s.contact_person as supplier_contact,
    inv.production_date,
    inv.expiry_date
FROM stock_out_items soi
JOIN stock_outs so ON soi.stock_out_id = so.id
JOIN warehouses w ON so.warehouse_id = w.id
LEFT JOIN consumption_plans cp ON so.consumption_plan_id = cp.id
LEFT JOIN menu_plans mp ON cp.menu_plan_id = mp.id  -- 已删除的表
LEFT JOIN menu_recipes mr ON mp.id = mr.menu_plan_id  -- 已删除的表
LEFT JOIN recipes r ON mr.recipe_id = r.id
LEFT JOIN recipe_ingredients ri ON r.id = ri.recipe_id AND ri.ingredient_id = :ingredient_id
LEFT JOIN administrative_areas aa ON w.area_id = aa.id
LEFT JOIN inventories inv ON soi.batch_number = inv.batch_number AND inv.ingredient_id = :ingredient_id
LEFT JOIN suppliers s ON inv.supplier_id = s.id
WHERE soi.ingredient_id = :ingredient_id
AND w.area_id IN ({})
AND so.status = '已出库'
AND ri.ingredient_id IS NOT NULL
```

**修复后**：
```sql
SELECT DISTINCT
    soi.batch_number,
    soi.quantity as consumed_quantity,
    soi.unit,
    so.stock_out_date,
    cp.consumption_date,
    cp.meal_type,
    cp.diners_count,
    aa.name as area_name,
    s.name as supplier_name,
    s.contact_person as supplier_contact,
    inv.production_date,
    inv.expiry_date
FROM stock_out_items soi
JOIN stock_outs so ON soi.stock_out_id = so.id
JOIN warehouses w ON so.warehouse_id = w.id
LEFT JOIN consumption_plans cp ON so.consumption_plan_id = cp.id
LEFT JOIN administrative_areas aa ON cp.area_id = aa.id  -- 直接使用cp.area_id
LEFT JOIN inventories inv ON soi.batch_number = inv.batch_number AND inv.ingredient_id = :ingredient_id
LEFT JOIN suppliers s ON inv.supplier_id = s.id
WHERE soi.ingredient_id = :ingredient_id
AND w.area_id IN ({})
AND so.status = '已出库'
```

## 数据库结构变化

### 删除的表
- `menu_plans` - 菜单计划表
- `menu_recipes` - 菜单食谱关联表

### 现有的替代表
- `weekly_menus` - 周菜单表
- `weekly_menu_recipes` - 周菜单食谱关联表
- `consumption_plans` - 消耗计划表（现在直接包含area_id字段）

## 关键变化

1. **消耗计划表结构变化**
   - `ConsumptionPlan`现在直接包含`area_id`字段
   - 不再需要通过`menu_plans`表来获取区域信息

2. **食谱关联方式变化**
   - 原来：`consumption_plans` → `menu_plans` → `menu_recipes` → `recipes`
   - 现在：直接通过周菜单系统管理食谱关联

3. **溯源查询简化**
   - 移除了复杂的菜谱关联查询
   - 专注于基本的消耗轨迹信息

## 修复效果

- ✅ 入库食材详情页面可以正常显示
- ✅ 消耗计划关联查询正常工作
- ✅ 食材溯源信息正常显示
- ✅ 保持了数据完整性和功能完整性

## 注意事项

1. **数据迁移**：确保在删除`menu_plans`表之前，相关数据已经迁移到新的周菜单系统

2. **功能验证**：需要测试所有涉及消耗计划和食材溯源的功能

3. **性能优化**：简化后的查询应该有更好的性能表现

4. **后续开发**：新的功能开发应该基于周菜单系统，不再使用已删除的菜单计划系统

## 总结

通过修复这两个关键文件中的SQL查询，解决了`menu_plans`表删除后的代码兼容性问题。修复后的代码：

- 使用`consumption_plans.area_id`直接获取区域信息
- 简化了查询逻辑，提高了性能
- 保持了功能的完整性
- 为后续基于周菜单系统的开发奠定了基础
