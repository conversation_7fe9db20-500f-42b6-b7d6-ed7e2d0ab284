{"migration_info": {"timestamp": "2025-06-15T16:33:23.223718", "bootstrap_version": "5.3.6", "project_root": "C:\\StudentsCMSSP", "dry_run": false}, "statistics": {"files_processed": 362, "files_modified": 315, "css_replacements": 0, "js_replacements": 0, "class_replacements": 2806, "attribute_replacements": 547, "errors": [], "js_code_replacements": 10, "complex_component_warnings": ["app\\templates\\_formhelpers.html:83 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\_formhelpers.html:102 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\admin\\role_permissions.html:53 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\admin\\role_permissions.html:61 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\login.html:21 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\login.html:37 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:170 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:191 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:211 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:229 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:249 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\auth\\register.html:267 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\consumption_plan\\super_editor.html:392 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\financial\\vouchers\\edit_professional.html:457 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\food_sample\\create.html:121 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\food_trace\\sample_management.html:95 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\food_trace\\sample_management.html:106 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\food_trace\\sample_management.html:123 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\includes\\form_helpers.html:87 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\inventory_alert\\batch_create_requisition.html:72 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\inventory_alert\\create_requisition.html:88 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\product_batch\\adjust_products.html:82 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\product_batch\\index.html:36 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\product_batch\\select_ingredients.html:51 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\product_batch\\set_attributes.html:64 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\product_batch\\set_attributes.html:105 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\purchase_order\\index.html:248 - 需要手动重构：移除 .input-group-prepend 包裹层，直接使用按钮", "app\\templates\\security\\dashboard.html:110 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor.html:140 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor.html:156 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_simplified.html:490 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_simplified.html:506 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_simplified.html:517 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_step1.html:207 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_step1.html:223 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_step2.html:262 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_step2.html:276 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\batch_editor_step2.html:290 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\edit.html:281 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\edit.html:386 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\stock_in\\edit.html:661 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\supplier\\form.html:133 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\supplier\\product_form.html:65 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\supplier\\product_form.html:124 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\supplier\\product_form.html:142 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\supplier\\school_form.html:52 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\weekly_menu\\plan.html:547 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\weekly_menu\\plan_time_aware.html:306 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮", "app\\templates\\weekly_menu\\plan_v2.html:617 - 需要手动重构：移除 .input-group-append 包裹层，直接使用按钮"]}, "file_mappings": {"css_replacements": {"bootstrap/css/bootstrap.min.css": "bootstrap/css/bootstrap.min.css", "bootstrap/css/bootstrap.css": "bootstrap/css/bootstrap.css", "vendor/bootstrap/css/bootstrap.min.css": "bootstrap/css/bootstrap.min.css", "vendor/datatables/css/dataTables.bootstrap4.min.css": "vendor/datatables/css/dataTables.bootstrap5.min.css", "vendor/select2/css/select2-bootstrap4.min.css": "vendor/select2/css/select2-bootstrap5.min.css"}, "js_replacements": {"bootstrap/js/bootstrap.bundle.min.js": "bootstrap/js/bootstrap.bundle.min.js", "bootstrap/js/bootstrap.min.js": "bootstrap/js/bootstrap.min.js", "vendor/bootstrap/js/bootstrap.bundle.min.js": "bootstrap/js/bootstrap.bundle.min.js"}, "class_mappings": {"text-left": "text-start", "text-right": "text-end", "float-left": "float-start", "float-right": "float-end", "ml-auto": "ms-auto", "mr-auto": "me-auto", "ml-": "ms-", "mr-": "me-", "pl-": "ps-", "pr-": "pe-", "form-group": "mb-3", "form-row": "row g-3", "input-group-append": "", "input-group-prepend": "", "card-deck": "row row-cols-1 row-cols-md-3 g-4", "jumbotron": "p-5 mb-4 bg-light rounded-3", "media": "d-flex", "sr-only": "visually-hidden", "font-weight-": "fw-", "font-italic": "fst-italic", "font-weight-bold": "fw-bold", "font-weight-normal": "fw-normal", "font-weight-light": "fw-light", "no-gutters": "g-0", "border-left": "border-start", "border-right": "border-end", "left-": "start-", "right-": "end-"}, "attribute_mappings": {"data-toggle": "data-bs-toggle", "data-target": "data-bs-target", "data-dismiss": "data-bs-dismiss", "data-slide": "data-bs-slide", "data-slide-to": "data-bs-slide-to", "data-ride": "data-bs-ride", "data-interval": "data-bs-interval", "data-keyboard": "data-bs-keyboard", "data-backdrop": "data-bs-backdrop", "data-spy": "data-bs-spy", "data-offset": "data-bs-offset"}}, "recommendations": ["建议在部署前进行全面测试", "检查所有页面的布局和功能是否正常"]}