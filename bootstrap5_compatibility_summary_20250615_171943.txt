======================================================================
Bootstrap 5 兼容性检查报告
======================================================================
检查时间: 2025-06-15T17:19:43
项目路径: C:\StudentsCMSSP

📊 检查统计:
  • 检查文件: 413 个
  • 有问题文件: 155 个
  • 总问题数: 2258 个

🔍 问题分类:
  • jQuery依赖: 2067 个
  • Bootstrap 4 API: 81 个
  • 过时事件: 43 个
  • 过时属性: 67 个

⚠️ 发现的问题:

📄 app\static\bootstrap\js\bootstrap.bundle.js:
  ⚠️ 第5508行: 过时的事件名: hide.bs.
  ⚠️ 第5809行: 过时的事件名: hide.bs.
  ⚠️ 第402行: 过时的data属性: data-toggle
  ⚠️ 第403行: 过时的data属性: data-toggle
  ⚠️ 第404行: 过时的data属性: data-toggle
  ⚠️ 第405行: 过时的data属性: data-toggle
  ⚠️ 第538行: 过时的data属性: data-toggle
  ⚠️ 第1186行: 过时的data属性: data-toggle
  ⚠️ 第1204行: 过时的data属性: data-toggle
  ⚠️ 第1204行: 过时的data属性: data-toggle
  ⚠️ 第1409行: 过时的data属性: data-toggle
  ⚠️ 第4159行: 过时的data属性: data-toggle
  ⚠️ 第4657行: 过时的data属性: data-toggle
  ⚠️ 第6543行: 过时的data属性: data-toggle
  ⚠️ 第6543行: 过时的data属性: data-toggle
  ⚠️ 第6543行: 过时的data属性: data-toggle
  ⚠️ 第138行: 过时的data属性: data-target
  ⚠️ 第1204行: 过时的data属性: data-target
  ⚠️ 第6425行: 过时的data属性: data-target
  ⚠️ 第254行: 过时的data属性: data-dismiss
  ⚠️ 第4658行: 过时的data属性: data-dismiss
  ⚠️ 第6761行: 过时的data属性: data-dismiss
  ⚠️ 第625行: 过时的data属性: data-slide
  ⚠️ 第625行: 过时的data属性: data-slide
  ⚠️ 第1108行: 过时的data属性: data-slide
  ⚠️ 第626行: 过时的data属性: data-ride
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 更新事件名为Bootstrap 5的格式

📄 app\static\bootstrap\js\bootstrap.bundle.min.js:
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: click.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式

📄 app\static\bootstrap\js\bootstrap.esm.min.js:
  ⚠️ 第6行: 发现jQuery依赖: jQuery(
  ⚠️ 第6行: 发现jQuery依赖: jQuery(
  ⚠️ 第6行: 过时的事件名: show.bs.
  ⚠️ 第6行: 过时的事件名: show.bs.
  ⚠️ 第6行: 过时的事件名: shown.bs.
  ⚠️ 第6行: 过时的事件名: shown.bs.
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: hidden.bs.
  ⚠️ 第6行: 过时的事件名: hidden.bs.
  ⚠️ 第6行: 过时的事件名: click.bs.
  ⚠️ 第6行: 过时的事件名: click.bs.
  ⚠️ 第6行: 过时的事件名: click.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\bootstrap\js\bootstrap.js:
  ⚠️ 第2893行: 过时的事件名: hide.bs.
  ⚠️ 第3194行: 过时的事件名: hide.bs.
  ⚠️ 第403行: 过时的data属性: data-toggle
  ⚠️ 第404行: 过时的data属性: data-toggle
  ⚠️ 第405行: 过时的data属性: data-toggle
  ⚠️ 第406行: 过时的data属性: data-toggle
  ⚠️ 第539行: 过时的data属性: data-toggle
  ⚠️ 第1187行: 过时的data属性: data-toggle
  ⚠️ 第1205行: 过时的data属性: data-toggle
  ⚠️ 第1205行: 过时的data属性: data-toggle
  ⚠️ 第1410行: 过时的data属性: data-toggle
  ⚠️ 第1544行: 过时的data属性: data-toggle
  ⚠️ 第2042行: 过时的data属性: data-toggle
  ⚠️ 第3928行: 过时的data属性: data-toggle
  ⚠️ 第3928行: 过时的data属性: data-toggle
  ⚠️ 第3928行: 过时的data属性: data-toggle
  ⚠️ 第139行: 过时的data属性: data-target
  ⚠️ 第1205行: 过时的data属性: data-target
  ⚠️ 第3810行: 过时的data属性: data-target
  ⚠️ 第255行: 过时的data属性: data-dismiss
  ⚠️ 第2043行: 过时的data属性: data-dismiss
  ⚠️ 第4146行: 过时的data属性: data-dismiss
  ⚠️ 第626行: 过时的data属性: data-slide
  ⚠️ 第626行: 过时的data属性: data-slide
  ⚠️ 第1109行: 过时的data属性: data-slide
  ⚠️ 第627行: 过时的data属性: data-ride
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 更新事件名为Bootstrap 5的格式

📄 app\static\bootstrap\js\bootstrap.min.js:
  ⚠️ 第6行: 过时的事件名: hide.bs.
  ⚠️ 第6行: 过时的事件名: click.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式

📄 app\static\financial\js\professional-voucher-editor.js:
  ⚠️ 第72行: 过时的data属性: data-dismiss
  ⚠️ 第957行: 过时的data属性: data-dismiss
  💡 建议:
    - 将data-*属性更新为data-bs-*格式

📄 app\static\financial\js\voucher-edit.js:
  ⚠️ 第587行: 发现jQuery依赖: $('#subject-tree-modal').modal('show')
  ⚠️ 第821行: 发现jQuery依赖: $('#subject-tree-modal').modal('hide')
  ❌ 第587行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第964行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\auth-helper.js:
  ⚠️ 第15行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第94行: 发现jQuery依赖: $('#user-dropdown').length > 0 || $('.user-panel')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\categorized-ingredient-select.js:
  ⚠️ 第7行: 发现jQuery依赖: $('.categorized-ingredient-select').categorizedIngredientSelect()
  ⚠️ 第33行: 发现jQuery依赖: $('<div class="categorized-ingredient-select-wrapper"></div>')
  ⚠️ 第37行: 发现jQuery依赖: $('<select></select>')
  ⚠️ 第141行: 发现jQuery依赖: $('<optgroup label="' + category.name + '"></optgroup>')
  ⚠️ 第144行: 发现jQuery依赖: $('<option></option>')
  ⚠️ 第158行: 发现jQuery依赖: $('<optgroup label="其他"></optgroup>')
  ⚠️ 第161行: 发现jQuery依赖: $('<option></option>')
  ⚠️ 第11行: 发现jQuery依赖: $.fn.
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\companion-uploader.js:
  ⚠️ 第26行: 发现jQuery依赖: $('.delete-photo').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\image_uploader.js:
  ❌ 第671行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第639行: 过时的data属性: data-dismiss
  ⚠️ 第647行: 过时的data属性: data-dismiss
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 更新为Bootstrap 5的API调用方式

📄 app\static\js\main.js:
  ⚠️ 第226行: 发现jQuery依赖: $('.datepicker')
  ⚠️ 第152行: 发现jQuery依赖: $.fn.
  ⚠️ 第166行: 发现jQuery依赖: $.fn.
  ⚠️ 第225行: 发现jQuery依赖: $.fn.
  ⚠️ 第16行: 过时的data属性: data-toggle
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\menu-switcher.js:
  ⚠️ 第31行: 过时的data属性: data-toggle
  ⚠️ 第171行: 过时的data属性: data-toggle
  💡 建议:
    - 将data-*属性更新为data-bs-*格式

📄 app\static\js\mobile-dropdown-fix.js:
  ⚠️ 第168行: 发现jQuery依赖: $('.navbar-nav .dropdown-toggle').dropdown()
  ⚠️ 第222行: 发现jQuery依赖: $.fn.
  ⚠️ 第159行: 过时的事件名: show.bs.
  ⚠️ 第163行: 过时的事件名: hide.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\quick_fix_recursion.js:
  ⚠️ 第69行: 发现jQuery依赖: $('#dropZone').on('click.safeFix', function(e)
  ⚠️ 第81行: 发现jQuery依赖: $('#fileInput').on('change.safeFix', function(e)
  ⚠️ 第93行: 发现jQuery依赖: $('#saveDocuments').on('click.safeFix', function(e)
  ⚠️ 第178行: 发现jQuery依赖: $('#dropZone')
  ⚠️ 第183行: 发现jQuery依赖: $('#fileInput')
  ⚠️ 第188行: 发现jQuery依赖: $('#saveDocuments')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\stock-in-detail-enhancement.js:
  ⚠️ 第43行: 发现jQuery依赖: $('.document-table .btn-primary').on('click', function()
  ⚠️ 第51行: 发现jQuery依赖: $('.document-table .btn-success').on('click', function()
  ⚠️ 第62行: 发现jQuery依赖: $('.document-table .btn-danger').on('click', function(e)
  ⚠️ 第88行: 发现jQuery依赖: $('.badge.mr-1.mb-1').on('click', function()
  ⚠️ 第99行: 发现jQuery依赖: $('#document')
  ⚠️ 第100行: 发现jQuery依赖: $('.custom-file-label')
  ⚠️ 第133行: 发现jQuery依赖: $('.custom-file')
  ⚠️ 第159行: 发现jQuery依赖: $('.modal').on('show.bs.modal', function()
  ⚠️ 第163行: 发现jQuery依赖: $('.modal').on('hide.bs.modal', function()
  ⚠️ 第171行: 发现jQuery依赖: $('.alert .fas').addClass('alert-icon-animated')
  ⚠️ 第174行: 发现jQuery依赖: $('.alert-info').each(function()
  ⚠️ 第187行: 发现jQuery依赖: $('#stockInBtn').on('click', function()
  ⚠️ 第212行: 发现jQuery依赖: $('#cancelStockInBtn').on('click', function()
  ⚠️ 第307行: 发现jQuery依赖: $('head').append(additionalStyles)
  ⚠️ 第159行: 过时的事件名: show.bs.
  ⚠️ 第163行: 过时的事件名: hide.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\user_guide.js:
  ⚠️ 第27行: 发现jQuery依赖: $('#nextStepBtn').on('click', ()
  ⚠️ 第32行: 发现jQuery依赖: $('#prevStepBtn').on('click', ()
  ⚠️ 第37行: 发现jQuery依赖: $('#skipGuideBtn').on('click', ()
  ⚠️ 第42行: 发现jQuery依赖: $('#guideStepModal').on('hidden.bs.modal', ()
  ⚠️ 第109行: 发现jQuery依赖: $('#videoModal').remove()
  ⚠️ 第112行: 发现jQuery依赖: $('body').append(modalHtml)
  ⚠️ 第113行: 发现jQuery依赖: $('#videoModal').modal('show')
  ⚠️ 第116行: 发现jQuery依赖: $('#videoModal').on('hidden.bs.modal', function()
  ⚠️ 第130行: 发现jQuery依赖: $('#guideProgress').css('width', progress + '%')
  ⚠️ 第136行: 发现jQuery依赖: $('#guideStepModal').modal('show')
  ⚠️ 第144行: 发现jQuery依赖: $('#' + templateId).html()
  ⚠️ 第147行: 发现jQuery依赖: $('#stepContent').html(template)
  ⚠️ 第151行: 发现jQuery依赖: $('#stepTitle').text(stepInfo.title)
  ⚠️ 第161行: 发现jQuery依赖: $('#stepContent').html(data.content)
  ⚠️ 第162行: 发现jQuery依赖: $('#stepTitle').text(data.title)
  ⚠️ 第169行: 发现jQuery依赖: $('#stepContent').html('<p class="text-danger">加载内容失败，请刷新页面重试。</p>')
  ⚠️ 第328行: 发现jQuery依赖: $('#prevStepBtn').show()
  ⚠️ 第330行: 发现jQuery依赖: $('#prevStepBtn').hide()
  ⚠️ 第335行: 发现jQuery依赖: $('#nextStepBtn').html('<i class="fas fa-check ml-1"></i>完成引导')
  ⚠️ 第337行: 发现jQuery依赖: $('#nextStepBtn').html('<i class="fas fa-arrow-right ml-1"></i>下一步')
  ⚠️ 第388行: 发现jQuery依赖: $('#guideStepModal').modal('hide')
  ⚠️ 第393行: 发现jQuery依赖: $('#guideStepModal').modal('hide')
  ⚠️ 第404行: 发现jQuery依赖: $('#guideStepModal').modal('hide')
  ⚠️ 第408行: 发现jQuery依赖: $('#guideStepModal').modal('hide')
  ⚠️ 第440行: 发现jQuery依赖: $('body').prepend(message)
  ⚠️ 第443行: 发现jQuery依赖: $('html, body').animate({ scrollTop: 0 }, 500)
  ❌ 第113行: Bootstrap 4 API调用: .modal('show'
  ❌ 第136行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第42行: 过时的事件名: hidden.bs.
  ⚠️ 第116行: 过时的事件名: hidden.bs.
  ⚠️ 第88行: 过时的data属性: data-dismiss
  ⚠️ 第99行: 过时的data属性: data-dismiss
  ⚠️ 第433行: 过时的data属性: data-dismiss
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将data-*属性更新为data-bs-*格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\video-management.js:
  ⚠️ 第9行: 发现jQuery依赖: $('#stepSelect').val(stepName)
  ⚠️ 第10行: 发现jQuery依赖: $('#uploadVideoModal').modal('show')
  ⚠️ 第15行: 发现jQuery依赖: $('#previewVideoTitle').text(videoName)
  ⚠️ 第16行: 发现jQuery依赖: $('#previewVideo')
  ⚠️ 第20行: 发现jQuery依赖: $('#videoLoadError').hide()
  ⚠️ 第24行: 发现jQuery依赖: $('#videoLoadError').show()
  ⚠️ 第27行: 发现jQuery依赖: $('#videoPreviewModal').modal('show')
  ⚠️ 第34行: 发现jQuery依赖: $('button[onclick="uploadVideo()"]')
  ⚠️ 第40行: 发现jQuery依赖: $('#videoFile')
  ⚠️ 第52行: 发现jQuery依赖: $('#uploadVideoForm')
  ⚠️ 第56行: 发现jQuery依赖: $('#uploadProgress').show()
  ⚠️ 第69行: 发现jQuery依赖: $('.progress-bar').css('width', percentComplete + '%')
  ⚠️ 第77行: 发现jQuery依赖: $('#uploadVideoModal').modal('hide')
  ⚠️ 第88行: 发现jQuery依赖: $('#uploadProgress').hide()
  ⚠️ 第89行: 发现jQuery依赖: $('.progress-bar').css('width', '0%')
  ⚠️ 第91行: 发现jQuery依赖: $('button[onclick="uploadVideo()"]').prop('disabled', false)
  ❌ 第10行: Bootstrap 4 API调用: .modal('show'
  ❌ 第27行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\weekly_menu_modal.js:
  ⚠️ 第23行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第24行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第452行: 发现jQuery依赖: $('.loading-overlay').css('display', 'flex').hide().fadeIn(this.config.animationDuration)
  ⚠️ 第459行: 发现jQuery依赖: $('.loading-overlay').fadeOut(this.config.animationDuration)
  ⚠️ 第867行: 发现jQuery依赖: $('#selectedDishes').empty()
  ⚠️ 第870行: 发现jQuery依赖: $('#recipeSearch').val('')
  ⚠️ 第874行: 发现jQuery依赖: $('#customDishInput').val('')
  ⚠️ 第877行: 发现jQuery依赖: $('#recipeCategories .nav-link').removeClass('active')
  ⚠️ 第878行: 发现jQuery依赖: $('#recipeCategories .nav-link[data-category="all"]').addClass('active')
  ⚠️ 第880行: 发现jQuery依赖: $('.recipe-card').show()
  ⚠️ 第956行: 发现jQuery依赖: $('#modalTitle').text(`${date} ${meal} 菜品选择`)
  ⚠️ 第959行: 发现jQuery依赖: $('#menuModal').modal('show')
  ⚠️ 第963行: 发现jQuery依赖: $('.nav-link[data-category="肉类"]').click()
  ⚠️ 第977行: 发现jQuery依赖: $('.recipe-card').show()
  ⚠️ 第979行: 发现jQuery依赖: $('.recipe-card').hide()
  ⚠️ 第1006行: 发现jQuery依赖: $('.recipe-card').hide()
  ⚠️ 第1009行: 发现jQuery依赖: $('.recipe-card').show()
  ⚠️ 第1013行: 发现jQuery依赖: $('.recipe-card:visible').each(function()
  ⚠️ 第1028行: 发现jQuery依赖: $('#customDishInput')
  ⚠️ 第1101行: 发现jQuery依赖: $('#selectedDishes')
  ⚠️ 第1222行: 发现jQuery依赖: $('#menuModal').modal('hide')
  ⚠️ 第1239行: 发现jQuery依赖: $('#selectedDishes').empty()
  ⚠️ 第1277行: 发现jQuery依赖: $('.menu-input').on('click', (e)
  ⚠️ 第1286行: 发现jQuery依赖: $('#saveMenuBtn').on('click', ()
  ⚠️ 第1291行: 发现jQuery依赖: $('#publishMenuBtn').on('click', ()
  ⚠️ 第1296行: 发现jQuery依赖: $('#unpublishMenuBtn').on('click', ()
  ⚠️ 第1301行: 发现jQuery依赖: $('#createMenuBtn').on('click', ()
  ⚠️ 第1306行: 发现jQuery依赖: $('.week-item').on('click', (e)
  ⚠️ 第1329行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1337行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1338行: 发现jQuery依赖: $('#week-start').val()
  ⚠️ 第1339行: 发现jQuery依赖: $('meta[name="csrf-token"]').attr('content')
  ⚠️ 第1358行: 发现jQuery依赖: $('#createMenuPrompt').hide()
  ⚠️ 第1359行: 发现jQuery依赖: $('#menuForm').show()
  ⚠️ 第1362行: 发现jQuery依赖: $('#menu-id').val(response.weekly_menu_id)
  ⚠️ 第1387行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第1388行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1406行: 发现jQuery依赖: $('#menuData').val(JSON.stringify(menuData))
  ⚠️ 第1409行: 发现jQuery依赖: $('#menuForm').submit()
  ⚠️ 第1425行: 发现jQuery依赖: $('.menu-input').each((index, input)
  ⚠️ 第1512行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第1545行: 发现jQuery依赖: $('#menu-id').val()
  ❌ 第959行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第364行: 过时的事件名: show.bs.
  ⚠️ 第369行: 过时的事件名: hidden.bs.
  ⚠️ 第484行: 过时的data属性: data-dismiss
  ⚠️ 第565行: 过时的data属性: data-dismiss
  ⚠️ 第572行: 过时的data属性: data-dismiss
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将data-*属性更新为data-bs-*格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\static\js\weekly_menu_v2.js:
  ⚠️ 第23行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第24行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第452行: 发现jQuery依赖: $('.loading-overlay').css('display', 'flex').hide().fadeIn(this.config.animationDuration)
  ⚠️ 第459行: 发现jQuery依赖: $('.loading-overlay').fadeOut(this.config.animationDuration)
  ⚠️ 第870行: 发现jQuery依赖: $('#selectedDishes').empty()
  ⚠️ 第873行: 发现jQuery依赖: $('#recipeSearch').val('')
  ⚠️ 第877行: 发现jQuery依赖: $('#customDishInput').val('')
  ⚠️ 第880行: 发现jQuery依赖: $('#recipeCategories .nav-link').removeClass('active')
  ⚠️ 第881行: 发现jQuery依赖: $('#recipeCategories .nav-link[data-category="all"]').addClass('active')
  ⚠️ 第883行: 发现jQuery依赖: $('.recipe-card').show()
  ⚠️ 第959行: 发现jQuery依赖: $('#modalTitle').text(`${date} ${meal} 菜品选择`)
  ⚠️ 第962行: 发现jQuery依赖: $('#menuModal').modal('show')
  ⚠️ 第966行: 发现jQuery依赖: $('.nav-link[data-category="肉类"]').click()
  ⚠️ 第980行: 发现jQuery依赖: $('.recipe-card-item').show()
  ⚠️ 第982行: 发现jQuery依赖: $('.recipe-card-item').each(function()
  ⚠️ 第1015行: 发现jQuery依赖: $('.recipe-card').hide()
  ⚠️ 第1018行: 发现jQuery依赖: $('.recipe-card').show()
  ⚠️ 第1022行: 发现jQuery依赖: $('.recipe-card:visible').each(function()
  ⚠️ 第1037行: 发现jQuery依赖: $('#customDishInput')
  ⚠️ 第1110行: 发现jQuery依赖: $('#selectedDishes')
  ⚠️ 第1231行: 发现jQuery依赖: $('#menuModal').modal('hide')
  ⚠️ 第1248行: 发现jQuery依赖: $('#selectedDishes').empty()
  ⚠️ 第1286行: 发现jQuery依赖: $('.menu-input').on('click', (e)
  ⚠️ 第1295行: 发现jQuery依赖: $('#saveMenuBtn').on('click', ()
  ⚠️ 第1300行: 发现jQuery依赖: $('#publishMenuBtn').on('click', ()
  ⚠️ 第1305行: 发现jQuery依赖: $('#unpublishMenuBtn').on('click', ()
  ⚠️ 第1310行: 发现jQuery依赖: $('#createMenuBtn').on('click', ()
  ⚠️ 第1315行: 发现jQuery依赖: $('.week-item').on('click', (e)
  ⚠️ 第1338行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1346行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1347行: 发现jQuery依赖: $('#week-start').val()
  ⚠️ 第1348行: 发现jQuery依赖: $('meta[name="csrf-token"]').attr('content')
  ⚠️ 第1367行: 发现jQuery依赖: $('#createMenuPrompt').hide()
  ⚠️ 第1368行: 发现jQuery依赖: $('#menuForm').show()
  ⚠️ 第1371行: 发现jQuery依赖: $('#menu-id').val(response.weekly_menu_id)
  ⚠️ 第1381行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1382行: 发现jQuery依赖: $('#week-start').val()
  ⚠️ 第1407行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第1408行: 发现jQuery依赖: $('#area-id').val()
  ⚠️ 第1426行: 发现jQuery依赖: $('#menuData').val(JSON.stringify(menuData))
  ⚠️ 第1429行: 发现jQuery依赖: $('#menuForm').submit()
  ⚠️ 第1443行: 发现jQuery依赖: $('.menu-input').each((index, input)
  ⚠️ 第1530行: 发现jQuery依赖: $('#menu-id').val()
  ⚠️ 第1563行: 发现jQuery依赖: $('#menu-id').val()
  ❌ 第962行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第364行: 过时的事件名: show.bs.
  ⚠️ 第369行: 过时的事件名: hidden.bs.
  ⚠️ 第484行: 过时的data属性: data-dismiss
  ⚠️ 第565行: 过时的data属性: data-dismiss
  ⚠️ 第572行: 过时的data属性: data-dismiss
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将data-*属性更新为data-bs-*格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\base.html:
  ⚠️ 第422行: 发现jQuery依赖: $('.navbar-nav .dropdown-toggle').on('click', function(e)
  ⚠️ 第436行: 发现jQuery依赖: $('.navbar-nav .dropdown-item').on('click', function()
  ⚠️ 第438行: 发现jQuery依赖: $('.navbar-collapse').collapse('hide')
  ⚠️ 第445行: 发现jQuery依赖: $('.btn, .nav-link, .dropdown-item').each(function()
  ⚠️ 第506行: 发现jQuery依赖: $.fn.
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\mobile_test.html:
  ⚠️ 第231行: 发现jQuery依赖: $('#screen-width').text(window.screen.width)
  ⚠️ 第232行: 发现jQuery依赖: $('#screen-height').text(window.screen.height)
  ⚠️ 第233行: 发现jQuery依赖: $('#device-ratio').text(window.devicePixelRatio || 1)
  ⚠️ 第234行: 发现jQuery依赖: $('#user-agent').text(navigator.userAgent.substring(0, 50) + '...')
  ⚠️ 第235行: 发现jQuery依赖: $('#touch-support').text('ontouchstart' in window ? '是' : '否')
  ⚠️ 第236行: 发现jQuery依赖: $('#mobile-detected').text(window.innerWidth <= 768 ? '是' : '否')
  ⚠️ 第239行: 发现jQuery依赖: $('.btn').on('click', function()
  ⚠️ 第246行: 发现jQuery依赖: $('form').on('submit', function(e)
  ⚠️ 第240行: 过时的data属性: data-toggle
  ⚠️ 第240行: 过时的data属性: data-dismiss
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\theme_demo.html:
  ⚠️ 第403行: 发现jQuery依赖: $('.theme-demo-btn').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\carousel_list.html:
  ⚠️ 第248行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式

📄 app\templates\admin\data_management.html:
  ⚠️ 第448行: 发现jQuery依赖: $('.clear-data-form').on('submit', function(e)
  ⚠️ 第455行: 发现jQuery依赖: $('#moduleNameSpan').text(moduleName)
  ⚠️ 第458行: 发现jQuery依赖: $('#confirmModal').modal('show')
  ⚠️ 第461行: 发现jQuery依赖: $('#confirmClearBtn').off('click').on('click', function()
  ⚠️ 第462行: 发现jQuery依赖: $('#confirmModal').modal('hide')
  ⚠️ 第468行: 发现jQuery依赖: $('#refreshStats').click(function()
  ⚠️ 第478行: 发现jQuery依赖: $('#ingredients-total').text(data.ingredients.total)
  ⚠️ 第479行: 发现jQuery依赖: $('#ingredients-categories').text(data.ingredients.categories)
  ⚠️ 第482行: 发现jQuery依赖: $('#recipes-total').text(data.recipes.total)
  ⚠️ 第483行: 发现jQuery依赖: $('#recipes-ingredients').text(data.recipes.ingredients)
  ⚠️ 第486行: 发现jQuery依赖: $('#suppliers-total').text(data.suppliers.total)
  ⚠️ 第487行: 发现jQuery依赖: $('#suppliers-products').text(data.suppliers.products)
  ⚠️ 第488行: 发现jQuery依赖: $('#suppliers-certificates').text(data.suppliers.certificates)
  ⚠️ 第489行: 发现jQuery依赖: $('#suppliers-categories').text(data.suppliers.categories)
  ⚠️ 第492行: 发现jQuery依赖: $('#inventory-records').text(data.inventory.records)
  ⚠️ 第493行: 发现jQuery依赖: $('#inventory-stock-in').text(data.inventory.stock_in)
  ⚠️ 第494行: 发现jQuery依赖: $('#inventory-stock-out').text(data.inventory.stock_out)
  ⚠️ 第495行: 发现jQuery依赖: $('#inventory-warehouses').text(data.inventory.warehouses)
  ⚠️ 第496行: 发现jQuery依赖: $('#inventory-locations').text(data.inventory.locations)
  ⚠️ 第499行: 发现jQuery依赖: $('#menu-plans').text(data.menu.plans)
  ⚠️ 第500行: 发现jQuery依赖: $('#menu-recipes').text(data.menu.recipes)
  ⚠️ 第503行: 发现jQuery依赖: $('#food-samples').text(data.food_samples)
  ⚠️ 第506行: 发现jQuery依赖: $('#employees-total').text(data.employees.total)
  ⚠️ 第507行: 发现jQuery依赖: $('#employees-health-certificates').text(data.employees.health_certificates)
  ⚠️ 第508行: 发现jQuery依赖: $('#employees-medical-examinations').text(data.employees.medical_examinations)
  ⚠️ 第509行: 发现jQuery依赖: $('#employees-health-checks').text(data.employees.health_checks)
  ⚠️ 第510行: 发现jQuery依赖: $('#employees-training-records').text(data.employees.training_records)
  ⚠️ 第513行: 发现jQuery依赖: $('#notifications').text(data.notifications)
  ⚠️ 第516行: 发现jQuery依赖: $('#audit-logs').text(data.audit_logs)
  ❌ 第458行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\roles.html:
  ⚠️ 第488行: 发现jQuery依赖: $('#searchBtn').click(function()
  ⚠️ 第492行: 发现jQuery依赖: $('#searchInput').keyup(function(e)
  ⚠️ 第498行: 发现jQuery依赖: $('#resetBtn').click(function()
  ⚠️ 第499行: 发现jQuery依赖: $('#searchInput').val('')
  ⚠️ 第500行: 发现jQuery依赖: $('table tbody tr').show()
  ⚠️ 第504行: 发现jQuery依赖: $('#searchInput').val().toLowerCase()
  ⚠️ 第505行: 发现jQuery依赖: $('table tbody tr').each(function()
  ⚠️ 第518行: 发现jQuery依赖: $('.sort-link').click(function(e)
  ⚠️ 第521行: 发现jQuery依赖: $('table tbody')
  ⚠️ 第554行: 发现jQuery依赖: $('.sort-link').find('i').attr('class', 'fas fa-sort')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\role_permissions.html:
  ⚠️ 第179行: 发现jQuery依赖: $('[data-bs-toggle="popover"]')
  ⚠️ 第185行: 发现jQuery依赖: $('.module-checkbox').change(function()
  ⚠️ 第190行: 发现jQuery依赖: $('input[data-module="' + module + '"].permission-checkbox').prop('checked', checked)
  ⚠️ 第194行: 发现jQuery依赖: $('.permission-checkbox').change(function()
  ⚠️ 第199行: 发现jQuery依赖: $('input[data-module="' + module + '"].permission-checkbox').each(function()
  ⚠️ 第207行: 发现jQuery依赖: $('#module_' + module).prop('checked', allChecked)
  ⚠️ 第211行: 发现jQuery依赖: $('.module-checkbox').each(function()
  ⚠️ 第216行: 发现jQuery依赖: $('input[data-module="' + module + '"].permission-checkbox').each(function()
  ⚠️ 第228行: 发现jQuery依赖: $('#selectAll').click(function()
  ⚠️ 第229行: 发现jQuery依赖: $('.permission-checkbox:visible, .module-checkbox:visible').prop('checked', true)
  ⚠️ 第233行: 发现jQuery依赖: $('#deselectAll').click(function()
  ⚠️ 第234行: 发现jQuery依赖: $('.permission-checkbox:visible, .module-checkbox:visible').prop('checked', false)
  ⚠️ 第238行: 发现jQuery依赖: $('#permissionSearch').on('input', function()
  ⚠️ 第240行: 发现jQuery依赖: $('#moduleFilter').val()
  ⚠️ 第243行: 发现jQuery依赖: $('.permission-card').each(function()
  ⚠️ 第276行: 发现jQuery依赖: $('#moduleFilter').change(function()
  ⚠️ 第278行: 发现jQuery依赖: $('#permissionSearch').trigger('input')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\users.html:
  ⚠️ 第442行: 发现jQuery依赖: $('#batchDeleteModal').modal('show')
  ⚠️ 第447行: 发现jQuery依赖: $('#batchAssignRolesModal').modal('show')
  ⚠️ 第504行: 发现jQuery依赖: $('#deleteUserModal').modal('show')
  ❌ 第442行: Bootstrap 4 API调用: .modal('show'
  ❌ 第447行: Bootstrap 4 API调用: .modal('show'
  ❌ 第504行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\user_form.html:
  ⚠️ 第168行: 发现jQuery依赖: $('#area_level_1').change(function()
  ⚠️ 第172行: 发现jQuery依赖: $('#area_id').val(areaId)
  ⚠️ 第173行: 发现jQuery依赖: $('#area_level').val(1)
  ⚠️ 第177行: 发现jQuery依赖: $('#area_level_2').show()
  ⚠️ 第180行: 发现jQuery依赖: $('#area_level_3, #area_level_4').hide().val('')
  ⚠️ 第183行: 发现jQuery依赖: $('#area_level_2, #area_level_3, #area_level_4').hide().val('')
  ⚠️ 第184行: 发现jQuery依赖: $('#area_id').val('')
  ⚠️ 第185行: 发现jQuery依赖: $('#area_level').val('')
  ⚠️ 第190行: 发现jQuery依赖: $('#area_level_2').change(function()
  ⚠️ 第194行: 发现jQuery依赖: $('#area_id').val(areaId)
  ⚠️ 第195行: 发现jQuery依赖: $('#area_level').val(2)
  ⚠️ 第199行: 发现jQuery依赖: $('#area_level_3').show()
  ⚠️ 第202行: 发现jQuery依赖: $('#area_level_4').hide().val('')
  ⚠️ 第205行: 发现jQuery依赖: $('#area_level_3, #area_level_4').hide().val('')
  ⚠️ 第207行: 发现jQuery依赖: $('#area_level_1').val()
  ⚠️ 第209行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第210行: 发现jQuery依赖: $('#area_level').val(1)
  ⚠️ 第212行: 发现jQuery依赖: $('#area_id').val('')
  ⚠️ 第213行: 发现jQuery依赖: $('#area_level').val('')
  ⚠️ 第219行: 发现jQuery依赖: $('#area_level_3').change(function()
  ⚠️ 第223行: 发现jQuery依赖: $('#area_id').val(areaId)
  ⚠️ 第224行: 发现jQuery依赖: $('#area_level').val(3)
  ⚠️ 第228行: 发现jQuery依赖: $('#area_level_4').show()
  ⚠️ 第231行: 发现jQuery依赖: $('#area_level_4').hide().val('')
  ⚠️ 第233行: 发现jQuery依赖: $('#area_level_2').val()
  ⚠️ 第235行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第236行: 发现jQuery依赖: $('#area_level').val(2)
  ⚠️ 第238行: 发现jQuery依赖: $('#area_level_1').val()
  ⚠️ 第240行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第241行: 发现jQuery依赖: $('#area_level').val(1)
  ⚠️ 第243行: 发现jQuery依赖: $('#area_id').val('')
  ⚠️ 第244行: 发现jQuery依赖: $('#area_level').val('')
  ⚠️ 第251行: 发现jQuery依赖: $('#area_level_4').change(function()
  ⚠️ 第255行: 发现jQuery依赖: $('#area_id').val(areaId)
  ⚠️ 第256行: 发现jQuery依赖: $('#area_level').val(4)
  ⚠️ 第259行: 发现jQuery依赖: $('#area_level_3').val()
  ⚠️ 第261行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第262行: 发现jQuery依赖: $('#area_level').val(3)
  ⚠️ 第264行: 发现jQuery依赖: $('#area_level_2').val()
  ⚠️ 第266行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第267行: 发现jQuery依赖: $('#area_level').val(2)
  ⚠️ 第269行: 发现jQuery依赖: $('#area_level_1').val()
  ⚠️ 第271行: 发现jQuery依赖: $('#area_id').val(parentId)
  ⚠️ 第272行: 发现jQuery依赖: $('#area_level').val(1)
  ⚠️ 第274行: 发现jQuery依赖: $('#area_id').val('')
  ⚠️ 第275行: 发现jQuery依赖: $('#area_level').val('')
  ⚠️ 第291行: 发现jQuery依赖: $('#area_level_' + level)
  ⚠️ 第298行: 发现jQuery依赖: $('<option></option>')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\user_permissions.html:
  ⚠️ 第136行: 发现jQuery依赖: $('input[type="checkbox"]').each(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\guide_management\demo_data.html:
  ⚠️ 第265行: 发现jQuery依赖: $('#demoDataModal').modal('show')
  ⚠️ 第266行: 发现jQuery依赖: $('#demoDataContent')
  ⚠️ 第275行: 发现jQuery依赖: $('#demoDataContent')
  ❌ 第265行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\guide_management\scenarios.html:
  ⚠️ 第245行: 发现jQuery依赖: $('#scenarioPreviewModal').modal('show')
  ⚠️ 第246行: 发现jQuery依赖: $('#scenarioPreviewContent')
  ⚠️ 第256行: 发现jQuery依赖: $('#scenarioPreviewContent').html(scenarioContent)
  ⚠️ 第262行: 发现jQuery依赖: $('#scenarioEditModal').modal('show')
  ⚠️ 第263行: 发现jQuery依赖: $('#scenarioEditContent')
  ⚠️ 第273行: 发现jQuery依赖: $('#scenarioEditContent').html(editForm)
  ⚠️ 第292行: 发现jQuery依赖: $('#scenarioPreviewModal').modal('hide')
  ⚠️ 第298行: 发现jQuery依赖: $('#scenarioEditModal').modal('hide')
  ❌ 第245行: Bootstrap 4 API调用: .modal('show'
  ❌ 第262行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\guide_management\users.html:
  ⚠️ 第267行: 发现jQuery依赖: $('#selectAll').change(function()
  ⚠️ 第268行: 发现jQuery依赖: $('.user-checkbox').prop('checked', this.checked)
  ⚠️ 第336行: 发现jQuery依赖: $('.user-checkbox:checked').map(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\super_delete\index.html:
  ⚠️ 第277行: 发现jQuery依赖: $('#entityType').change(function()
  ⚠️ 第280行: 发现jQuery依赖: $('#entitySearch').prop('disabled', false)
  ⚠️ 第281行: 发现jQuery依赖: $('#searchBtn').prop('disabled', false)
  ⚠️ 第285行: 发现jQuery依赖: $('#entitySearch').attr('placeholder', '输入学校/机构名称搜索')
  ⚠️ 第287行: 发现jQuery依赖: $('#entitySearch').attr('placeholder', '输入名称或ID搜索')
  ⚠️ 第290行: 发现jQuery依赖: $('#entitySearch').prop('disabled', true)
  ⚠️ 第291行: 发现jQuery依赖: $('#searchBtn').prop('disabled', true)
  ⚠️ 第294行: 发现jQuery依赖: $('#entitySearch').val('')
  ⚠️ 第295行: 发现jQuery依赖: $('#entityId').val('')
  ⚠️ 第296行: 发现jQuery依赖: $('#searchResults').hide().empty()
  ⚠️ 第297行: 发现jQuery依赖: $('#entityDetails').hide()
  ⚠️ 第302行: 发现jQuery依赖: $('#refreshEntityList').click(function()
  ⚠️ 第303行: 发现jQuery依赖: $('#entityType').val()
  ⚠️ 第310行: 发现jQuery依赖: $('#entityList').change(function()
  ⚠️ 第313行: 发现jQuery依赖: $('#entityType').val()
  ⚠️ 第316行: 发现jQuery依赖: $('#entityId').val(entityId)
  ⚠️ 第317行: 发现jQuery依赖: $('#entitySearch').val(entityName)
  ⚠️ 第323行: 发现jQuery依赖: $('#entityId').val('')
  ⚠️ 第324行: 发现jQuery依赖: $('#entitySearch').val('')
  ⚠️ 第325行: 发现jQuery依赖: $('#entityDetails').hide()
  ⚠️ 第332行: 发现jQuery依赖: $('#entityList').prop('disabled', true)
  ⚠️ 第333行: 发现jQuery依赖: $('#refreshEntityList').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>')
  ⚠️ 第342行: 发现jQuery依赖: $('#entityList').empty().append('<option value="">-- 请选择 --</option>')
  ⚠️ 第346行: 发现jQuery依赖: $('<option></option>')
  ⚠️ 第349行: 发现jQuery依赖: $('#entityList').append(option)
  ⚠️ 第352行: 发现jQuery依赖: $('#entityList').append('<option value="" disabled>没有可用的实体</option>')
  ⚠️ 第355行: 发现jQuery依赖: $('#entityList').prop('disabled', false)
  ⚠️ 第356行: 发现jQuery依赖: $('#refreshEntityList').prop('disabled', false).html('<i class="fas fa-sync-alt"></i>')
  ⚠️ 第359行: 发现jQuery依赖: $('#entityList').empty()
  ⚠️ 第361行: 发现jQuery依赖: $('#entityList').prop('disabled', false)
  ⚠️ 第362行: 发现jQuery依赖: $('#refreshEntityList').prop('disabled', false).html('<i class="fas fa-sync-alt"></i>')
  ⚠️ 第368行: 发现jQuery依赖: $('#searchBtn').click(function()
  ⚠️ 第373行: 发现jQuery依赖: $('#entitySearch').on('input', function()
  ⚠️ 第378行: 发现jQuery依赖: $('#searchResults').hide().empty()
  ⚠️ 第379行: 发现jQuery依赖: $('#entityId').val('')
  ⚠️ 第386行: 发现jQuery依赖: $('#entityType').val()
  ⚠️ 第387行: 发现jQuery依赖: $('#entitySearch').val()
  ⚠️ 第399行: 发现jQuery依赖: $('#searchResults').empty()
  ⚠️ 第402行: 发现jQuery依赖: $('#searchResults').append('<div class="list-group-item">未找到匹配的实体</div>')
  ⚠️ 第405行: 发现jQuery依赖: $('<a href="#" class="list-group-item list-group-item-action"></a>')
  ⚠️ 第410行: 发现jQuery依赖: $('#entitySearch').val(entity.name)
  ⚠️ 第411行: 发现jQuery依赖: $('#entityId').val(entity.id)
  ⚠️ 第412行: 发现jQuery依赖: $('#searchResults').hide()
  ⚠️ 第418行: 发现jQuery依赖: $('#searchResults').append(item)
  ⚠️ 第422行: 发现jQuery依赖: $('#searchResults').show()
  ⚠️ 第425行: 发现jQuery依赖: $('#searchResults').empty()
  ⚠️ 第434行: 发现jQuery依赖: $('#entityType').val()
  ⚠️ 第435行: 发现jQuery依赖: $('#entityId').val()
  ⚠️ 第438行: 发现jQuery依赖: $('#analyzeBtn').prop('disabled', false)
  ⚠️ 第439行: 发现jQuery依赖: $('#deleteBtn').prop('disabled', false)
  ⚠️ 第441行: 发现jQuery依赖: $('#analyzeBtn').prop('disabled', true)
  ⚠️ 第442行: 发现jQuery依赖: $('#deleteBtn').prop('disabled', true)
  ⚠️ 第458行: 发现jQuery依赖: $('#entityDetailsTitle').text(data.entity_name + ' 详情')
  ⚠️ 第461行: 发现jQuery依赖: $('#entityDetailsTable').empty()
  ⚠️ 第463行: 发现jQuery依赖: $('<tr></tr>')
  ⚠️ 第464行: 发现jQuery依赖: $('<td></td>').text(key))
  ⚠️ 第465行: 发现jQuery依赖: $('<td></td>').text(value))
  ⚠️ 第466行: 发现jQuery依赖: $('#entityDetailsTable').append(row)
  ⚠️ 第470行: 发现jQuery依赖: $('#relatedEntitiesTable').empty()
  ⚠️ 第472行: 发现jQuery依赖: $('<tr></tr>')
  ⚠️ 第473行: 发现jQuery依赖: $('<td></td>').text(relation.relation_type))
  ⚠️ 第474行: 发现jQuery依赖: $('<td></td>').text(relation.entity_type))
  ⚠️ 第475行: 发现jQuery依赖: $('<td></td>').text(relation.count))
  ⚠️ 第476行: 发现jQuery依赖: $('#relatedEntitiesTable').append(row)
  ⚠️ 第480行: 发现jQuery依赖: $('#entityDetails').show()
  ⚠️ 第483行: 发现jQuery依赖: $('#entityDetails').hide()
  ⚠️ 第488行: 发现jQuery依赖: $('#entityDetails').hide()
  ⚠️ 第503行: 发现jQuery依赖: $('#areaTree').html('<div class="alert alert-danger">加载区域数据失败</div>')
  ⚠️ 第507行: 发现jQuery依赖: $('#areaTree').html('<div class="alert alert-danger">网络错误，无法加载区域数据</div>')
  ⚠️ 第517行: 发现jQuery依赖: $('#areaTree').html(html)
  ⚠️ 第520行: 发现jQuery依赖: $('.area-node').click(function()
  ⚠️ 第581行: 发现jQuery依赖: $('.area-node').removeClass('bg-primary text-white')
  ⚠️ 第585行: 发现jQuery依赖: $('#selectedAreaName').text(areaName)
  ⚠️ 第586行: 发现jQuery依赖: $('#selectedAreaId').text(areaId)
  ⚠️ 第587行: 发现jQuery依赖: $('#selectedAreaLevel').text(areaLevel)
  ⚠️ 第588行: 发现jQuery依赖: $('#entityId').val(areaId)
  ⚠️ 第591行: 发现jQuery依赖: $('#noAreaSelected').hide()
  ⚠️ 第592行: 发现jQuery依赖: $('#areaDetails').show()
  ⚠️ 第611行: 发现jQuery依赖: $('#dataStatsCards').html('<div class="col-12"><div class="alert alert-warning">无法加载数据统计</div></div>')
  ⚠️ 第615行: 发现jQuery依赖: $('#dataStatsCards').html('<div class="col-12"><div class="alert alert-danger">加载数据统计失败</div></div>')
  ⚠️ 第666行: 发现jQuery依赖: $('#dataStatsCards').html(html)
  ⚠️ 第709行: 发现jQuery依赖: $('#deleteSummary').html(html)
  ⚠️ 第713行: 发现jQuery依赖: $('#analyzeBtn').click(function()
  ⚠️ 第714行: 发现jQuery依赖: $('#entityId').val()
  ⚠️ 第731行: 发现jQuery依赖: $('#analyzeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 分析中...')
  ⚠️ 第732行: 发现jQuery依赖: $('#deleteResult').hide()
  ⚠️ 第735行: 发现jQuery依赖: $('#analyzeBtn').prop('disabled', false).html('<i class="fas fa-search"></i> 分析')
  ⚠️ 第738行: 发现jQuery依赖: $('#analysisResult').show()
  ⚠️ 第741行: 发现jQuery依赖: $('#analysisAlert').removeClass('alert-danger').addClass('alert-success').text(data.message)
  ⚠️ 第742行: 发现jQuery依赖: $('#confirmDeleteBtn').show()
  ⚠️ 第744行: 发现jQuery依赖: $('#analysisAlert').removeClass('alert-success').addClass('alert-danger').text(data.message)
  ⚠️ 第745行: 发现jQuery依赖: $('#confirmDeleteBtn').hide()
  ⚠️ 第751行: 发现jQuery依赖: $('#deleteStepsSection').show()
  ⚠️ 第753行: 发现jQuery依赖: $('#deleteStepsSection').hide()
  ⚠️ 第762行: 发现jQuery依赖: $('#blockedEntitiesList').text(blockedText)
  ⚠️ 第763行: 发现jQuery依赖: $('#blockedEntitiesSection').show()
  ⚠️ 第765行: 发现jQuery依赖: $('#blockedEntitiesSection').hide()
  ⚠️ 第769行: 发现jQuery依赖: $('#analyzeBtn').prop('disabled', false).html('<i class="fas fa-search"></i> 分析')
  ⚠️ 第770行: 发现jQuery依赖: $('#analysisResult').show()
  ⚠️ 第771行: 发现jQuery依赖: $('#analysisAlert').removeClass('alert-success').addClass('alert-danger').text('分析请求失败')
  ⚠️ 第772行: 发现jQuery依赖: $('#confirmDeleteBtn').hide()
  ⚠️ 第773行: 发现jQuery依赖: $('#deletedEntitiesSection').hide()
  ⚠️ 第774行: 发现jQuery依赖: $('#blockedEntitiesSection').hide()
  ⚠️ 第780行: 发现jQuery依赖: $('#deleteBtn, #confirmDeleteBtn').click(function()
  ⚠️ 第785行: 发现jQuery依赖: $('#entityId').val()
  ⚠️ 第802行: 发现jQuery依赖: $('#deleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...')
  ⚠️ 第803行: 发现jQuery依赖: $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...')
  ⚠️ 第804行: 发现jQuery依赖: $('#analysisResult').hide()
  ⚠️ 第807行: 发现jQuery依赖: $('#deleteBtn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除')
  ⚠️ 第808行: 发现jQuery依赖: $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-check"></i> 确认删除')
  ⚠️ 第811行: 发现jQuery依赖: $('#deleteResult').show()
  ⚠️ 第814行: 发现jQuery依赖: $('#deleteAlert').removeClass('alert-danger').addClass('alert-success').text(data.message)
  ⚠️ 第816行: 发现jQuery依赖: $('#deleteAlert').removeClass('alert-success').addClass('alert-danger').text(data.message)
  ⚠️ 第821行: 发现jQuery依赖: $('#successEntitiesTable').empty()
  ⚠️ 第823行: 发现jQuery依赖: $('<tr></tr>')
  ⚠️ 第824行: 发现jQuery依赖: $('<td></td>').text(entity.type_name))
  ⚠️ 第825行: 发现jQuery依赖: $('<td></td>').text(entity.count))
  ⚠️ 第826行: 发现jQuery依赖: $('<td></td>').text(entity.ids.join(', ')))
  ⚠️ 第827行: 发现jQuery依赖: $('#successEntitiesTable').append(row)
  ⚠️ 第829行: 发现jQuery依赖: $('#successEntitiesSection').show()
  ⚠️ 第831行: 发现jQuery依赖: $('#successEntitiesSection').hide()
  ⚠️ 第836行: 发现jQuery依赖: $('#failedEntitiesTable').empty()
  ⚠️ 第838行: 发现jQuery依赖: $('<tr></tr>')
  ⚠️ 第839行: 发现jQuery依赖: $('<td></td>').text(entity.type_name))
  ⚠️ 第840行: 发现jQuery依赖: $('<td></td>').text(entity.id))
  ⚠️ 第841行: 发现jQuery依赖: $('<td></td>').text(entity.reason))
  ⚠️ 第842行: 发现jQuery依赖: $('#failedEntitiesTable').append(row)
  ⚠️ 第844行: 发现jQuery依赖: $('#failedEntitiesSection').show()
  ⚠️ 第846行: 发现jQuery依赖: $('#failedEntitiesSection').hide()
  ⚠️ 第851行: 发现jQuery依赖: $('#entitySearch').val('')
  ⚠️ 第852行: 发现jQuery依赖: $('#entityId').val('')
  ⚠️ 第857行: 发现jQuery依赖: $('#dataStatsCards').empty()
  ⚠️ 第858行: 发现jQuery依赖: $('#entityDetails').hide()
  ⚠️ 第859行: 发现jQuery依赖: $('#analysisResult').hide()
  ⚠️ 第863行: 发现jQuery依赖: $('#deleteResult').fadeOut()
  ⚠️ 第868行: 发现jQuery依赖: $('#deleteBtn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除')
  ⚠️ 第869行: 发现jQuery依赖: $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-check"></i> 确认删除')
  ⚠️ 第870行: 发现jQuery依赖: $('#deleteResult').show()
  ⚠️ 第871行: 发现jQuery依赖: $('#deleteAlert').removeClass('alert-success').addClass('alert-danger').text('删除请求失败')
  ⚠️ 第872行: 发现jQuery依赖: $('#successEntitiesSection').hide()
  ⚠️ 第873行: 发现jQuery依赖: $('#failedEntitiesSection').hide()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\system\dashboard.html:
  ⚠️ 第469行: 发现jQuery依赖: $('#refreshDashboard').click(function()
  ⚠️ 第484行: 发现jQuery依赖: $('#current-time').text(formattedTime)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\system\module_visibility.html:
  ⚠️ 第151行: 发现jQuery依赖: $('.module-visibility-toggle, .submodule-visibility-toggle').change(function()
  ⚠️ 第180行: 发现jQuery依赖: $('.toggle-children').click(function()
  ⚠️ 第225行: 发现jQuery依赖: $('.set-all-visible').click(function()
  ⚠️ 第227行: 发现jQuery依赖: $('#role-' + roleId)
  ⚠️ 第265行: 发现jQuery依赖: $('.set-all-hidden').click(function()
  ⚠️ 第267行: 发现jQuery依赖: $('#role-' + roleId)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\system\monitor.html:
  ⚠️ 第258行: 发现jQuery依赖: $('#refreshMonitor').click(function()
  ⚠️ 第272行: 发现jQuery依赖: $('#refreshMonitor').html('<i class="fas fa-sync-alt"></i> 刷新数据')
  ⚠️ 第273行: 发现jQuery依赖: $('#refreshMonitor').prop('disabled', false)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\video_guide\create.html:
  ⚠️ 第122行: 发现jQuery依赖: $('#video_file').on('change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\admin\video_guide\edit.html:
  ⚠️ 第159行: 发现jQuery依赖: $('#video_file').on('change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\area\index.html:
  ⚠️ 第234行: 发现jQuery依赖: $('#area-{{ area.id }}').addClass('show')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\auth\register.html:
  ⚠️ 第361行: 发现jQuery依赖: $('#generate_username_btn').click(function()
  ⚠️ 第362行: 发现jQuery依赖: $('#school_name_input').val().trim()
  ⚠️ 第365行: 发现jQuery依赖: $('#school_name_input').focus()
  ⚠️ 第371行: 发现jQuery依赖: $('#school_name_input').focus()
  ⚠️ 第376行: 发现jQuery依赖: $('#username_input').val(suggestedUsername)
  ⚠️ 第388行: 发现jQuery依赖: $('#school_name_input').on('input', function()
  ⚠️ 第396行: 发现jQuery依赖: $('<small class="char-count-hint text-info"></small>')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\batch_flow\form.html:
  ⚠️ 第148行: 发现jQuery依赖: $('#flow_type').change(function()
  ⚠️ 第150行: 发现jQuery依赖: $('#flow_direction')
  ⚠️ 第162行: 发现jQuery依赖: $('form').submit(function(e)
  ⚠️ 第163行: 发现jQuery依赖: $('#quantity').val())
  ⚠️ 第164行: 发现jQuery依赖: $('#flow_direction').val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\consumption_plan\create.html:
  ⚠️ 第290行: 发现jQuery依赖: $('.ingredient-checkbox').change(function()
  ⚠️ 第292行: 发现jQuery依赖: $('input[name="quantity_' + ingredientId + '"]')
  ⚠️ 第302行: 发现jQuery依赖: $('#add-condiment').click(function()
  ⚠️ 第322行: 发现jQuery依赖: $('#condiments-container').append(newRow)
  ⚠️ 第326行: 发现jQuery依赖: $('.highlight-row').removeClass('highlight-row')
  ⚠️ 第338行: 发现jQuery依赖: $('#consumption-form').submit(function(e)
  ⚠️ 第342行: 发现jQuery依赖: $('.ingredient-checkbox:checked').each(function()
  ⚠️ 第344行: 发现jQuery依赖: $('input[name="quantity_' + ingredientId + '"]').val()
  ⚠️ 第354行: 发现jQuery依赖: $('input[name="condiment_name[]"]').each(function(index)
  ⚠️ 第356行: 发现jQuery依赖: $('input[name="condiment_quantity[]"]').eq(index).val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\consumption_plan\edit.html:
  ⚠️ 第150行: 发现jQuery依赖: $('input[name^="quantity_"]').on('change', function()
  ⚠️ 第153行: 发现jQuery依赖: $('#inventory_' + detailId).text())
  ⚠️ 第164行: 发现jQuery依赖: $('form').on('submit', function(e)
  ⚠️ 第165行: 发现jQuery依赖: $('input[name="meal_types[]"]:checked')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\consumption_plan\new.html:
  ⚠️ 第249行: 发现jQuery依赖: $('#loading-overlay').hide()
  ⚠️ 第252行: 发现jQuery依赖: $('#area_id option').length > 1)
  ⚠️ 第254行: 发现jQuery依赖: $('#area_id option:eq(1)').val()
  ⚠️ 第255行: 发现jQuery依赖: $('#area_id').val(firstAreaId).trigger('change')
  ⚠️ 第262行: 发现jQuery依赖: $('#area_id').change(function()
  ⚠️ 第266行: 发现jQuery依赖: $('#warehouse_id').html('<option value="">请先选择区域</option>')
  ⚠️ 第268行: 发现jQuery依赖: $('.tab-pane tbody').empty().html('<tr><td colspan="5" class="text-center">请先选择区域和仓库</td></tr>')
  ⚠️ 第273行: 发现jQuery依赖: $('#loading-overlay').show()
  ⚠️ 第279行: 发现jQuery依赖: $('#warehouse_id').empty()
  ⚠️ 第282行: 发现jQuery依赖: $('#warehouse_id').append('<option value="">请选择仓库</option>')
  ⚠️ 第287行: 发现jQuery依赖: $('#warehouse_id').append('<option value="' + warehouse.id + '">' + warehouse.name + '</option>')
  ⚠️ 第292行: 发现jQuery依赖: $('#warehouse_id').val(data.warehouses[0].id).trigger('change')
  ⚠️ 第296行: 发现jQuery依赖: $('.tab-pane tbody').empty()
  ⚠️ 第313行: 发现jQuery依赖: $('#warehouse_id').html('<option value="">获取仓库失败</option>')
  ⚠️ 第320行: 发现jQuery依赖: $('#loading-overlay').hide()
  ⚠️ 第325行: 发现jQuery依赖: $('#warehouse_id').change(function()
  ⚠️ 第329行: 发现jQuery依赖: $('.tab-pane tbody').empty().html('<tr><td colspan="5" class="text-center">请先选择仓库</td></tr>')
  ⚠️ 第334行: 发现jQuery依赖: $('#loading-overlay').show()
  ⚠️ 第340行: 发现jQuery依赖: $('.tab-pane tbody').empty()
  ⚠️ 第345行: 发现jQuery依赖: $('.tab-pane tbody').each(function()
  ⚠️ 第365行: 发现jQuery依赖: $('#category-' + categoryId + '-body')
  ⚠️ 第408行: 发现jQuery依赖: $('.tab-pane tbody').empty()
  ⚠️ 第413行: 发现jQuery依赖: $('.tab-pane tbody').each(function()
  ⚠️ 第430行: 发现jQuery依赖: $('.tab-pane tbody').each(function()
  ⚠️ 第450行: 发现jQuery依赖: $('#loading-overlay').hide()
  ⚠️ 第456行: 发现jQuery依赖: $('.ingredient-checkbox').change(function()
  ⚠️ 第458行: 发现jQuery依赖: $('input[name="quantity_' + ingredientId + '"]')
  ⚠️ 第469行: 发现jQuery依赖: $('#add-condiment').click(function()
  ⚠️ 第489行: 发现jQuery依赖: $('#condiments-container').append(newRow)
  ⚠️ 第493行: 发现jQuery依赖: $('.highlight-row').removeClass('highlight-row')
  ⚠️ 第505行: 发现jQuery依赖: $('#consumption-form').submit(function(e)
  ⚠️ 第507行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第515行: 发现jQuery依赖: $('#warehouse_id').val()
  ⚠️ 第525行: 发现jQuery依赖: $('.ingredient-checkbox:checked').each(function()
  ⚠️ 第527行: 发现jQuery依赖: $('input[name="quantity_' + ingredientId + '"]').val()
  ⚠️ 第537行: 发现jQuery依赖: $('input[name="condiment_name[]"]').each(function(index)
  ⚠️ 第539行: 发现jQuery依赖: $('input[name="condiment_quantity[]"]').eq(index).val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\consumption_plan\super_editor.html:
  ⚠️ 第467行: 发现jQuery依赖: $('#loading-overlay').hide()
  ⚠️ 第484行: 发现jQuery依赖: $('#alertContainer').html(alertHtml)
  ⚠️ 第488行: 发现jQuery依赖: $('#alertContainer .alert').alert('close')
  ⚠️ 第499行: 发现jQuery依赖: $('.meal-type-checkbox').change(function()
  ⚠️ 第504行: 发现jQuery依赖: $('#consumption_date').change(function()
  ⚠️ 第510行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第511行: 发现jQuery依赖: $('#consumption_date').val()
  ⚠️ 第514行: 发现jQuery依赖: $('.meal-type-checkbox:checked').each(function()
  ⚠️ 第525行: 发现jQuery依赖: $('#recipeAnalysisSection').hide()
  ⚠️ 第530行: 发现jQuery依赖: $('#recipeAnalysisSection').show()
  ⚠️ 第531行: 发现jQuery依赖: $('#recipeAnalysisContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 分析食谱中...</div>')
  ⚠️ 第580行: 发现jQuery依赖: $('#recipeAnalysisContent').html('<div class="alert alert-danger"><strong>错误:</strong> ' + errorMsg + '</div>')
  ⚠️ 第757行: 发现jQuery依赖: $('#recipeAnalysisContent').html(html)
  ⚠️ 第777行: 发现jQuery依赖: $('#warehouse_id').val()
  ⚠️ 第779行: 发现jQuery依赖: $('#inventoryBody').html('<tr><td colspan="9" class="text-center">请选择仓库</td></tr>')
  ⚠️ 第783行: 发现jQuery依赖: $('#loading-overlay').show()
  ⚠️ 第787行: 发现jQuery依赖: $('#inventoryBody').empty()
  ⚠️ 第790行: 发现jQuery依赖: $('#inventoryBody').html('<tr><td colspan="9" class="text-center">该仓库暂无库存</td></tr>')
  ⚠️ 第801行: 发现jQuery依赖: $('#inventoryBody')
  ⚠️ 第826行: 发现jQuery依赖: $('#inventoryBody')
  ⚠️ 第858行: 发现jQuery依赖: $('#inventoryBody')
  ⚠️ 第867行: 发现jQuery依赖: $('#loading-overlay').hide()
  ⚠️ 第874行: 发现jQuery依赖: $('.batch-checkbox').change(function()
  ⚠️ 第876行: 发现jQuery依赖: $('input[name="quantity_' + batchId + '"]')
  ⚠️ 第935行: 发现jQuery依赖: $('.quantity-input').on('input', function()
  ⚠️ 第940行: 发现jQuery依赖: $('.quantity-input').on('paste', function()
  ⚠️ 第948行: 发现jQuery依赖: $('.quantity-input').on('blur', function()
  ⚠️ 第954行: 发现jQuery依赖: $('#selectAllBtn').click(function()
  ⚠️ 第955行: 发现jQuery依赖: $('.batch-checkbox').prop('checked', true).trigger('change')
  ⚠️ 第959行: 发现jQuery依赖: $('#deselectAllBtn').click(function()
  ⚠️ 第960行: 发现jQuery依赖: $('.batch-checkbox').prop('checked', false).trigger('change')
  ⚠️ 第964行: 发现jQuery依赖: $('#fillAllBtn').click(function()
  ⚠️ 第965行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第969行: 发现jQuery依赖: $('input[name="quantity_' + batchId + '"]')
  ⚠️ 第975行: 发现jQuery依赖: $('#searchBtn').click(function()
  ⚠️ 第976行: 发现jQuery依赖: $('#searchInput').val().toLowerCase()
  ⚠️ 第977行: 发现jQuery依赖: $('#inventoryTable tbody tr').each(function()
  ⚠️ 第997行: 发现jQuery依赖: $('#searchInput').keypress(function(e)
  ⚠️ 第999行: 发现jQuery依赖: $('#searchBtn').click()
  ⚠️ 第1005行: 发现jQuery依赖: $('#consumptionForm').submit(function(e)
  ⚠️ 第1006行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第1018行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第1021行: 发现jQuery依赖: $('input[name="quantity_' + batchId + '"]')
  ⚠️ 第1059行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第1060行: 发现jQuery依赖: $('#consumption_date').val()
  ⚠️ 第1063行: 发现jQuery依赖: $('.meal-type-checkbox:checked').each(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\add_training.html:
  ⚠️ 第247行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第302行: 发现jQuery依赖: $('#trainingForm').on('submit', function(e)
  ⚠️ 第322行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\companions.html:
  ⚠️ 第200行: 发现jQuery依赖: $('#dataTable')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\edit_inspection.html:
  ⚠️ 第167行: 发现jQuery依赖: $('#templateSelect').val(templateId).trigger('change')
  ⚠️ 第172行: 发现jQuery依赖: $('#templateSelect').val(selectedTemplateId).trigger('change')
  ⚠️ 第203行: 发现jQuery依赖: $('#templateSelect')
  ⚠️ 第216行: 发现jQuery依赖: $('#templateSelect').change(function()
  ⚠️ 第221行: 发现jQuery依赖: $('#templateDescription').text('请选择一个模板查看描述')
  ⚠️ 第231行: 发现jQuery依赖: $('#templateDescription').text(currentTemplate.description || '无描述')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\edit_inspection_new.html:
  ⚠️ 第471行: 发现jQuery依赖: $.fn.
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\edit_log.html:
  ⚠️ 第223行: 发现jQuery依赖: $('#student_count').val())
  ⚠️ 第224行: 发现jQuery依赖: $('#teacher_count').val())
  ⚠️ 第225行: 发现jQuery依赖: $('#other_count').val())
  ⚠️ 第227行: 发现jQuery依赖: $('#total-count').text(total)
  ⚠️ 第231行: 发现jQuery依赖: $('#student_count, #teacher_count, #other_count').on('input', calculateTotal)
  ⚠️ 第234行: 发现jQuery依赖: $('#logForm').on('submit', function(e)
  ⚠️ 第235行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\fixed_inspection_qrcode.html:
  ⚠️ 第211行: 发现jQuery依赖: $('#successModal').modal('show')
  ❌ 第211行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\index.html:
  ⚠️ 第263行: 发现jQuery依赖: $('#go-to-date').click(function()
  ⚠️ 第264行: 发现jQuery依赖: $('#date-picker').val()
  ⚠️ 第271行: 发现jQuery依赖: $('#create-log').click(function()
  ⚠️ 第272行: 发现jQuery依赖: $('#date-picker').val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\inspection_table_widget.html:
  ⚠️ 第195行: 发现jQuery依赖: $.fn.
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\inspection_templates.html:
  ⚠️ 第293行: 发现jQuery依赖: $('#templateList').html('<div class="col-12 text-center py-5"><i class="fas fa-spinner fa-spin fa-3x"></i></div>')
  ⚠️ 第305行: 发现jQuery依赖: $('#templateList').html('<div class="col-12 text-center py-5"><div class="alert alert-danger">获取模板数据失败</div></div>')
  ⚠️ 第313行: 发现jQuery依赖: $('#templateList').empty()
  ⚠️ 第323行: 发现jQuery依赖: $('#templateList').html('<div class="col-12 text-center py-5"><div class="alert alert-info">暂无模板数据</div></div>')
  ⚠️ 第357行: 发现jQuery依赖: $('#templateList').append(card)
  ⚠️ 第382行: 发现jQuery依赖: $('.category-filter .btn').click(function()
  ⚠️ 第384行: 发现jQuery依赖: $('.category-filter .btn').removeClass('active')
  ⚠️ 第399行: 发现jQuery依赖: $('#addItemBtn').click(function()
  ⚠️ 第404行: 发现jQuery依赖: $('#saveTemplateBtn').click(function()
  ⚠️ 第441行: 发现jQuery依赖: $('#itemList').append(itemRow)
  ⚠️ 第452行: 发现jQuery依赖: $('#templateName').val()
  ⚠️ 第453行: 发现jQuery依赖: $('#templateDescription').val()
  ⚠️ 第454行: 发现jQuery依赖: $('#templateCategory').val()
  ⚠️ 第455行: 发现jQuery依赖: $('#isDefault').prop('checked')
  ⚠️ 第459行: 发现jQuery依赖: $('.item-row').each(function()
  ⚠️ 第506行: 发现jQuery依赖: $('#createTemplateModal').modal('hide')
  ⚠️ 第526行: 发现jQuery依赖: $('#templateName').val('')
  ⚠️ 第527行: 发现jQuery依赖: $('#templateDescription').val('')
  ⚠️ 第528行: 发现jQuery依赖: $('#templateCategory').val('卫生检查')
  ⚠️ 第529行: 发现jQuery依赖: $('#isDefault').prop('checked', false)
  ⚠️ 第532行: 发现jQuery依赖: $('#itemList').empty()
  ⚠️ 第541行: 发现jQuery依赖: $('#editAddItemBtn').click(function()
  ⚠️ 第546行: 发现jQuery依赖: $('#updateTemplateBtn').click(function()
  ⚠️ 第580行: 发现jQuery依赖: $('#editItemList').append(itemRow)
  ⚠️ 第599行: 发现jQuery依赖: $('#editTemplateId').val(template.id)
  ⚠️ 第600行: 发现jQuery依赖: $('#editTemplateName').val(template.name)
  ⚠️ 第601行: 发现jQuery依赖: $('#editTemplateDescription').val(template.description)
  ⚠️ 第602行: 发现jQuery依赖: $('#editTemplateCategory').val(template.category)
  ⚠️ 第603行: 发现jQuery依赖: $('#editIsDefault').prop('checked', template.is_default)
  ⚠️ 第606行: 发现jQuery依赖: $('#editItemList').empty()
  ⚠️ 第619行: 发现jQuery依赖: $('#editTemplateModal').modal('show')
  ⚠️ 第625行: 发现jQuery依赖: $('#editTemplateId').val()
  ⚠️ 第626行: 发现jQuery依赖: $('#editTemplateName').val()
  ⚠️ 第627行: 发现jQuery依赖: $('#editTemplateDescription').val()
  ⚠️ 第628行: 发现jQuery依赖: $('#editTemplateCategory').val()
  ⚠️ 第629行: 发现jQuery依赖: $('#editIsDefault').prop('checked')
  ⚠️ 第633行: 发现jQuery依赖: $('#editItemList .item-row').each(function()
  ⚠️ 第680行: 发现jQuery依赖: $('#editTemplateModal').modal('hide')
  ❌ 第619行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\optimized_dashboard.html:
  ⚠️ 第763行: 发现jQuery依赖: $('#customDateRangeModal').modal('show')
  ⚠️ 第818行: 发现jQuery依赖: $('#customDateRangeModal').modal('hide')
  ❌ 第763行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\photo_upload.html:
  ⚠️ 第371行: 发现jQuery依赖: $('meta[name="csrf-token"]').attr('content')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\public_rate_inspection_photos.html:
  ⚠️ 第403行: 发现jQuery依赖: $('#photoModal').on('show.bs.modal', function (event)
  ⚠️ 第414行: 发现jQuery依赖: $('.star').on('click', function()
  ⚠️ 第432行: 发现jQuery依赖: $('.star').on('mouseenter', function()
  ⚠️ 第448行: 发现jQuery依赖: $('.submit-evaluation-btn').on('click', function()
  ⚠️ 第403行: 过时的事件名: show.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\simplified_inspection.html:
  ⚠️ 第429行: 发现jQuery依赖: $('#morningTable')
  ⚠️ 第436行: 发现jQuery依赖: $('#noonTable')
  ⚠️ 第443行: 发现jQuery依赖: $('#eveningTable')
  ❌ 第566行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第569行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\trainings.html:
  ⚠️ 第118行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ❌ 第118行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\view_training.html:
  ⚠️ 第168行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ❌ 第168行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\components\data_visualization.html:
  ⚠️ 第217行: 发现jQuery依赖: $('#customDateRangeModal').modal('show')
  ⚠️ 第231行: 发现jQuery依赖: $('#customDateRangeModal').modal('show')
  ⚠️ 第265行: 发现jQuery依赖: $('#customDateRangeModal').modal('hide')
  ❌ 第217行: Bootstrap 4 API调用: .modal('show'
  ❌ 第231行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_companions.html:
  ⚠️ 第271行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_events.html:
  ⚠️ 第249行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_inspections.html:
  ⚠️ 第250行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_issues.html:
  ⚠️ 第315行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_log.html:
  ⚠️ 第377行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\print\print_trainings.html:
  ⚠️ 第241行: 发现jQuery依赖: $('.print-page').each(function(index)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\daily_management\widgets\image_widget.html:
  ❌ 第484行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式

📄 app\templates\data_repair\index.html:
  ⚠️ 第169行: 发现jQuery依赖: $('#checkMissingDataBtn').click(function()
  ⚠️ 第188行: 发现jQuery依赖: $('#checkMissingDataBtn').prop('disabled', false)
  ⚠️ 第189行: 发现jQuery依赖: $('#checkMissingDataBtn').html('<i class="fas fa-search"></i> 检查缺失数据')
  ⚠️ 第195行: 发现jQuery依赖: $('#fixRecipesBtn').click(function()
  ⚠️ 第200行: 发现jQuery依赖: $('#fixIngredientsBtn').click(function()
  ⚠️ 第205行: 发现jQuery依赖: $('#fixSuppliersBtn').click(function()
  ⚠️ 第210行: 发现jQuery依赖: $('#checkIntegrityBtn').click(function()
  ⚠️ 第217行: 发现jQuery依赖: $('#checkResultsArea').show()
  ⚠️ 第221行: 发现jQuery依赖: $('#recipesBadge').text(missingRecipes.length)
  ⚠️ 第224行: 发现jQuery依赖: $('#recipesEmpty').hide()
  ⚠️ 第225行: 发现jQuery依赖: $('#recipesTable').show()
  ⚠️ 第227行: 发现jQuery依赖: $('#recipesTableBody')
  ⚠️ 第242行: 发现jQuery依赖: $('#recipesEmpty').show()
  ⚠️ 第243行: 发现jQuery依赖: $('#recipesTable').hide()
  ⚠️ 第248行: 发现jQuery依赖: $('#ingredientsBadge').text(missingIngredients.length)
  ⚠️ 第251行: 发现jQuery依赖: $('#ingredientsEmpty').hide()
  ⚠️ 第252行: 发现jQuery依赖: $('#ingredientsTable').show()
  ⚠️ 第254行: 发现jQuery依赖: $('#ingredientsTableBody')
  ⚠️ 第268行: 发现jQuery依赖: $('#ingredientsEmpty').show()
  ⚠️ 第269行: 发现jQuery依赖: $('#ingredientsTable').hide()
  ⚠️ 第274行: 发现jQuery依赖: $('#suppliersBadge').text(missingSuppliers.length)
  ⚠️ 第277行: 发现jQuery依赖: $('#suppliersEmpty').hide()
  ⚠️ 第278行: 发现jQuery依赖: $('#suppliersTable').show()
  ⚠️ 第280行: 发现jQuery依赖: $('#suppliersTableBody')
  ⚠️ 第293行: 发现jQuery依赖: $('#suppliersEmpty').show()
  ⚠️ 第294行: 发现jQuery依赖: $('#suppliersTable').hide()
  ⚠️ 第317行: 发现jQuery依赖: $('#checkMissingDataBtn').click()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\data_repair\tools.html:
  ⚠️ 第110行: 发现jQuery依赖: $('#runWeeklyMenuTool').click(function()
  ⚠️ 第111行: 发现jQuery依赖: $('#weeklyMenuOperation').val()
  ⚠️ 第116行: 发现jQuery依赖: $('#runRecipeIngredientsTool').click(function()
  ⚠️ 第117行: 发现jQuery依赖: $('#recipeIngredientsOperation').val()
  ⚠️ 第122行: 发现jQuery依赖: $('#runPurchaseOrdersTool').click(function()
  ⚠️ 第123行: 发现jQuery依赖: $('#purchaseOrdersOperation').val()
  ⚠️ 第168行: 发现jQuery依赖: $('#toolResultArea').show()
  ⚠️ 第169行: 发现jQuery依赖: $('#toolResultContent').text(JSON.stringify(data, null, 2))
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\employee\employee_form.html:
  ⚠️ 第414行: 发现jQuery依赖: $('#create_user_account').change(function()
  ⚠️ 第416行: 发现jQuery依赖: $('#system-account-section').show()
  ⚠️ 第418行: 发现jQuery依赖: $('#username').val() === '')
  ⚠️ 第419行: 发现jQuery依赖: $('#username').val($('#phone').val())
  ⚠️ 第422行: 发现jQuery依赖: $('#system-account-section').hide()
  ⚠️ 第427行: 发现jQuery依赖: $('#create_user_account').is(':checked'))
  ⚠️ 第428行: 发现jQuery依赖: $('#system-account-section').show()
  ⚠️ 第430行: 发现jQuery依赖: $('#username').val() === '')
  ⚠️ 第431行: 发现jQuery依赖: $('#username').val($('#phone').val())
  ⚠️ 第437行: 发现jQuery依赖: $('#create_user_account').prop('checked', false)
  ⚠️ 第438行: 发现jQuery依赖: $('#system-account-section').hide()
  ⚠️ 第442行: 发现jQuery依赖: $('#phone').change(function()
  ⚠️ 第443行: 发现jQuery依赖: $('#create_user_account').is(':checked'))
  ⚠️ 第444行: 发现jQuery依赖: $('#username').val($(this).val())
  ⚠️ 第449行: 发现jQuery依赖: $('.area-checkbox').change(function()
  ⚠️ 第454行: 发现jQuery依赖: $('.cert-checkbox').change(function()
  ⚠️ 第480行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第497行: 发现jQuery依赖: $('#role-cards')
  ⚠️ 第501行: 发现jQuery依赖: $('#roles_hidden').val() ? $('#roles_hidden').val().split(',').map(Number)
  ⚠️ 第530行: 发现jQuery依赖: $('.role-card:not(.disabled)').click(function()
  ⚠️ 第544行: 发现jQuery依赖: $('.role-card.selected').each(function()
  ⚠️ 第549行: 发现jQuery依赖: $('#roles_hidden')
  ⚠️ 第557行: 发现jQuery依赖: $('#area_id').change(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\examples\categorized_ingredient_select.html:
  ⚠️ 第57行: 发现jQuery依赖: $('.categorized-ingredient-select').categorizedIngredientSelect()
  ⚠️ 第77行: 发现jQuery依赖: $('.categorized-ingredient-select').categorizedIngredientSelect()
  ⚠️ 第80行: 发现jQuery依赖: $('#ingredient_id').on('change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\accounting_subjects\form.html:
  ⚠️ 第782行: 发现jQuery依赖: $('#parentSubjectModal').modal('show')
  ⚠️ 第830行: 发现jQuery依赖: $('#parentSubjectModal').modal('hide')
  ❌ 第782行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\accounting_subjects\text_tree.html:
  ⚠️ 第185行: 发现jQuery依赖: $('#fontSize').on('change', function()
  ⚠️ 第187行: 发现jQuery依赖: $('.text-display').css('font-size', fontSize)
  ⚠️ 第191行: 发现jQuery依赖: $('#lightTheme').on('click', function()
  ⚠️ 第192行: 发现jQuery依赖: $('.text-display').removeClass('dark-theme')
  ⚠️ 第193行: 发现jQuery依赖: $('#lightTheme').addClass('active')
  ⚠️ 第194行: 发现jQuery依赖: $('#darkTheme').removeClass('active')
  ⚠️ 第197行: 发现jQuery依赖: $('#darkTheme').on('click', function()
  ⚠️ 第198行: 发现jQuery依赖: $('.text-display').addClass('dark-theme')
  ⚠️ 第199行: 发现jQuery依赖: $('#darkTheme').addClass('active')
  ⚠️ 第200行: 发现jQuery依赖: $('#lightTheme').removeClass('active')
  ⚠️ 第204行: 发现jQuery依赖: $('#searchText').on('keyup', function(e)
  ⚠️ 第212行: 发现jQuery依赖: $('#subjectsText').text()
  ⚠️ 第223行: 发现jQuery依赖: $('#subjectsText').text()
  ⚠️ 第242行: 发现jQuery依赖: $('#searchText').val().trim()
  ⚠️ 第243行: 发现jQuery依赖: $('#subjectsText')
  ⚠️ 第270行: 发现jQuery依赖: $('#subjectsText')
  ⚠️ 第277行: 发现jQuery依赖: $('#searchText').val('')
  ⚠️ 第295行: 发现jQuery依赖: $('.alert').remove()
  ⚠️ 第298行: 发现jQuery依赖: $('.card-body').prepend(alert)
  ⚠️ 第302行: 发现jQuery依赖: $('.alert').fadeOut()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\vouchers\create.html:
  ⚠️ 第1111行: 发现jQuery依赖: $('#voucher-tbody tr').length === 0)
  ⚠️ 第1126行: 发现jQuery依赖: $('#voucher-date').val(today)
  ⚠️ 第1142行: 发现jQuery依赖: $('#uf-subject-search').on('input', function()
  ⚠️ 第1146行: 发现jQuery依赖: $('#uf-subject-search').on('keydown', function(e)
  ⚠️ 第1198行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1212行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1230行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1248行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1262行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1298行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第1311行: 发现jQuery依赖: $('.cell-input:focus')
  ⚠️ 第1348行: 发现jQuery依赖: $('.cell-input:focus')
  ⚠️ 第1363行: 发现jQuery依赖: $('#voucher-tbody tr').each(function(index)
  ⚠️ 第1373行: 发现jQuery依赖: $('.debit-amount').each(function()
  ⚠️ 第1378行: 发现jQuery依赖: $('.credit-amount').each(function()
  ⚠️ 第1383行: 发现jQuery依赖: $('#debit-total').html('<span class="uf-currency">¥</span>' + debitTotal.toFixed(2))
  ⚠️ 第1384行: 发现jQuery依赖: $('#credit-total').html('<span class="uf-currency">¥</span>' + creditTotal.toFixed(2))
  ⚠️ 第1389行: 发现jQuery依赖: $('#debit-total').text().replace(/[¥,]/g, '')
  ⚠️ 第1390行: 发现jQuery依赖: $('#credit-total').text().replace(/[¥,]/g, '')
  ⚠️ 第1395行: 发现jQuery依赖: $('#balance-indicator')
  ⚠️ 第1407行: 发现jQuery依赖: $('#current-time').text(timeString)
  ⚠️ 第1475行: 发现jQuery依赖: $('#subjectSelector').show()
  ⚠️ 第1487行: 发现jQuery依赖: $('#uf-subject-search').focus()
  ⚠️ 第1594行: 发现jQuery依赖: $('#subjectTree')
  ⚠️ 第1687行: 发现jQuery依赖: $('.uf-tree-node').removeClass('selected')
  ⚠️ 第1697行: 发现jQuery依赖: $('#selectedSubjectCode').text(subject.code || '-')
  ⚠️ 第1698行: 发现jQuery依赖: $('#selectedSubjectName').text(subject.name || '-')
  ⚠️ 第1699行: 发现jQuery依赖: $('#selectedSubjectType').text(subject.subject_type || '-')
  ⚠️ 第1700行: 发现jQuery依赖: $('#selectedSubjectLevel').text(subject.level || getSubjectLevel(subject.code))
  ⚠️ 第1701行: 发现jQuery依赖: $('#selectedSubjectDirection').text(subject.balance_direction || getBalanceDirection(subject.subject_type))
  ⚠️ 第1706行: 发现jQuery依赖: $('#selectedSubjectInfo').text(infoText)
  ⚠️ 第1750行: 发现jQuery依赖: $('#uf-subject-search').val().trim()
  ⚠️ 第1780行: 发现jQuery依赖: $('#subjectTree')
  ⚠️ 第1835行: 发现jQuery依赖: $('#subjectCount').text(`共 ${displayCount} 个科目`)
  ⚠️ 第1855行: 发现jQuery依赖: $('#subjectSelector').hide()
  ⚠️ 第1858行: 发现jQuery依赖: $('#uf-subject-search').val('')
  ⚠️ 第1863行: 发现jQuery依赖: $('#subjectSelector .uf-content').toggle()
  ⚠️ 第1902行: 发现jQuery依赖: $('#voucher-tbody tr').each(function()
  ⚠️ 第1920行: 发现jQuery依赖: $('#voucher-type').val()
  ⚠️ 第1921行: 发现jQuery依赖: $('#voucher-date').val()
  ⚠️ 第1922行: 发现jQuery依赖: $('#attachment-count').val())
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\vouchers\edit_professional.html:
  ⚠️ 第314行: 发现jQuery依赖: $('#loading-indicator').hide()
  ⚠️ 第320行: 发现jQuery依赖: $('#voucherType, #voucherDate, #attachmentCount').on('change', saveBasicInfo)
  ⚠️ 第332行: 发现jQuery依赖: $('#shortcut-help').show()
  ⚠️ 第334行: 发现jQuery依赖: $('#shortcut-help').hide()
  ⚠️ 第391行: 发现jQuery依赖: $('.voucher-table-section').html(toolbar + table)
  ⚠️ 第421行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第484行: 发现jQuery依赖: $('#voucher-tbody')
  ⚠️ 第498行: 发现jQuery依赖: $('.row-checkbox:checked').closest('tr').remove()
  ⚠️ 第504行: 发现jQuery依赖: $('#voucher-tbody tr').each(function(index)
  ⚠️ 第514行: 发现jQuery依赖: $('.debit-input').each(function()
  ⚠️ 第519行: 发现jQuery依赖: $('.credit-input').each(function()
  ⚠️ 第524行: 发现jQuery依赖: $('#debit-total').text(debitTotal.toFixed(2))
  ⚠️ 第525行: 发现jQuery依赖: $('#credit-total').text(creditTotal.toFixed(2))
  ⚠️ 第529行: 发现jQuery依赖: $('#balance-indicator')
  ⚠️ 第540行: 发现jQuery依赖: $('#balance-indicator')
  ⚠️ 第559行: 发现jQuery依赖: $('#voucherType').val()
  ⚠️ 第560行: 发现jQuery依赖: $('#voucherDate').val()
  ⚠️ 第561行: 发现jQuery依赖: $('#attachmentCount').val())
  ⚠️ 第583行: 发现jQuery依赖: $('#shortcut-help').toggle()
  ⚠️ 第601行: 发现jQuery依赖: $('.alert').remove()
  ⚠️ 第604行: 发现jQuery依赖: $('#alertContainer').html(alert)
  ⚠️ 第608行: 发现jQuery依赖: $('.alert').fadeOut()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\vouchers\index.html:
  ⚠️ 第911行: 发现jQuery依赖: $('#selectAll').on('change', function()
  ⚠️ 第913行: 发现jQuery依赖: $('.voucher-checkbox').prop('checked', isChecked)
  ⚠️ 第919行: 发现jQuery依赖: $('.voucher-checkbox').on('change', function()
  ⚠️ 第927行: 发现jQuery依赖: $('.voucher-checkbox')
  ⚠️ 第928行: 发现jQuery依赖: $('.voucher-checkbox:checked')
  ⚠️ 第929行: 发现jQuery依赖: $('#selectAll').prop('checked', total === checked)
  ⚠️ 第935行: 发现jQuery依赖: $('.voucher-list-table tbody tr').on('click', function(e)
  ⚠️ 第943行: 发现jQuery依赖: $('.voucher-list-table tbody tr').on('dblclick', function()
  ⚠️ 第954行: 发现jQuery依赖: $('select[name="voucher_type"], select[name="status"]').on('change', function()
  ⚠️ 第955行: 发现jQuery依赖: $('#searchForm').submit()
  ⚠️ 第959行: 发现jQuery依赖: $('input[name="keyword"]').on('keypress', function(e)
  ⚠️ 第961行: 发现jQuery依赖: $('#searchForm').submit()
  ⚠️ 第970行: 发现jQuery依赖: $('#selectAll').prop('checked', true).trigger('change')
  ⚠️ 第991行: 发现jQuery依赖: $('.voucher-checkbox').each(function()
  ⚠️ 第1003行: 发现jQuery依赖: $('.voucher-checkbox')
  ⚠️ 第1004行: 发现jQuery依赖: $('.voucher-checkbox:checked')
  ⚠️ 第1010行: 发现jQuery依赖: $('.status-bar span:first-child').text(statusText)
  ⚠️ 第1016行: 发现jQuery依赖: $('.voucher-checkbox:checked').each(function()
  ⚠️ 第1024行: 发现jQuery依赖: $('.voucher-checkbox:checked')
  ⚠️ 第1028行: 发现jQuery依赖: $('.voucher-checkbox:checked').filter(function()
  ⚠️ 第1059行: 发现jQuery依赖: $('.voucher-checkbox:checked').each(function()
  ⚠️ 第1085行: 发现jQuery依赖: $('#rejectVoucherModal').modal('show')
  ⚠️ 第1143行: 发现jQuery依赖: $('#batchGenerateModal').modal('show')
  ⚠️ 第1254行: 发现jQuery依赖: $('.voucher-checkbox:checked').each(function()
  ⚠️ 第1297行: 发现jQuery依赖: $('#batchReviewModal').modal('show')
  ⚠️ 第1302行: 发现jQuery依赖: $('input[name="reviewAction"]').on('change', function()
  ⚠️ 第1312行: 发现jQuery依赖: $('#startBatchReview').on('click', function()
  ⚠️ 第1314行: 发现jQuery依赖: $('.voucher-checkbox:checked').each(function()
  ⚠️ 第1327行: 发现jQuery依赖: $('input[name="reviewAction"]:checked').val()
  ❌ 第1085行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1143行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1297行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\financial\vouchers\text_view.html:
  ⚠️ 第227行: 发现jQuery依赖: $('#fontSize').on('change', function()
  ⚠️ 第229行: 发现jQuery依赖: $('.text-display').css('font-size', fontSize)
  ⚠️ 第233行: 发现jQuery依赖: $('#lightTheme').on('click', function()
  ⚠️ 第234行: 发现jQuery依赖: $('.text-display').removeClass('dark-theme')
  ⚠️ 第235行: 发现jQuery依赖: $('#lightTheme').addClass('active')
  ⚠️ 第236行: 发现jQuery依赖: $('#darkTheme').removeClass('active')
  ⚠️ 第239行: 发现jQuery依赖: $('#darkTheme').on('click', function()
  ⚠️ 第240行: 发现jQuery依赖: $('.text-display').addClass('dark-theme')
  ⚠️ 第241行: 发现jQuery依赖: $('#darkTheme').addClass('active')
  ⚠️ 第242行: 发现jQuery依赖: $('#lightTheme').removeClass('active')
  ⚠️ 第246行: 发现jQuery依赖: $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e)
  ⚠️ 第249行: 发现jQuery依赖: $('#printText').text(content)
  ⚠️ 第253行: 发现jQuery依赖: $('#searchText').on('keyup', function(e)
  ⚠️ 第261行: 发现jQuery依赖: $('.tab-pane.active .text-display')
  ⚠️ 第273行: 发现jQuery依赖: $('.tab-pane.active .text-display')
  ⚠️ 第293行: 发现jQuery依赖: $('#searchText').val().trim()
  ⚠️ 第294行: 发现jQuery依赖: $('.tab-pane.active .text-display')
  ⚠️ 第337行: 发现jQuery依赖: $('.alert').remove()
  ⚠️ 第340行: 发现jQuery依赖: $('.card-body').prepend(alert)
  ⚠️ 第344行: 发现jQuery依赖: $('.alert').fadeOut()
  ⚠️ 第246行: 过时的事件名: shown.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\food_sample\create.html:
  ⚠️ 第167行: 发现jQuery依赖: $('.select2')
  ⚠️ 第172行: 发现jQuery依赖: $('#startTimePicker')
  ⚠️ 第184行: 发现jQuery依赖: $('#sample_image').change(function()
  ⚠️ 第188行: 发现jQuery依赖: $('#imagePreview img').attr('src', e.target.result)
  ⚠️ 第189行: 发现jQuery依赖: $('#imagePreview').show()
  ⚠️ 第193行: 发现jQuery依赖: $('#imagePreview').hide()
  ⚠️ 第198行: 发现jQuery依赖: $('#menu_plan_id').change(function()
  ⚠️ 第205行: 发现jQuery依赖: $('#area_id').val(areaId).trigger('change')
  ⚠️ 第206行: 发现jQuery依赖: $('#meal_date').val(mealDate)
  ⚠️ 第207行: 发现jQuery依赖: $('#meal_type').val(mealType)
  ⚠️ 第215行: 发现jQuery依赖: $('#menu_plan_id').val(menuPlanId).trigger('change')
  ⚠️ 第119行: 过时的data属性: data-target
  💡 建议:
    - 将data-*属性更新为data-bs-*格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\food_trace\qr_scan.html:
  ⚠️ 第132行: 发现jQuery依赖: $('#start-scan').click(function()
  ⚠️ 第137行: 发现jQuery依赖: $('#stop-scan').click(function()
  ⚠️ 第142行: 发现jQuery依赖: $('#scan-file').click(function()
  ⚠️ 第152行: 发现jQuery依赖: $('#manual-trace-form').submit(function(e)
  ⚠️ 第154行: 发现jQuery依赖: $('#batch-number').val().trim()
  ⚠️ 第155行: 发现jQuery依赖: $('#ingredient-name').val().trim()
  ⚠️ 第183行: 发现jQuery依赖: $('#start-scan').hide()
  ⚠️ 第184行: 发现jQuery依赖: $('#stop-scan').show()
  ⚠️ 第194行: 发现jQuery依赖: $('#start-scan').show()
  ⚠️ 第195行: 发现jQuery依赖: $('#stop-scan').hide()
  ⚠️ 第219行: 发现jQuery依赖: $('#scan-result').show()
  ⚠️ 第220行: 发现jQuery依赖: $('#error-message').hide()
  ⚠️ 第265行: 发现jQuery依赖: $('#scan-result').hide()
  ⚠️ 第266行: 发现jQuery依赖: $('#trace-result').show()
  ⚠️ 第310行: 发现jQuery依赖: $('#trace-content').html(html)
  ⚠️ 第314行: 发现jQuery依赖: $('#scan-result').hide()
  ⚠️ 第315行: 发现jQuery依赖: $('#trace-result').hide()
  ⚠️ 第316行: 发现jQuery依赖: $('#error-text').text(message)
  ⚠️ 第317行: 发现jQuery依赖: $('#error-message').show()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\food_trace\sample_management.html:
  ⚠️ 第216行: 发现jQuery依赖: $('#date').val(today)
  ⚠️ 第222行: 发现jQuery依赖: $('#search-btn').click(function()
  ⚠️ 第228行: 发现jQuery依赖: $('#date').val()
  ⚠️ 第229行: 发现jQuery依赖: $('#meal-type').val()
  ⚠️ 第230行: 发现jQuery依赖: $('#area').val()
  ⚠️ 第232行: 发现jQuery依赖: $('#recipe-container')
  ⚠️ 第255行: 发现jQuery依赖: $('#recipe-container').html('<div class="col-12 text-center"><p class="alert alert-warning">' + response.message + '</p></div>')
  ⚠️ 第259行: 发现jQuery依赖: $('#recipe-container').html('<div class="col-12 text-center"><p class="alert alert-danger">加载菜品失败</p></div>')
  ⚠️ 第295行: 发现jQuery依赖: $('#recipe-container').html(html)
  ⚠️ 第301行: 发现jQuery依赖: $('#pagination').html('')
  ⚠️ 第330行: 发现jQuery依赖: $('#pagination').html(html)
  ⚠️ 第333行: 发现jQuery依赖: $('.page-link').click(function(e)
  ⚠️ 第341行: 发现jQuery依赖: $('#select-all').click(function()
  ⚠️ 第342行: 发现jQuery依赖: $('.recipe-checkbox:not(:disabled)').prop('checked', true)
  ⚠️ 第346行: 发现jQuery依赖: $('#deselect-all').click(function()
  ⚠️ 第347行: 发现jQuery依赖: $('.recipe-checkbox:not(:disabled)').prop('checked', false)
  ⚠️ 第351行: 发现jQuery依赖: $('#generate-labels').click(function()
  ⚠️ 第353行: 发现jQuery依赖: $('.recipe-checkbox:checked:not(:disabled)').each(function()
  ⚠️ 第362行: 发现jQuery依赖: $('#date').val()
  ⚠️ 第363行: 发现jQuery依赖: $('#meal-type').val()
  ⚠️ 第364行: 发现jQuery依赖: $('#area').val()
  ⚠️ 第365行: 发现jQuery依赖: $('#sample-quantity').val()
  ⚠️ 第366行: 发现jQuery依赖: $('#storage-hours').val()
  ⚠️ 第367行: 发现jQuery依赖: $('#storage-location').val()
  ⚠️ 第368行: 发现jQuery依赖: $('#storage-temp').val()
  ⚠️ 第376行: 发现jQuery依赖: $('#print-labels').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\guide\scenario_selection.html:
  ⚠️ 第175行: 发现jQuery依赖: $('.scenario-card').click(function()
  ⚠️ 第176行: 发现jQuery依赖: $('.scenario-card').removeClass('selected')
  ⚠️ 第179行: 发现jQuery依赖: $('#confirmScenarioBtn').prop('disabled', false)
  ⚠️ 第183行: 发现jQuery依赖: $('#confirmScenarioBtn').click(function()
  ⚠️ 第190行: 发现jQuery依赖: $('#autoDetectBtn').click(function()
  ⚠️ 第204行: 发现jQuery依赖: $('#scenarioSelectionModal').modal('hide')
  ⚠️ 第240行: 发现jQuery依赖: $('.scenario-card').removeClass('selected')
  ⚠️ 第243行: 发现jQuery依赖: $('#confirmScenarioBtn').prop('disabled', false)
  ⚠️ 第289行: 发现jQuery依赖: $('body').prepend(welcomeHtml)
  ⚠️ 第293行: 发现jQuery依赖: $('.alert-success').fadeOut()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\ingredient\categories.html:
  ⚠️ 第217行: 发现jQuery依赖: $('#category-tree')
  ⚠️ 第226行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第228行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第231行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第245行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第249行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第228行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\ingredient\form.html:
  ⚠️ 第101行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\ingredient\index.html:
  ⚠️ 第240行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第242行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第245行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第259行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第263行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第242行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\ingredient\index_category.html:
  ⚠️ 第245行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第247行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第250行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第264行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第268行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第247行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\inspection\edit.html:
  ⚠️ 第312行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第324行: 发现jQuery依赖: $('#fullImage').attr('src', imageSrc)
  ⚠️ 第336行: 发现jQuery依赖: $('#imageRating').html(ratingHtml)
  ⚠️ 第337行: 发现jQuery依赖: $('#imageModal').modal('show')
  ❌ 第337行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\inspection\view.html:
  ⚠️ 第271行: 发现jQuery依赖: $('#fullImage').attr('src', imageSrc)
  ⚠️ 第283行: 发现jQuery依赖: $('#imageRating').html(ratingHtml)
  ⚠️ 第284行: 发现jQuery依赖: $('#imageModal').modal('show')
  ❌ 第284行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\inventory\statistics.html:
  ❌ 第627行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第630行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 更新事件名为Bootstrap 5的格式

📄 app\templates\inventory_alert\batch_create_requisition.html:
  ⚠️ 第112行: 发现jQuery依赖: $('#required_date').val(tomorrowStr)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\inventory_alert\create_requisition.html:
  ⚠️ 第133行: 发现jQuery依赖: $('#required_date').val(tomorrowStr)
  ⚠️ 第138行: 发现jQuery依赖: $('#quantity').val(suggestedQuantity)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\inventory_alert\index.html:
  ⚠️ 第235行: 发现jQuery依赖: $('#selectAll').change(function()
  ⚠️ 第236行: 发现jQuery依赖: $('.alert-checkbox').prop('checked', $(this).prop('checked'))
  ⚠️ 第241行: 发现jQuery依赖: $('.alert-checkbox').change(function()
  ⚠️ 第247行: 发现jQuery依赖: $('.alert-checkbox:checked')
  ⚠️ 第248行: 发现jQuery依赖: $('#batchProcessBtn, #batchCreateRequisitionBtn').prop('disabled', checkedCount === 0)
  ⚠️ 第252行: 发现jQuery依赖: $('#batchProcessBtn').click(function()
  ⚠️ 第255行: 发现jQuery依赖: $('#batchProcessAlertIds').empty()
  ⚠️ 第258行: 发现jQuery依赖: $('.alert-checkbox:checked').each(function()
  ⚠️ 第259行: 发现jQuery依赖: $('#batchProcessAlertIds').append('<input type="hidden" name="alert_ids[]" value="' + $(this).val() + '">')
  ⚠️ 第263行: 发现jQuery依赖: $('#batchProcessForm').submit()
  ⚠️ 第268行: 发现jQuery依赖: $('#batchCreateRequisitionBtn').click(function()
  ⚠️ 第270行: 发现jQuery依赖: $('#batchCreateRequisitionAlertIds').empty()
  ⚠️ 第273行: 发现jQuery依赖: $('.alert-checkbox:checked').each(function()
  ⚠️ 第274行: 发现jQuery依赖: $('#batchCreateRequisitionAlertIds').append('<input type="hidden" name="alert_ids[]" value="' + $(this).val() + '">')
  ⚠️ 第278行: 发现jQuery依赖: $('#batchCreateRequisitionForm').submit()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\main\canteen_dashboard.html:
  ⚠️ 第419行: 发现jQuery依赖: $('#refreshDashboard, #refreshDashboardMobile').click(function()
  ⚠️ 第456行: 发现jQuery依赖: $('#today-diners-count').text(data.daily_log.total_count)
  ⚠️ 第459行: 发现jQuery依赖: $('#today-inspections-count').text(data.inspections.count)
  ⚠️ 第462行: 发现jQuery依赖: $('#today-companions-count').text(data.dining_companions.count)
  ⚠️ 第465行: 发现jQuery依赖: $('#pending-issues-count').text(data.issues.pending_count)
  ⚠️ 第469行: 发现jQuery依赖: $('#today-diners-link').attr('href', '/daily-management/logs/' + data.daily_log.id)
  ⚠️ 第470行: 发现jQuery依赖: $('#today-inspections-link').attr('href', '/daily-management/logs/' + data.daily_log.id + '/inspections')
  ⚠️ 第471行: 发现jQuery依赖: $('#today-companions-link').attr('href', '/daily-management/logs/' + data.daily_log.id + '/companions')
  ⚠️ 第473行: 发现jQuery依赖: $('#today-diners-link').attr('href', '/daily-management/logs/create?date=' + data.date)
  ⚠️ 第474行: 发现jQuery依赖: $('#today-inspections-link').attr('href', '/daily-management/logs/create?date=' + data.date)
  ⚠️ 第475行: 发现jQuery依赖: $('#today-companions-link').attr('href', '/daily-management/logs/create?date=' + data.date)
  ⚠️ 第478行: 发现jQuery依赖: $('#pending-issues-link').attr('href', '/daily-management/issues?status=pending')
  ⚠️ 第490行: 发现jQuery依赖: $('#' + elementId)
  ⚠️ 第541行: 发现jQuery依赖: $('#today-inspections')
  ⚠️ 第579行: 发现jQuery依赖: $('#pending-issues')
  ⚠️ 第636行: 发现jQuery依赖: $('#weekly-menu').html('<div class="col-12 text-center py-4"><p class="text-danger">加载周菜单数据失败</p></div>')
  ⚠️ 第643行: 发现jQuery依赖: $('#weekly-menu')
  ⚠️ 第693行: 发现jQuery依赖: $('#create-daily-log-btn, #create-daily-log-btn2').click(function(e)
  ⚠️ 第699行: 发现jQuery依赖: $('#add-inspection-btn, #add-inspection-btn2').click(function(e)
  ⚠️ 第718行: 发现jQuery依赖: $('#add-companion-btn').click(function(e)
  ⚠️ 第737行: 发现jQuery依赖: $('#add-training-btn').click(function(e)
  ⚠️ 第756行: 发现jQuery依赖: $('#add-event-btn').click(function(e)
  ⚠️ 第775行: 发现jQuery依赖: $('#add-issue-btn, #add-issue-btn2').click(function(e)
  ⚠️ 第784行: 发现jQuery依赖: $('#create-daily-log-btn-mobile').click(function()
  ⚠️ 第785行: 发现jQuery依赖: $('#create-daily-log-btn').click()
  ⚠️ 第788行: 发现jQuery依赖: $('#add-inspection-btn2-mobile').click(function()
  ⚠️ 第789行: 发现jQuery依赖: $('#add-inspection-btn2').click()
  ⚠️ 第792行: 发现jQuery依赖: $('#add-companion-btn-mobile').click(function()
  ⚠️ 第793行: 发现jQuery依赖: $('#add-companion-btn').click()
  ⚠️ 第796行: 发现jQuery依赖: $('#add-issue-btn2-mobile').click(function()
  ⚠️ 第797行: 发现jQuery依赖: $('#add-issue-btn2').click()
  ⚠️ 第800行: 发现jQuery依赖: $('#add-training-btn-mobile').click(function()
  ⚠️ 第801行: 发现jQuery依赖: $('#add-training-btn').click()
  ⚠️ 第804行: 发现jQuery依赖: $('#add-event-btn-mobile').click(function()
  ⚠️ 第805行: 发现jQuery依赖: $('#add-event-btn').click()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\main\canteen_dashboard_new.html:
  ⚠️ 第833行: 发现jQuery依赖: $('#generate-qr-code-btn').click(function()
  ⚠️ 第850行: 发现jQuery依赖: $('#refreshDashboard').click(function()
  ⚠️ 第868行: 发现jQuery依赖: $('#scenarioSelectionModal').modal('show')
  ⚠️ 第887行: 发现jQuery依赖: $('#companion-qr-code')
  ⚠️ 第905行: 发现jQuery依赖: $('#companion-qr-code').attr('src', originalSrc || "{{ url_for('static', filename='img/qr-code-placeholder.png') }}")
  ⚠️ 第913行: 发现jQuery依赖: $('#create-daily-log-btn').click(function(e)
  ⚠️ 第919行: 发现jQuery依赖: $('#add-inspection-btn').click(function(e)
  ⚠️ 第925行: 发现jQuery依赖: $('#inspection-qrcode-btn').click(function(e)
  ⚠️ 第934行: 发现jQuery依赖: $('#add-companion-btn, #add-companion-btn2').click(function(e)
  ⚠️ 第940行: 发现jQuery依赖: $('#add-training-btn').click(function(e)
  ⚠️ 第946行: 发现jQuery依赖: $('#add-event-btn').click(function(e)
  ⚠️ 第952行: 发现jQuery依赖: $('#add-issue-btn').click(function(e)
  ⚠️ 第965行: 发现jQuery依赖: $('#companions-table-body')
  ⚠️ 第986行: 发现jQuery依赖: $('#companions-table-body')
  ⚠️ 第1068行: 发现jQuery依赖: $('#breakfast-menu, #lunch-menu, #dinner-menu').html(loadingHtml)
  ⚠️ 第1174行: 发现jQuery依赖: $('.companion-entry-link').click(function(e)
  ⚠️ 第1180行: 发现jQuery依赖: $('.training-entry-link').click(function(e)
  ⚠️ 第1186行: 发现jQuery依赖: $('.event-entry-link').click(function(e)
  ⚠️ 第1192行: 发现jQuery依赖: $('.issue-entry-link').click(function(e)
  ⚠️ 第1257行: 发现jQuery依赖: $('#purchase-pending').text(supplyChainData.purchase_orders.pending + '单')
  ⚠️ 第1258行: 发现jQuery依赖: $('#purchase-completed').text(supplyChainData.purchase_orders.completed + '单')
  ⚠️ 第1259行: 发现jQuery依赖: $('#purchase-total').text('¥' + supplyChainData.purchase_orders.total_amount.toFixed(0))
  ⚠️ 第1262行: 发现jQuery依赖: $('#stock-in-today').text(supplyChainData.stock_in.today_batches + '批次')
  ⚠️ 第1263行: 发现jQuery依赖: $('#stock-in-pending').text(supplyChainData.stock_in.pending_inspection + '批次')
  ⚠️ 第1264行: 发现jQuery依赖: $('#stock-in-quality').text(supplyChainData.stock_in.quality_rate + '%')
  ⚠️ 第1267行: 发现jQuery依赖: $('#consumption-plan').text(supplyChainData.consumption_plan.today_plans > 0 ? '已制定' : '未制定')
  ⚠️ 第1268行: 发现jQuery依赖: $('#consumption-rate').text(supplyChainData.consumption_plan.execution_rate + '%')
  ⚠️ 第1269行: 发现jQuery依赖: $('#consumption-cost').text('¥' + supplyChainData.consumption_plan.estimated_cost.toFixed(0))
  ⚠️ 第1272行: 发现jQuery依赖: $('#stock-out-today').text(supplyChainData.stock_out.today_batches + '批次')
  ⚠️ 第1273行: 发现jQuery依赖: $('#stock-out-pending').text(supplyChainData.stock_out.pending_batches + '批次')
  ⚠️ 第1274行: 发现jQuery依赖: $('#stock-out-turnover').text(supplyChainData.stock_out.turnover_status)
  ⚠️ 第1329行: 发现jQuery依赖: $('.notification-item').parent()
  ⚠️ 第1369行: 发现jQuery依赖: $('#todo-container')
  ❌ 第868行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\main\help.html:
  ⚠️ 第1020行: 发现jQuery依赖: $('a[href^="#"]').on('click', function(event)
  ⚠️ 第1027行: 发现jQuery依赖: $('html, body').stop()
  ⚠️ 第1035行: 发现jQuery依赖: $('a[href="#"]').on('click', function(event)
  ⚠️ 第1042行: 发现jQuery依赖: $('#backToTop').fadeIn()
  ⚠️ 第1044行: 发现jQuery依赖: $('#backToTop').fadeOut()
  ⚠️ 第1048行: 发现jQuery依赖: $('#backToTop').click(function()
  ⚠️ 第1049行: 发现jQuery依赖: $('html, body').animate({scrollTop: 0}, 800)
  ⚠️ 第1056行: 发现jQuery依赖: $('.btn-outline-primary, .btn-outline-info, .btn-outline-success, .btn-outline-warning, .btn-outline-danger, .btn-outline-secondary, .btn-outline-dark').each(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\main\index.html:
  ⚠️ 第1584行: 发现jQuery依赖: $('.navbar').addClass('scrolled')
  ⚠️ 第1586行: 发现jQuery依赖: $('.navbar').removeClass('scrolled')
  ⚠️ 第1591行: 发现jQuery依赖: $('a[href^="#"]').on('click', function(event)
  ⚠️ 第1595行: 发现jQuery依赖: $('html, body').stop()
  ⚠️ 第1608行: 发现jQuery依赖: $('#videosLoading').show()
  ⚠️ 第1609行: 发现jQuery依赖: $('#videosContainer').hide()
  ⚠️ 第1610行: 发现jQuery依赖: $('#videoPagination').hide()
  ⚠️ 第1617行: 发现jQuery依赖: $('#videosLoading').hide()
  ⚠️ 第1626行: 发现jQuery依赖: $('#videosContainer').show()
  ⚠️ 第1627行: 发现jQuery依赖: $('#videoPagination').show()
  ⚠️ 第1637行: 发现jQuery依赖: $('#videosLoading').hide()
  ⚠️ 第1650行: 发现jQuery依赖: $('.video-card').addClass('hide')
  ⚠️ 第1657行: 发现jQuery依赖: $('.video-card').addClass('show')
  ⚠️ 第1701行: 发现jQuery依赖: $('#videosContainer').html(html)
  ⚠️ 第1706行: 发现jQuery依赖: $('#paginationInfo').text(`第 ${currentPage} 页 / 共 ${totalPages} 页`)
  ⚠️ 第1709行: 发现jQuery依赖: $('#prevBtn').prop('disabled', currentPage <= 1)
  ⚠️ 第1710行: 发现jQuery依赖: $('#nextBtn').prop('disabled', currentPage >= totalPages)
  ⚠️ 第1723行: 发现jQuery依赖: $('html, body')
  ⚠️ 第1724行: 发现jQuery依赖: $('#videos').offset()
  ⚠️ 第1731行: 发现jQuery依赖: $('#videosContainer').show()
  ⚠️ 第1732行: 发现jQuery依赖: $('#videoPagination').hide()
  ⚠️ 第1751行: 发现jQuery依赖: $('#videosContainer').html(html)
  ⚠️ 第1802行: 发现jQuery依赖: $('#videoModal').remove()
  ⚠️ 第1805行: 发现jQuery依赖: $('body').append(modal)
  ⚠️ 第1806行: 发现jQuery依赖: $('#videoModal').modal('show')
  ⚠️ 第1809行: 发现jQuery依赖: $('#videoModal').on('hidden.bs.modal', function()
  ⚠️ 第1819行: 发现jQuery依赖: $('#videoModal video').on('error', function()
  ⚠️ 第1882行: 发现jQuery依赖: $('.copy-success').remove()
  ⚠️ 第1892行: 发现jQuery依赖: $('body').append(successDiv)
  ⚠️ 第1903行: 发现jQuery依赖: $('.copy-success').remove()
  ⚠️ 第1913行: 发现jQuery依赖: $('body').append(errorDiv)
  ⚠️ 第1924行: 发现jQuery依赖: $('.website-url-card')
  ⚠️ 第1935行: 发现jQuery依赖: $('.website-url-card:hover').length > 0)
  ❌ 第1806行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第1809行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\material_batch\form.html:
  ⚠️ 第164行: 发现jQuery依赖: $('#ingredient_id, #production_date').change(function()
  ⚠️ 第165行: 发现jQuery依赖: $('#ingredient_id').val()
  ⚠️ 第166行: 发现jQuery依赖: $('#production_date').val()
  ⚠️ 第180行: 发现jQuery依赖: $('#expiry_date').val(year + '-' + month + '-' + day)
  ⚠️ 第187行: 发现jQuery依赖: $('#ingredient_id').change(function()
  ⚠️ 第192行: 发现jQuery依赖: $('#unit').val(data.unit)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\material_batch\view.html:
  ⚠️ 第296行: 发现jQuery依赖: $('.delete-document').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\menu_sync\index.html:
  ⚠️ 第158行: 发现jQuery依赖: $('#start_date').val(oneWeekAgo.toISOString().split('T')[0])
  ⚠️ 第159行: 发现jQuery依赖: $('#end_date').val(today.toISOString().split('T')[0])
  ⚠️ 第162行: 发现jQuery依赖: $('#sync-area-btn').click(function()
  ⚠️ 第163行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第164行: 发现jQuery依赖: $('#start_date').val()
  ⚠️ 第165行: 发现jQuery依赖: $('#end_date').val()
  ⚠️ 第173行: 发现jQuery依赖: $('#area-sync-form')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\notification\send.html:
  ⚠️ 第164行: 发现jQuery依赖: $('input[name="send_type"]').change(function()
  ⚠️ 第167行: 发现jQuery依赖: $('#user_selection').hide()
  ⚠️ 第168行: 发现jQuery依赖: $('#area_selection').hide()
  ⚠️ 第169行: 发现jQuery依赖: $('#all_selection').hide()
  ⚠️ 第172行: 发现jQuery依赖: $('#user_selection').show()
  ⚠️ 第174行: 发现jQuery依赖: $('#area_selection').show()
  ⚠️ 第176行: 发现jQuery依赖: $('#all_selection').show()
  ⚠️ 第181行: 发现jQuery依赖: $('#select_all_users').change(function()
  ⚠️ 第182行: 发现jQuery依赖: $('.user-checkbox').prop('checked', $(this).is(':checked'))
  ⚠️ 第186行: 发现jQuery依赖: $('.user-checkbox').change(function()
  ⚠️ 第187行: 发现jQuery依赖: $('.user-checkbox')
  ⚠️ 第188行: 发现jQuery依赖: $('.user-checkbox:checked')
  ⚠️ 第189行: 发现jQuery依赖: $('#select_all_users').prop('checked', totalUsers === checkedUsers)
  ⚠️ 第193行: 发现jQuery依赖: $('#area_id').change(function()
  ⚠️ 第199行: 发现jQuery依赖: $('#area_users_count').text(data.users.length)
  ⚠️ 第200行: 发现jQuery依赖: $('#area_users_preview').show()
  ⚠️ 第204行: 发现jQuery依赖: $('#area_users_preview').hide()
  ⚠️ 第207行: 发现jQuery依赖: $('#area_users_preview').hide()
  ⚠️ 第212行: 发现jQuery依赖: $('#sendNotificationForm').submit(function(e)
  ⚠️ 第213行: 发现jQuery依赖: $('input[name="send_type"]:checked').val()
  ⚠️ 第216行: 发现jQuery依赖: $('.user-checkbox:checked')
  ⚠️ 第223行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第232行: 发现jQuery依赖: $('#title').val()
  ⚠️ 第233行: 发现jQuery依赖: $('#content').val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\adjust_products.html:
  ⚠️ 第167行: 发现jQuery依赖: $('#searchProduct').on('keyup', function()
  ⚠️ 第169行: 发现jQuery依赖: $('.product-row').filter(function()
  ⚠️ 第175行: 发现jQuery依赖: $('#clearSearch').click(function()
  ⚠️ 第176行: 发现jQuery依赖: $('#searchProduct').val('')
  ⚠️ 第177行: 发现jQuery依赖: $('.product-row').show()
  ⚠️ 第181行: 发现jQuery依赖: $('#checkAll').change(function()
  ⚠️ 第182行: 发现jQuery依赖: $('.row-checkbox:visible').prop('checked', $(this).prop('checked'))
  ⚠️ 第187行: 发现jQuery依赖: $('#selectAll').click(function()
  ⚠️ 第188行: 发现jQuery依赖: $('.row-checkbox:visible').prop('checked', true)
  ⚠️ 第193行: 发现jQuery依赖: $('#deselectAll').click(function()
  ⚠️ 第194行: 发现jQuery依赖: $('.row-checkbox:visible').prop('checked', false)
  ⚠️ 第199行: 发现jQuery依赖: $('.row-checkbox').change(function()
  ⚠️ 第205行: 发现jQuery依赖: $('.row-checkbox').each(function()
  ⚠️ 第215行: 发现jQuery依赖: $('.set-unit').click(function(e)
  ⚠️ 第220行: 发现jQuery依赖: $('.row-checkbox:checked').each(function()
  ⚠️ 第226行: 发现jQuery依赖: $('#productForm').submit(function()
  ⚠️ 第229行: 发现jQuery依赖: $('.product-row').each(function()
  ⚠️ 第256行: 发现jQuery依赖: $('#productData').val(JSON.stringify(productData))
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\approve.html:
  ⚠️ 第141行: 发现jQuery依赖: $('#approve_all').change(function()
  ⚠️ 第143行: 发现jQuery依赖: $('#rejectReasonGroup').hide()
  ⚠️ 第145行: 发现jQuery依赖: $('#rejectReasonGroup').show()
  ⚠️ 第150行: 发现jQuery依赖: $('#approve_all').trigger('change')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\index.html:
  ⚠️ 第178行: 发现jQuery依赖: $('.shelf-batch').click(function()
  ⚠️ 第200行: 发现jQuery依赖: $('.unshelf-batch').click(function()
  ⚠️ 第222行: 发现jQuery依赖: $('.delete-batch').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\select_ingredients.html:
  ⚠️ 第118行: 发现jQuery依赖: $('#searchIngredient').on('keyup', function()
  ⚠️ 第120行: 发现jQuery依赖: $('.ingredient-item').filter(function()
  ⚠️ 第126行: 发现jQuery依赖: $('#clearSearch').click(function()
  ⚠️ 第127行: 发现jQuery依赖: $('#searchIngredient').val('')
  ⚠️ 第128行: 发现jQuery依赖: $('.ingredient-item').show()
  ⚠️ 第132行: 发现jQuery依赖: $('#selectAll').click(function()
  ⚠️ 第133行: 发现jQuery依赖: $('.ingredient-item:visible input[type="checkbox"]').prop('checked', true)
  ⚠️ 第138行: 发现jQuery依赖: $('#deselectAll').click(function()
  ⚠️ 第139行: 发现jQuery依赖: $('.ingredient-item:visible input[type="checkbox"]').prop('checked', false)
  ⚠️ 第145行: 发现jQuery依赖: $('.ingredient-item input[type="checkbox"]:checked')
  ⚠️ 第146行: 发现jQuery依赖: $('#selectedCount').text(count)
  ⚠️ 第150行: 发现jQuery依赖: $('.ingredient-item input[type="checkbox"]').change(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\set_attributes.html:
  ⚠️ 第161行: 发现jQuery依赖: $('#price_strategy').change(function()
  ⚠️ 第163行: 发现jQuery依赖: $('#fixedPriceGroup').show()
  ⚠️ 第165行: 发现jQuery依赖: $('#fixedPriceGroup').hide()
  ⚠️ 第170行: 发现jQuery依赖: $('#price_strategy').trigger('change')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\product_batch\view.html:
  ⚠️ 第137行: 发现jQuery依赖: $('.shelf-batch').click(function()
  ⚠️ 第159行: 发现jQuery依赖: $('.unshelf-batch').click(function()
  ⚠️ 第181行: 发现jQuery依赖: $('.delete-batch').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\purchase_order\create_form.html:
  ⚠️ 第483行: 发现jQuery依赖: $('.ingredient-row').each(function()
  ⚠️ 第487行: 发现jQuery依赖: $('#total-amount').text(total.toFixed(2))
  ⚠️ 第492行: 发现jQuery依赖: $('.ingredient-row').length === 0)
  ⚠️ 第493行: 发现jQuery依赖: $('#no-ingredients-message').show()
  ⚠️ 第495行: 发现jQuery依赖: $('#no-ingredients-message').hide()
  ⚠️ 第519行: 发现jQuery依赖: $('#category-filter')
  ⚠️ 第544行: 发现jQuery依赖: $('#ingredients-list-container')
  ⚠️ 第568行: 发现jQuery依赖: $('#ingredient-search').val()
  ⚠️ 第580行: 发现jQuery依赖: $('.select-all-ingredients').prop('disabled', false)
  ⚠️ 第603行: 发现jQuery依赖: $('#ingredients-list-container')
  ⚠️ 第615行: 发现jQuery依赖: $('#category-filter').val()
  ⚠️ 第621行: 发现jQuery依赖: $('#ingredient-search').val()
  ⚠️ 第641行: 发现jQuery依赖: $('.select-all-ingredients').prop('disabled', false)
  ⚠️ 第663行: 发现jQuery依赖: $('#ingredients-list-container')
  ⚠️ 第736行: 发现jQuery依赖: $('#pagination-info')
  ⚠️ 第742行: 发现jQuery依赖: $('#page-info').text(`${pagination.page} / ${pagination.pages}`)
  ⚠️ 第744行: 发现jQuery依赖: $('#prev-page').prop('disabled', !pagination.has_prev)
  ⚠️ 第745行: 发现jQuery依赖: $('#next-page').prop('disabled', !pagination.has_next)
  ⚠️ 第747行: 发现jQuery依赖: $('#pagination-container').show()
  ⚠️ 第752行: 发现jQuery依赖: $('#pagination-info')
  ⚠️ 第758行: 发现jQuery依赖: $('#page-info').text('1 / 1')
  ⚠️ 第759行: 发现jQuery依赖: $('#prev-page').prop('disabled', true)
  ⚠️ 第760行: 发现jQuery依赖: $('#next-page').prop('disabled', true)
  ⚠️ 第761行: 发现jQuery依赖: $('#pagination-container').show()
  ⚠️ 第765行: 发现jQuery依赖: $('#add-ingredient').click(function()
  ⚠️ 第766行: 发现jQuery依赖: $('#ingredientModal').modal('show')
  ⚠️ 第782行: 发现jQuery依赖: $('#load-ingredients').click(function()
  ⚠️ 第783行: 发现jQuery依赖: $('#category-filter').val()
  ⚠️ 第787行: 发现jQuery依赖: $('#category-filter').change(function()
  ⚠️ 第794行: 发现jQuery依赖: $('#prev-page').click(function()
  ⚠️ 第800行: 发现jQuery依赖: $('#next-page').click(function()
  ⚠️ 第808行: 发现jQuery依赖: $('#ingredient-search').on('keyup', function()
  ⚠️ 第819行: 发现jQuery依赖: $('#ingredients-list-container .ingredient-checkbox')
  ⚠️ 第851行: 发现jQuery依赖: $('a[data-bs-toggle="pill"]').on('shown.bs.tab', function (e)
  ⚠️ 第852行: 发现jQuery依赖: $('#ingredient-search').val('')
  ⚠️ 第853行: 发现jQuery依赖: $('.ingredient-item').show()
  ⚠️ 第857行: 发现jQuery依赖: $('#add-selected-ingredients').click(function()
  ⚠️ 第860行: 发现jQuery依赖: $('.ingredient-item').each(function()
  ⚠️ 第874行: 发现jQuery依赖: $('#ingredientModal').modal('hide')
  ⚠️ 第876行: 发现jQuery依赖: $('.ingredient-checkbox').prop('checked', false)
  ⚠️ 第877行: 发现jQuery依赖: $('.select-all-ingredients').html('<i class="fas fa-check-double"></i> 全选当前页')
  ⚠️ 第955行: 发现jQuery依赖: $('.ingredient-row')
  ⚠️ 第1006行: 发现jQuery依赖: $('#ingredients-container').append(template)
  ⚠️ 第1007行: 发现jQuery依赖: $('.ingredient-row').last()
  ⚠️ 第1016行: 发现jQuery依赖: $('#purchase-order-form').submit(function(e)
  ⚠️ 第1017行: 发现jQuery依赖: $('.ingredient-row').length === 0)
  ⚠️ 第1024行: 发现jQuery依赖: $('.ingredient-row').each(function(index)
  ⚠️ 第1036行: 发现jQuery依赖: $('.ingredient-row').each(function()
  ⚠️ 第1110行: 发现jQuery依赖: $('.card-body').first().prepend(infoHtml)
  ⚠️ 第1116行: 发现jQuery依赖: $('#no-ingredients-message').hide()
  ⚠️ 第1152行: 发现jQuery依赖: $('.ingredient-row')
  ⚠️ 第1205行: 发现jQuery依赖: $('#ingredients-container').append(template)
  ⚠️ 第1208行: 发现jQuery依赖: $('.ingredient-row').last()
  ❌ 第766行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第851行: 过时的事件名: shown.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\purchase_order\create_from_menu.html:
  ⚠️ 第1060行: 发现jQuery依赖: $('#ingredientsPreviewModal').modal('show')
  ⚠️ 第1199行: 发现jQuery依赖: $('#ingredientsPreviewModal tbody tr:visible').each(function()
  ⚠️ 第1227行: 发现jQuery依赖: $('#ingredientsPreviewModal tbody tr:visible').each(function()
  ❌ 第1060行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\purchase_order\index.html:
  ⚠️ 第864行: 发现jQuery依赖: $('input[name="start_date"]').val(thirtyDaysAgo.toISOString().split('T')[0])
  ⚠️ 第865行: 发现jQuery依赖: $('input[name="end_date"]').val(today.toISOString().split('T')[0])
  ⚠️ 第868行: 发现jQuery依赖: $('.confirm-btn').click(function()
  ⚠️ 第870行: 发现jQuery依赖: $('#confirmModal').modal('show')
  ⚠️ 第873行: 发现jQuery依赖: $('#confirmOrderBtn').click(function()
  ⚠️ 第900行: 发现jQuery依赖: $('#confirmModal').modal('hide')
  ⚠️ 第906行: 发现jQuery依赖: $('.cancel-btn').click(function()
  ⚠️ 第908行: 发现jQuery依赖: $('#cancelModal').modal('show')
  ⚠️ 第911行: 发现jQuery依赖: $('#cancelOrderBtn').click(function()
  ⚠️ 第914行: 发现jQuery依赖: $('#cancelReason').val().trim()
  ⚠️ 第937行: 发现jQuery依赖: $('#cancelReason').val('')
  ⚠️ 第948行: 发现jQuery依赖: $('#cancelModal').modal('hide')
  ⚠️ 第954行: 发现jQuery依赖: $('.deliver-btn').click(function()
  ⚠️ 第956行: 发现jQuery依赖: $('#deliverModal').modal('show')
  ⚠️ 第959行: 发现jQuery依赖: $('#deliverOrderBtn').click(function()
  ⚠️ 第962行: 发现jQuery依赖: $('#deliveryNotes').val().trim()
  ⚠️ 第980行: 发现jQuery依赖: $('#deliveryNotes').val('')
  ⚠️ 第991行: 发现jQuery依赖: $('#deliverModal').modal('hide')
  ⚠️ 第997行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第999行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第1002行: 发现jQuery依赖: $('#deleteOrderBtn').click(function()
  ⚠️ 第1027行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第1033行: 发现jQuery依赖: $('#filterForm').submit(function(e)
  ⚠️ 第1049行: 发现jQuery依赖: $('#filterForm button[type="reset"]').click(function()
  ⚠️ 第1054行: 发现jQuery依赖: $('#confirmAreaBtn').click(function()
  ⚠️ 第1055行: 发现jQuery依赖: $('#areaSelect').val()
  ⚠️ 第1069行: 发现jQuery依赖: $('tbody tr').each(function()
  ⚠️ 第1135行: 发现jQuery依赖: $('.container-fluid').prepend(alertHtml)
  ⚠️ 第1138行: 发现jQuery依赖: $('.alert-message').fadeOut()
  ⚠️ 第1153行: 发现jQuery依赖: $('.alert-message').remove()
  ⚠️ 第1166行: 发现jQuery依赖: $('.container-fluid').prepend(alertHtml)
  ⚠️ 第1170行: 发现jQuery依赖: $('.alert-message').fadeOut()
  ⚠️ 第1177行: 发现jQuery依赖: $('.alert-message').remove()
  ⚠️ 第1190行: 发现jQuery依赖: $('.container-fluid').prepend(alertHtml)
  ⚠️ 第1194行: 发现jQuery依赖: $('.alert-message').fadeOut()
  ⚠️ 第1317行: 发现jQuery依赖: $('tbody tr')
  ⚠️ 第1328行: 发现jQuery依赖: $('tbody').html(emptyRowHtml)
  ⚠️ 第1336行: 发现jQuery依赖: $('.confirm-btn').off('click').on('click', function()
  ⚠️ 第1338行: 发现jQuery依赖: $('#confirmModal').modal('show')
  ⚠️ 第1342行: 发现jQuery依赖: $('.cancel-btn').off('click').on('click', function()
  ⚠️ 第1344行: 发现jQuery依赖: $('#cancelModal').modal('show')
  ⚠️ 第1348行: 发现jQuery依赖: $('.deliver-btn').off('click').on('click', function()
  ⚠️ 第1350行: 发现jQuery依赖: $('#deliverModal').modal('show')
  ⚠️ 第1354行: 发现jQuery依赖: $('.delete-btn').off('click').on('click', function()
  ⚠️ 第1356行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第1367行: 发现jQuery依赖: $('.modal').hasClass('show'))
  ⚠️ 第1382行: 发现jQuery依赖: $('.confirm-btn, .cancel-btn, .deliver-btn').each(function()
  ❌ 第870行: Bootstrap 4 API调用: .modal('show'
  ❌ 第908行: Bootstrap 4 API调用: .modal('show'
  ❌ 第956行: Bootstrap 4 API调用: .modal('show'
  ❌ 第999行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1338行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1344行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1350行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1356行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\purchase_order\view.html:
  ⚠️ 第725行: 发现jQuery依赖: $('.purchase-order-status')
  ⚠️ 第756行: 发现jQuery依赖: $('.action-buttons .btn').hide()
  ⚠️ 第760行: 发现jQuery依赖: $('.confirm-btn, .cancel-btn, .delete-btn').show()
  ⚠️ 第763行: 发现jQuery依赖: $('.deliver-btn, .cancel-btn').show()
  ⚠️ 第766行: 发现jQuery依赖: $('.create-stock-in-btn').show()
  ⚠️ 第769行: 发现jQuery依赖: $('.delete-btn').show()
  ⚠️ 第775行: 发现jQuery依赖: $('.compact-process-step').removeClass('active completed')
  ⚠️ 第779行: 发现jQuery依赖: $('.compact-process-step[data-step="pending"]').addClass('active')
  ⚠️ 第782行: 发现jQuery依赖: $('.compact-process-step[data-step="pending"]').addClass('completed')
  ⚠️ 第783行: 发现jQuery依赖: $('.compact-process-step[data-step="confirmed"]').addClass('active')
  ⚠️ 第786行: 发现jQuery依赖: $('.compact-process-step[data-step="pending"], .compact-process-step[data-step="confirmed"]').addClass('completed')
  ⚠️ 第787行: 发现jQuery依赖: $('.compact-process-step[data-step="delivered"]').addClass('active')
  ⚠️ 第790行: 发现jQuery依赖: $('.compact-process-step').removeClass('active completed')
  ⚠️ 第791行: 发现jQuery依赖: $('.compact-process-step[data-step="cancelled"]').addClass('active')
  ⚠️ 第806行: 发现jQuery依赖: $('.card-body').first().prepend(alertHtml)
  ⚠️ 第810行: 发现jQuery依赖: $('.alert-success').alert('close')
  ⚠️ 第817行: 发现jQuery依赖: $('.confirm-btn').click(function()
  ⚠️ 第818行: 发现jQuery依赖: $('#confirmModal').modal('show')
  ⚠️ 第821行: 发现jQuery依赖: $('#confirmOrderBtn').click(function()
  ⚠️ 第832行: 发现jQuery依赖: $('#confirmModal').modal('hide')
  ⚠️ 第847行: 发现jQuery依赖: $('.cancel-btn').click(function()
  ⚠️ 第848行: 发现jQuery依赖: $('#cancelModal').modal('show')
  ⚠️ 第851行: 发现jQuery依赖: $('#cancelOrderBtn').click(function()
  ⚠️ 第852行: 发现jQuery依赖: $('#cancelReason').val().trim()
  ⚠️ 第870行: 发现jQuery依赖: $('#cancelModal').modal('hide')
  ⚠️ 第885行: 发现jQuery依赖: $('.deliver-btn').click(function()
  ⚠️ 第886行: 发现jQuery依赖: $('#deliverModal').modal('show')
  ⚠️ 第889行: 发现jQuery依赖: $('#deliverOrderBtn').click(function()
  ⚠️ 第890行: 发现jQuery依赖: $('#deliveryNotes').val().trim()
  ⚠️ 第903行: 发现jQuery依赖: $('#deliverModal').modal('hide')
  ⚠️ 第918行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第919行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第922行: 发现jQuery依赖: $('#deleteOrderBtn').click(function()
  ⚠️ 第942行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第818行: Bootstrap 4 API调用: .modal('show'
  ❌ 第848行: Bootstrap 4 API调用: .modal('show'
  ❌ 第886行: Bootstrap 4 API调用: .modal('show'
  ❌ 第919行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\categories.html:
  ⚠️ 第82行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第84行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第87行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第101行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第105行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第84行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\create.html:
  ⚠️ 第259行: 发现jQuery依赖: $('#main_image').change(function()
  ⚠️ 第263行: 发现jQuery依赖: $('#imagePreview').attr('src', e.target.result)
  ⚠️ 第264行: 发现jQuery依赖: $('#imagePreviewContainer').show()
  ⚠️ 第271行: 发现jQuery依赖: $('#ingredientSearch').on('input', function()
  ⚠️ 第276行: 发现jQuery依赖: $('#categoryFilter').on('change', function()
  ⚠️ 第287行: 发现jQuery依赖: $('#ingredient-' + id).removeClass('disabled').prop('disabled', false)
  ⚠️ 第291行: 发现jQuery依赖: $('#recipeForm').submit(function(e)
  ⚠️ 第295行: 发现jQuery依赖: $('#name').val())
  ⚠️ 第297行: 发现jQuery依赖: $('#name').addClass('is-invalid')
  ⚠️ 第299行: 发现jQuery依赖: $('#name').removeClass('is-invalid')
  ⚠️ 第315行: 发现jQuery依赖: $('#categoryFilter')
  ⚠️ 第337行: 发现jQuery依赖: $('#ingredientList')
  ⚠️ 第347行: 发现jQuery依赖: $('.selected-ingredient').each(function()
  ⚠️ 第356行: 发现jQuery依赖: $('#categoryFilter option').each(function()
  ⚠️ 第367行: 发现jQuery依赖: $('<div class="ingredient-item' + (isDisabled ? ' disabled' : '') + '" id="ingredient-' + ingredient.id + '" data-id="' + ingredient.id + '" data-name="' + ingredient.name + '" data-unit="' + ingredient.unit + '" data-category-id="' + ingredient.category_id + '"' + (isDisabled ? ' disabled' : '') + '></div>')
  ⚠️ 第370行: 发现jQuery依赖: $('<span class="ingredient-name">' + ingredient.name + '</span>')
  ⚠️ 第371行: 发现jQuery依赖: $('<span class="ingredient-category-badge">' + categoryName + '</span>')
  ⚠️ 第386行: 发现jQuery依赖: $('#ingredientList').html('<div class="alert alert-danger">加载食材失败</div>')
  ⚠️ 第393行: 发现jQuery依赖: $('#ingredientSearch').val().toLowerCase()
  ⚠️ 第394行: 发现jQuery依赖: $('#categoryFilter').val()
  ⚠️ 第396行: 发现jQuery依赖: $('.ingredient-item').each(function()
  ⚠️ 第412行: 发现jQuery依赖: $('.ingredient-item:visible')
  ⚠️ 第413行: 发现jQuery依赖: $('.ingredient-item')
  ⚠️ 第419行: 发现jQuery依赖: $('#filterResultInfo').length === 0)
  ⚠️ 第420行: 发现jQuery依赖: $('#ingredientList').before('<div id="filterResultInfo" class="alert alert-info py-1 mb-2">' + resultText + '</div>')
  ⚠️ 第422行: 发现jQuery依赖: $('#filterResultInfo').text(resultText).show()
  ⚠️ 第425行: 发现jQuery依赖: $('#filterResultInfo').hide()
  ⚠️ 第448行: 发现jQuery依赖: $('#selectedIngredients').append(html)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\favorites.html:
  ⚠️ 第165行: 发现jQuery依赖: $('.remove-favorite').click(function()
  ⚠️ 第180行: 发现jQuery依赖: $('.recipe-card').length === 0)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\form.html:
  ⚠️ 第250行: 发现jQuery依赖: $('#main_image').change(function()
  ⚠️ 第254行: 发现jQuery依赖: $('#imagePreview img').attr('src', e.target.result)
  ⚠️ 第255行: 发现jQuery依赖: $('#imagePreview').show()
  ⚠️ 第259行: 发现jQuery依赖: $('#imagePreview').hide()
  ⚠️ 第264行: 发现jQuery依赖: $('#recipeForm').submit(function(e)
  ⚠️ 第278行: 发现jQuery依赖: $('#recipeForm [required]').each(function()
  ⚠️ 第285行: 发现jQuery依赖: $('html, body')
  ⚠️ 第300行: 发现jQuery依赖: $('#recipeForm button[type="submit"]')
  ⚠️ 第314行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第341行: 发现jQuery依赖: $('#recipeForm [required]').on('input change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\form_new.html:
  ⚠️ 第250行: 发现jQuery依赖: $('#main_image').change(function()
  ⚠️ 第254行: 发现jQuery依赖: $('#imagePreview img').attr('src', e.target.result)
  ⚠️ 第255行: 发现jQuery依赖: $('#imagePreview').show()
  ⚠️ 第259行: 发现jQuery依赖: $('#imagePreview').hide()
  ⚠️ 第264行: 发现jQuery依赖: $('#recipeForm').submit(function(e)
  ⚠️ 第278行: 发现jQuery依赖: $('#recipeForm [required]').each(function()
  ⚠️ 第285行: 发现jQuery依赖: $('html, body')
  ⚠️ 第300行: 发现jQuery依赖: $('#recipeForm button[type="submit"]')
  ⚠️ 第314行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第341行: 发现jQuery依赖: $('#recipeForm [required]').on('input change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\form_simplified.html:
  ⚠️ 第257行: 发现jQuery依赖: $('#main_image').change(function()
  ⚠️ 第261行: 发现jQuery依赖: $('#imagePreview img').attr('src', e.target.result)
  ⚠️ 第262行: 发现jQuery依赖: $('#imagePreview').show()
  ⚠️ 第266行: 发现jQuery依赖: $('#imagePreview').hide()
  ⚠️ 第271行: 发现jQuery依赖: $('#ingredientModal').on('show.bs.modal', function()
  ⚠️ 第276行: 发现jQuery依赖: $('#ingredientSearchInput').on('keyup', function()
  ⚠️ 第278行: 发现jQuery依赖: $('#ingredientsList .list-group-item').each(function()
  ⚠️ 第294行: 发现jQuery依赖: $('#recipeForm').submit(function(e)
  ⚠️ 第299行: 发现jQuery依赖: $('#recipeForm [required]').each(function()
  ⚠️ 第314行: 发现jQuery依赖: $('#recipeForm button[type="submit"]')
  ⚠️ 第328行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第352行: 发现jQuery依赖: $('#ingredientsList')
  ⚠️ 第361行: 发现jQuery依赖: $('<a href="#" class="list-group-item list-group-item-action"></a>')
  ⚠️ 第370行: 发现jQuery依赖: $('#ingredientModal').modal('hide')
  ⚠️ 第377行: 发现jQuery依赖: $('#ingredientsList').html('<div class="alert alert-danger">加载食材失败</div>')
  ⚠️ 第385行: 发现jQuery依赖: $('#ingredients-container .ingredient-row[data-id="' + ingredient.id + '"]').length > 0)
  ⚠️ 第390行: 发现jQuery依赖: $('<div class="ingredient-row" data-id="' + ingredient.id + '"></div>')
  ⚠️ 第408行: 发现jQuery依赖: $('#ingredients-container').append(ingredientRow)
  ⚠️ 第271行: 过时的事件名: show.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\index.html:
  ⚠️ 第463行: 发现jQuery依赖: $('.favorite-btn').click(function()
  ⚠️ 第469行: 发现jQuery依赖: $('.copy-btn').click(function()
  ⚠️ 第535行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第537行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第540行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第570行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第585行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第596行: 发现jQuery依赖: $('.favorite-btn').each(function()
  ⚠️ 第615行: 发现jQuery依赖: $('.favorite-btn[data-recipe-id="' + recipeId + '"]').addClass('active')
  ❌ 第537行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\recipe\view.html:
  ⚠️ 第373行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第388行: 发现jQuery依赖: $('#favoriteBtn').click(function()
  ⚠️ 第394行: 发现jQuery依赖: $('#rateRecipeBtn').click(function()
  ⚠️ 第397行: 发现jQuery依赖: $('#rateRecipeModal').modal('show')
  ⚠️ 第402行: 发现jQuery依赖: $('.rating-star')
  ⚠️ 第408行: 发现jQuery依赖: $('#ratingValue').val()
  ⚠️ 第413行: 发现jQuery依赖: $('.rating-star').click(function()
  ⚠️ 第415行: 发现jQuery依赖: $('#ratingValue').val(rating)
  ⚠️ 第420行: 发现jQuery依赖: $('#submitRatingBtn').click(function()
  ⚠️ 第421行: 发现jQuery依赖: $('#ratingValue').val()
  ⚠️ 第422行: 发现jQuery依赖: $('#ratingComment').val()
  ⚠️ 第433行: 发现jQuery依赖: $('#favoriteBtn').data('recipe-id')
  ⚠️ 第441行: 发现jQuery依赖: $('#rateRecipeModal').modal('hide')
  ⚠️ 第457行: 发现jQuery依赖: $('#favoriteBtn').data('recipe-id')
  ⚠️ 第464行: 发现jQuery依赖: $('#favoriteBtn').addClass('active')
  ⚠️ 第484行: 发现jQuery依赖: $('#favoriteBtn').addClass('active')
  ⚠️ 第487行: 发现jQuery依赖: $('#favoriteBtn').removeClass('active')
  ⚠️ 第503行: 发现jQuery依赖: $('.rating-star').removeClass('fas').addClass('far')
  ⚠️ 第507行: 发现jQuery依赖: $('.rating-star[data-rating="' + i + '"]').removeClass('far').addClass('fas')
  ⚠️ 第512行: 发现jQuery依赖: $('.rating-star').css('color', '')
  ⚠️ 第514行: 发现jQuery依赖: $('.rating-star[data-rating="' + i + '"]').css('color', '#ffc107')
  ⚠️ 第517行: 发现jQuery依赖: $('.rating-star').css('color', '')
  ❌ 第397行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\school_admin\user_form.html:
  ⚠️ 第176行: 发现jQuery依赖: $('.role-checkbox').change(function()
  ⚠️ 第223行: 发现jQuery依赖: $('form').submit(function(e)
  ⚠️ 第225行: 发现jQuery依赖: $('.role-checkbox:checked').length === 0)
  ⚠️ 第240行: 发现jQuery依赖: $('.role-checkbox:checked').each(function()
  ⚠️ 第245行: 发现jQuery依赖: $('#roles_hidden').val(selectedRoles)
  ⚠️ 第251行: 发现jQuery依赖: $('#toast-container')
  ⚠️ 第253行: 发现jQuery依赖: $('body').append('<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>')
  ⚠️ 第254行: 发现jQuery依赖: $('#toast-container')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\security\dashboard.html:
  ⚠️ 第180行: 发现jQuery依赖: $('#blocked-ips-list').html(html)
  ⚠️ 第205行: 发现jQuery依赖: $('#block-ip-input').val().trim()
  ⚠️ 第217行: 发现jQuery依赖: $('#block-ip-input').val('')
  ⚠️ 第232行: 发现jQuery依赖: $('#active-sessions').text(data.active_sessions)
  ⚠️ 第233行: 发现jQuery依赖: $('#total-requests').text(data.total_requests)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\batch_editor.html:
  ⚠️ 第256行: 发现jQuery依赖: $("#selectAll").change(function()
  ⚠️ 第257行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', $(this).prop('checked'))
  ⚠️ 第261行: 发现jQuery依赖: $("#applyBulkSupplier").click(function()
  ⚠️ 第262行: 发现jQuery依赖: $("#bulkSupplier").val()
  ⚠️ 第268行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第270行: 发现jQuery依赖: $("select[name='supplier_id_" + itemId + "']").val(supplierId)
  ⚠️ 第275行: 发现jQuery依赖: $("#applyBulkStorageLocation").click(function()
  ⚠️ 第276行: 发现jQuery依赖: $("#bulkStorageLocation").val()
  ⚠️ 第282行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第284行: 发现jQuery依赖: $("select[name='storage_location_id_" + itemId + "']").val(locationId)
  ⚠️ 第289行: 发现jQuery依赖: $("#selectAllBatches").click(function(e)
  ⚠️ 第291行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', true)
  ⚠️ 第292行: 发现jQuery依赖: $("#selectAll").prop('checked', true)
  ⚠️ 第295行: 发现jQuery依赖: $("#deselectAllBatches").click(function(e)
  ⚠️ 第297行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', false)
  ⚠️ 第298行: 发现jQuery依赖: $("#selectAll").prop('checked', false)
  ⚠️ 第302行: 发现jQuery依赖: $("#groupByIngredient").click(function(e)
  ⚠️ 第308行: 发现jQuery依赖: $("#groupByBatch").click(function(e)
  ⚠️ 第315行: 发现jQuery依赖: $(".batch-row").detach()
  ⚠️ 第326行: 发现jQuery依赖: $("#batchTable tbody")
  ⚠️ 第330行: 发现jQuery依赖: $("<tr>").addClass("table-secondary")
  ⚠️ 第335行: 发现jQuery依赖: $("<td>").attr("colspan", 6).text(headerText))
  ⚠️ 第355行: 发现jQuery依赖: $('<div class="total-price mt-2">总价: ' + totalPrice.toFixed(2) + ' 元</div>')
  ⚠️ 第361行: 发现jQuery依赖: $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function()
  ⚠️ 第367行: 发现jQuery依赖: $('.batch-row').each(function()
  ⚠️ 第372行: 发现jQuery依赖: $('#batchEditForm').submit(function(e)
  ⚠️ 第373行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第383行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\batch_editor_simplified_scripts.html:
  ⚠️ 第4行: 发现jQuery依赖: $("#selectAll").change(function()
  ⚠️ 第5行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', $(this).prop('checked'))
  ⚠️ 第9行: 发现jQuery依赖: $("#applyBulkSupplier").click(function()
  ⚠️ 第10行: 发现jQuery依赖: $("#bulkSupplier").val()
  ⚠️ 第16行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第18行: 发现jQuery依赖: $("select[name='supplier_id_" + itemId + "']").val(supplierId)
  ⚠️ 第23行: 发现jQuery依赖: $("#applyBulkStorageLocation").click(function()
  ⚠️ 第24行: 发现jQuery依赖: $("#bulkStorageLocation").val()
  ⚠️ 第30行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第32行: 发现jQuery依赖: $("select[name='storage_location_id_" + itemId + "']").val(locationId)
  ⚠️ 第37行: 发现jQuery依赖: $("#applyBulkDates").click(function()
  ⚠️ 第38行: 发现jQuery依赖: $("#bulkProductionDate").val()
  ⚠️ 第50行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第52行: 发现jQuery依赖: $("input[name='production_date_" + itemId + "']").val(productionDate)
  ⚠️ 第53行: 发现jQuery依赖: $("input[name='expiry_date_" + itemId + "']").val(expiryDateStr)
  ⚠️ 第58行: 发现jQuery依赖: $("#selectAllBatches").click(function(e)
  ⚠️ 第60行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', true)
  ⚠️ 第61行: 发现jQuery依赖: $("#selectAll").prop('checked', true)
  ⚠️ 第64行: 发现jQuery依赖: $("#deselectAllBatches").click(function(e)
  ⚠️ 第66行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', false)
  ⚠️ 第67行: 发现jQuery依赖: $("#selectAll").prop('checked', false)
  ⚠️ 第71行: 发现jQuery依赖: $("#groupByIngredient").click(function(e)
  ⚠️ 第77行: 发现jQuery依赖: $("#groupBySupplier").click(function(e)
  ⚠️ 第84行: 发现jQuery依赖: $(".batch-row").detach()
  ⚠️ 第95行: 发现jQuery依赖: $("#batchTable tbody")
  ⚠️ 第99行: 发现jQuery依赖: $("<tr>").addClass("table-secondary")
  ⚠️ 第109行: 发现jQuery依赖: $("<td>").attr("colspan", 6).text(headerText))
  ⚠️ 第134行: 发现jQuery依赖: $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function()
  ⚠️ 第140行: 发现jQuery依赖: $('.batch-row').each(function()
  ⚠️ 第145行: 发现jQuery依赖: $(".document-input").change(function(e)
  ⚠️ 第148行: 发现jQuery依赖: $("#document_list_" + itemId)
  ⚠️ 第157行: 发现jQuery依赖: $('<div class="document-item"></div>')
  ⚠️ 第202行: 发现jQuery依赖: $("#applyBulkDocument").click(function()
  ⚠️ 第203行: 发现jQuery依赖: $("#bulkDocumentUpload")
  ⚠️ 第210行: 发现jQuery依赖: $(".batch-checkbox:checked")
  ⚠️ 第256行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第257行: 发现jQuery依赖: $('.batch-checkbox')
  ⚠️ 第262行: 发现jQuery依赖: $('#selectedCount').text(selectedCount)
  ⚠️ 第263行: 发现jQuery依赖: $('#totalCount').text(totalCount)
  ⚠️ 第266行: 发现jQuery依赖: $('.batch-row').each(function()
  ⚠️ 第300行: 发现jQuery依赖: $('.batch-checkbox').on('change', function()
  ⚠️ 第323行: 发现jQuery依赖: $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function()
  ⚠️ 第334行: 发现jQuery依赖: $('#batchEditForm').submit(function(e)
  ⚠️ 第337行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第349行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第391行: 发现jQuery依赖: $('#direct_approve').is(':checked'))
  ⚠️ 第398行: 发现jQuery依赖: $('#saveBtn')
  ⚠️ 第404行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第450行: 发现jQuery依赖: $('#alertContainer').html(alertHtml)
  ⚠️ 第453行: 发现jQuery依赖: $('html, body').animate({ scrollTop: 0 }, 'slow')
  ⚠️ 第482行: 发现jQuery依赖: $('#alertContainer').html(alertHtml)
  ⚠️ 第485行: 发现jQuery依赖: $('html, body').animate({ scrollTop: 0 }, 'slow')
  ⚠️ 第496行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第503行: 发现jQuery依赖: $('#batchNumbersList').toggle()
  ⚠️ 第508行: 发现jQuery依赖: $('#batch_numbers').val()
  ⚠️ 第514行: 发现jQuery依赖: $('#batch_numbers').val(lines.join('\n'))
  ⚠️ 第519行: 发现jQuery依赖: $('#uploadDocumentForm').submit(function(e)
  ⚠️ 第534行: 发现jQuery依赖: $('#uploadDocumentModal').modal('hide')
  ⚠️ 第573行: 发现jQuery依赖: $('#image_' + docId).show()
  ⚠️ 第574行: 发现jQuery依赖: $('#loading_' + docId).hide()
  ⚠️ 第579行: 发现jQuery依赖: $('#loading_' + docId).hide()
  ⚠️ 第606行: 发现jQuery依赖: $('img[id^="image_"]').each(function()
  ⚠️ 第618行: 发现jQuery依赖: $('#loading_' + docId)
  ⚠️ 第620行: 发现jQuery依赖: $('#image_' + docId)
  ⚠️ 第629行: 发现jQuery依赖: $('#previewBtn').click(function()
  ⚠️ 第634行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第672行: 发现jQuery依赖: $('#previewDialog').remove()
  ⚠️ 第675行: 发现jQuery依赖: $('<div id="previewDialog" title="A4横向打印预览"></div>')
  ⚠️ 第768行: 发现jQuery依赖: $('body').append(previewDialog)
  ⚠️ 第778行: 发现jQuery依赖: $('#printBtn').click()
  ⚠️ 第806行: 发现jQuery依赖: $('#printBtn').click(function()
  ⚠️ 第808行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第821行: 发现jQuery依赖: $('#confirmEntryBtn').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\batch_editor_step1.html:
  ⚠️ 第319行: 发现jQuery依赖: $("#selectAll").change(function()
  ⚠️ 第320行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', $(this).prop('checked'))
  ⚠️ 第324行: 发现jQuery依赖: $("#applyBulkSupplier").click(function()
  ⚠️ 第325行: 发现jQuery依赖: $("#bulkSupplier").val()
  ⚠️ 第331行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第333行: 发现jQuery依赖: $("select[name='supplier_id_" + itemId + "']").val(supplierId)
  ⚠️ 第338行: 发现jQuery依赖: $("#applyBulkStorageLocation").click(function()
  ⚠️ 第339行: 发现jQuery依赖: $("#bulkStorageLocation").val()
  ⚠️ 第345行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第347行: 发现jQuery依赖: $("select[name='storage_location_id_" + itemId + "']").val(locationId)
  ⚠️ 第352行: 发现jQuery依赖: $("#selectAllBatches").click(function(e)
  ⚠️ 第354行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', true)
  ⚠️ 第355行: 发现jQuery依赖: $("#selectAll").prop('checked', true)
  ⚠️ 第358行: 发现jQuery依赖: $("#deselectAllBatches").click(function(e)
  ⚠️ 第360行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', false)
  ⚠️ 第361行: 发现jQuery依赖: $("#selectAll").prop('checked', false)
  ⚠️ 第365行: 发现jQuery依赖: $("#groupByIngredient").click(function(e)
  ⚠️ 第371行: 发现jQuery依赖: $("#groupByBatch").click(function(e)
  ⚠️ 第378行: 发现jQuery依赖: $(".batch-row").detach()
  ⚠️ 第389行: 发现jQuery依赖: $("#batchTable tbody")
  ⚠️ 第393行: 发现jQuery依赖: $("<tr>").addClass("table-secondary")
  ⚠️ 第398行: 发现jQuery依赖: $("<td>").attr("colspan", 6).text(headerText))
  ⚠️ 第418行: 发现jQuery依赖: $('<div class="total-price mt-2">总价: ' + totalPrice.toFixed(2) + ' 元</div>')
  ⚠️ 第424行: 发现jQuery依赖: $('input[name^="quantity_"], input[name^="unit_price_"]').on('input', function()
  ⚠️ 第430行: 发现jQuery依赖: $('.batch-row').each(function()
  ⚠️ 第435行: 发现jQuery依赖: $('#batchEditForm').submit(function(e)
  ⚠️ 第436行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第446行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\batch_editor_step2.html:
  ⚠️ 第411行: 发现jQuery依赖: $("#selectAll").change(function()
  ⚠️ 第412行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', $(this).prop('checked'))
  ⚠️ 第416行: 发现jQuery依赖: $("#applyBulkProductionDate").click(function()
  ⚠️ 第417行: 发现jQuery依赖: $("#bulkProductionDate").val()
  ⚠️ 第423行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第425行: 发现jQuery依赖: $("input[name='production_date_" + itemId + "']").val(date)
  ⚠️ 第430行: 发现jQuery依赖: $("#applyBulkExpiryDate").click(function()
  ⚠️ 第431行: 发现jQuery依赖: $("#bulkExpiryDate").val()
  ⚠️ 第437行: 发现jQuery依赖: $(".batch-checkbox:checked").each(function()
  ⚠️ 第439行: 发现jQuery依赖: $("input[name='expiry_date_" + itemId + "']").val(date)
  ⚠️ 第444行: 发现jQuery依赖: $("#selectAllBatches").click(function(e)
  ⚠️ 第446行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', true)
  ⚠️ 第447行: 发现jQuery依赖: $("#selectAll").prop('checked', true)
  ⚠️ 第450行: 发现jQuery依赖: $("#deselectAllBatches").click(function(e)
  ⚠️ 第452行: 发现jQuery依赖: $(".batch-checkbox").prop('checked', false)
  ⚠️ 第453行: 发现jQuery依赖: $("#selectAll").prop('checked', false)
  ⚠️ 第457行: 发现jQuery依赖: $("#groupByIngredient").click(function(e)
  ⚠️ 第463行: 发现jQuery依赖: $("#groupBySupplier").click(function(e)
  ⚠️ 第470行: 发现jQuery依赖: $(".batch-row").detach()
  ⚠️ 第481行: 发现jQuery依赖: $("#batchTable tbody")
  ⚠️ 第485行: 发现jQuery依赖: $("<tr>").addClass("table-secondary")
  ⚠️ 第494行: 发现jQuery依赖: $("<td>").attr("colspan", 6).text(headerText))
  ⚠️ 第504行: 发现jQuery依赖: $(".document-input").change(function(e)
  ⚠️ 第507行: 发现jQuery依赖: $("#document_list_" + itemId)
  ⚠️ 第519行: 发现jQuery依赖: $('<div class="document-item"></div>')
  ⚠️ 第564行: 发现jQuery依赖: $("#applyBulkDocument").click(function()
  ⚠️ 第565行: 发现jQuery依赖: $("#bulkDocumentUpload")
  ⚠️ 第572行: 发现jQuery依赖: $(".batch-checkbox:checked")
  ⚠️ 第614行: 发现jQuery依赖: $('#batchEditForm').submit(function(e)
  ⚠️ 第615行: 发现jQuery依赖: $('.batch-checkbox:checked')
  ⚠️ 第625行: 发现jQuery依赖: $('.batch-checkbox:checked').each(function()
  ⚠️ 第659行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\create_from_purchase.html:
  ⚠️ 第137行: 发现jQuery依赖: $('#stock_in_date').val(today)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\create_from_purchase_order.html:
  ⚠️ 第384行: 发现jQuery依赖: $('#stockInForm').on('submit', function(e)
  ⚠️ 第400行: 发现jQuery依赖: $('#itemsTable tbody tr:visible')
  ⚠️ 第428行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...')
  ⚠️ 第439行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> 创建入库单')
  ⚠️ 第445行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> 创建入库单')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\edit.html:
  ⚠️ 第1013行: 发现jQuery依赖: $('#importFromPurchaseBtn').on('click', function()
  ⚠️ 第1016行: 发现jQuery依赖: $('#purchaseOrderModal').modal('show')
  ⚠️ 第1047行: 发现jQuery依赖: $('#importedItemsTable').empty()
  ⚠️ 第1062行: 发现jQuery依赖: $('#batch_production_date').val(defaultProductionDate)
  ⚠️ 第1063行: 发现jQuery依赖: $('#batch_expiry_date').val(defaultExpiryDateStr)
  ⚠️ 第1106行: 发现jQuery依赖: $('#importedItemsTable').append(rowHtml)
  ⚠️ 第1110行: 发现jQuery依赖: $('#importedIngredientsContainer').show()
  ⚠️ 第1113行: 发现jQuery依赖: $('#purchaseOrderModal').modal('hide')
  ⚠️ 第1127行: 发现jQuery依赖: $('#selectAll').change(function()
  ⚠️ 第1128行: 发现jQuery依赖: $('.select-item').prop('checked', $(this).prop('checked'))
  ⚠️ 第1133行: 发现jQuery依赖: $('.select-item:not(:checked)')
  ⚠️ 第1134行: 发现jQuery依赖: $('#selectAll').prop('checked', allChecked)
  ⚠️ 第1138行: 发现jQuery依赖: $('#selectAllItems').click(function()
  ⚠️ 第1139行: 发现jQuery依赖: $('.select-item, #selectAll').prop('checked', true)
  ⚠️ 第1143行: 发现jQuery依赖: $('#deselectAllItems').click(function()
  ⚠️ 第1144行: 发现jQuery依赖: $('.select-item, #selectAll').prop('checked', false)
  ⚠️ 第1148行: 发现jQuery依赖: $('#generateBatchNumber').click(function()
  ⚠️ 第1149行: 发现jQuery依赖: $('#batchNumberPrefix').val()
  ⚠️ 第1154行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1161行: 发现jQuery依赖: $('#applySupplier').click(function()
  ⚠️ 第1162行: 发现jQuery依赖: $('#globalSupplier').val()
  ⚠️ 第1168行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1175行: 发现jQuery依赖: $('#applyStorageLocation').click(function()
  ⚠️ 第1176行: 发现jQuery依赖: $('#batch_storage_location').val()
  ⚠️ 第1182行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1189行: 发现jQuery依赖: $('#applyProductionDate').click(function()
  ⚠️ 第1190行: 发现jQuery依赖: $('#batch_production_date').val()
  ⚠️ 第1196行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1203行: 发现jQuery依赖: $('#applyExpiryDate').click(function()
  ⚠️ 第1204行: 发现jQuery依赖: $('#batch_expiry_date').val()
  ⚠️ 第1210行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1217行: 发现jQuery依赖: $('#applyUnitPrice').click(function()
  ⚠️ 第1218行: 发现jQuery依赖: $('#batch_unit_price').val()
  ⚠️ 第1224行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1231行: 发现jQuery依赖: $('#applyDocument').click(function()
  ⚠️ 第1232行: 发现jQuery依赖: $('#relatedDocument').val()
  ⚠️ 第1243行: 发现jQuery依赖: $('#cancelImport').click(function()
  ⚠️ 第1245行: 发现jQuery依赖: $('#importedIngredientsContainer').hide()
  ⚠️ 第1246行: 发现jQuery依赖: $('#importedItemsTable').empty()
  ⚠️ 第1251行: 发现jQuery依赖: $('#batchSaveBtn').click(function()
  ⚠️ 第1256行: 发现jQuery依赖: $('.select-item:checked').each(function()
  ⚠️ 第1368行: 发现jQuery依赖: $('#documentUploadBtn').click(function()
  ⚠️ 第1369行: 发现jQuery依赖: $('#documentUploadModal').modal('show')
  ⚠️ 第1373行: 发现jQuery依赖: $('#uploadDocumentForm').submit(function(e)
  ⚠️ 第1388行: 发现jQuery依赖: $('#documentUploadModal').modal('hide')
  ⚠️ 第1391行: 发现jQuery依赖: $('#selectedDocumentId').val(response.document.id)
  ⚠️ 第1392行: 发现jQuery依赖: $('#associateItemsModal').modal('show')
  ⚠️ 第1404行: 发现jQuery依赖: $('#saveAssociationBtn').click(function()
  ⚠️ 第1405行: 发现jQuery依赖: $('#selectedDocumentId').val()
  ⚠️ 第1408行: 发现jQuery依赖: $('.select-item-for-document:checked').each(function()
  ⚠️ 第1425行: 发现jQuery依赖: $('#associateItemsModal').modal('hide')
  ⚠️ 第1437行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第1445行: 发现jQuery依赖: $('#inspectionItemName').text(itemName)
  ⚠️ 第1446行: 发现jQuery依赖: $('#inspectionItemId').val(itemId)
  ⚠️ 第1450行: 发现jQuery依赖: $('#inspection_type option').each(function()
  ⚠️ 第1461行: 发现jQuery依赖: $('#inspectionModal').modal('show')
  ⚠️ 第1497行: 发现jQuery依赖: $('#inspectionRecordsList').html(html)
  ⚠️ 第1505行: 发现jQuery依赖: $('#saveInspectionBtn').click(function()
  ⚠️ 第1506行: 发现jQuery依赖: $('#inspectionItemId').val()
  ⚠️ 第1507行: 发现jQuery依赖: $('#inspection_type').val()
  ⚠️ 第1508行: 发现jQuery依赖: $('input[name="result"]:checked').val()
  ⚠️ 第1509行: 发现jQuery依赖: $('#inspection_document').val()
  ⚠️ 第1510行: 发现jQuery依赖: $('#inspection_notes').val()
  ⚠️ 第1536行: 发现jQuery依赖: $('#inspection_type').val('')
  ⚠️ 第1537行: 发现jQuery依赖: $('input[name="result"]').prop('checked', false)
  ⚠️ 第1538行: 发现jQuery依赖: $('#inspection_document').val('')
  ⚠️ 第1539行: 发现jQuery依赖: $('#inspection_notes').val('')
  ⚠️ 第1551行: 发现jQuery依赖: $('#stockInBtn').click(function()
  ❌ 第1016行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1369行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1392行: Bootstrap 4 API调用: .modal('show'
  ❌ 第1461行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\form.html:
  ⚠️ 第612行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第622行: 发现jQuery依赖: $('#preCheckList input[type="checkbox"]').on('change', function()
  ⚠️ 第628行: 发现jQuery依赖: $('#preCheckList input[type="checkbox"]').each(function()
  ⚠️ 第637行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', false)
  ⚠️ 第639行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', true)
  ⚠️ 第645行: 发现jQuery依赖: $('#directCreateFormContainer').show()
  ⚠️ 第646行: 发现jQuery依赖: $('#purchaseOrderListContainer').hide()
  ⚠️ 第651行: 发现jQuery依赖: $('#purchaseOrderListContainer').show()
  ⚠️ 第652行: 发现jQuery依赖: $('#directCreateFormContainer').hide()
  ⚠️ 第657行: 发现jQuery依赖: $('#purchaseOrderListContainer').hide()
  ⚠️ 第662行: 发现jQuery依赖: $('button[type="submit"]').prop('disabled', true)
  ⚠️ 第665行: 发现jQuery依赖: $('#stock_in_date').val() === '')
  ⚠️ 第671行: 发现jQuery依赖: $('#stock_in_date').val(today)
  ⚠️ 第675行: 发现jQuery依赖: $('#orderSearchInput').on('keyup', function()
  ⚠️ 第677行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr').each(function()
  ⚠️ 第690行: 发现jQuery依赖: $('.btn-group button[data-filter]').on('click', function()
  ⚠️ 第694行: 发现jQuery依赖: $('.btn-group button[data-filter]').removeClass('active')
  ⚠️ 第699行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr').show()
  ⚠️ 第701行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr').hide()
  ⚠️ 第702行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr[data-status="' + filter + '"]').show()
  ⚠️ 第708行: 发现jQuery依赖: $('#sortByDate').on('click', function()
  ⚠️ 第709行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr').get()
  ⚠️ 第725行: 发现jQuery依赖: $('#purchaseOrderTable tbody').append(row)
  ⚠️ 第730行: 发现jQuery依赖: $('#sortBySupplier').on('click', function()
  ⚠️ 第731行: 发现jQuery依赖: $('#purchaseOrderTable tbody tr').get()
  ⚠️ 第747行: 发现jQuery依赖: $('#purchaseOrderTable tbody').append(row)
  ⚠️ 第752行: 发现jQuery依赖: $('.preview-btn').on('click', function()
  ⚠️ 第754行: 发现jQuery依赖: $('#selectOrderBtn').attr('href', "{{ url_for('stock_in.create_from_purchase', purchase_order_id=0) }}".replace('0', orderId))
  ⚠️ 第757行: 发现jQuery依赖: $('#orderPreviewModal').modal('show')
  ⚠️ 第851行: 发现jQuery依赖: $('#orderPreviewContent').html(html)
  ⚠️ 第853行: 发现jQuery依赖: $('#orderPreviewContent')
  ⚠️ 第861行: 发现jQuery依赖: $('#orderPreviewContent')
  ❌ 第757行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\view.html:
  ⚠️ 第1057行: 发现jQuery依赖: $('#selectedDocumentId').val(documentId)
  ⚠️ 第1058行: 发现jQuery依赖: $('#docItemsModal' + documentId).modal('hide')
  ⚠️ 第1059行: 发现jQuery依赖: $('#associateItemsModal').modal('show')
  ⚠️ 第1063行: 发现jQuery依赖: $('#saveAssociationBtn').click(function()
  ⚠️ 第1064行: 发现jQuery依赖: $('#selectedDocumentId').val()
  ⚠️ 第1067行: 发现jQuery依赖: $('.select-item-for-document:checked').each(function()
  ⚠️ 第1084行: 发现jQuery依赖: $('#associateItemsModal').modal('hide')
  ⚠️ 第1098行: 发现jQuery依赖: $('#uploadDocumentForm').submit(function(e)
  ⚠️ 第1113行: 发现jQuery依赖: $('#uploadDocumentModal').modal('hide')
  ⚠️ 第1128行: 发现jQuery依赖: $('#stockInBtn').click(function()
  ⚠️ 第1151行: 发现jQuery依赖: $('#cancelStockInBtn').click(function()
  ⚠️ 第1194行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第1201行: 发现jQuery依赖: $('#batchNumbersList').toggle()
  ⚠️ 第1206行: 发现jQuery依赖: $('#batch_numbers')
  ❌ 第1059行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\wizard.html:
  ⚠️ 第738行: 发现jQuery依赖: $('#stock_in_id').val()
  ⚠️ 第742行: 发现jQuery依赖: $('#createFromPurchaseBtn').on('click', function()
  ⚠️ 第745行: 发现jQuery依赖: $('#selectPurchaseOrderModal').modal('show')
  ⚠️ 第756行: 发现jQuery依赖: $('#default_storage_location').val())
  ⚠️ 第765行: 发现jQuery依赖: $('#selectPurchaseOrderModal').modal('hide')
  ⚠️ 第769行: 发现jQuery依赖: $('#createManuallyBtn').on('click', function()
  ⚠️ 第775行: 发现jQuery依赖: $('#step1NextBtn').on('click', function()
  ⚠️ 第782行: 发现jQuery依赖: $('#stockInBasicForm')[0].checkValidity())
  ⚠️ 第783行: 发现jQuery依赖: $('#stockInBasicForm')[0].reportValidity()
  ⚠️ 第789行: 发现jQuery依赖: $('#warehouse_id').val()
  ⚠️ 第790行: 发现jQuery依赖: $('#stock_in_type').val()
  ⚠️ 第791行: 发现jQuery依赖: $('#stock_in_date').val()
  ⚠️ 第792行: 发现jQuery依赖: $('#notes').val()
  ⚠️ 第793行: 发现jQuery依赖: $('#default_storage_location').val()
  ⚠️ 第806行: 发现jQuery依赖: $('#stock_in_id').val(stockInId)
  ⚠️ 第825行: 发现jQuery依赖: $('#stockInBasicForm')[0].checkValidity())
  ⚠️ 第827行: 发现jQuery依赖: $('#stockInBasicForm')[0].reportValidity()
  ⚠️ 第833行: 发现jQuery依赖: $('#warehouse_id').val()
  ⚠️ 第834行: 发现jQuery依赖: $('#stock_in_type').val()
  ⚠️ 第835行: 发现jQuery依赖: $('#stock_in_date').val()
  ⚠️ 第836行: 发现jQuery依赖: $('#notes').val()
  ⚠️ 第837行: 发现jQuery依赖: $('#default_storage_location').val()
  ⚠️ 第844行: 发现jQuery依赖: $('<button class="btn btn-primary" disabled><i class="fas fa-spinner fa-spin"></i> 正在创建...</button>')
  ⚠️ 第845行: 发现jQuery依赖: $('.select-order-btn').prop('disabled', true)
  ⚠️ 第856行: 发现jQuery依赖: $('.select-order-btn').prop('disabled', false)
  ⚠️ 第861行: 发现jQuery依赖: $('#stock_in_id').val(stockInId)
  ⚠️ 第877行: 发现jQuery依赖: $('.select-order-btn').prop('disabled', false)
  ⚠️ 第891行: 发现jQuery依赖: $('.wizard-step').addClass('hidden-step')
  ⚠️ 第896行: 发现jQuery依赖: $('.step').removeClass('active completed')
  ⚠️ 第918行: 发现jQuery依赖: $('#inspectionItemsContainer').html('<div class="alert alert-info">请先保存基础信息</div>')
  ⚠️ 第919行: 发现jQuery依赖: $('#normalItemsContainer').html('<div class="alert alert-info">请先保存基础信息</div>')
  ⚠️ 第924行: 发现jQuery依赖: $('#inspectionItemsContainer').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</div>')
  ⚠️ 第925行: 发现jQuery依赖: $('#normalItemsContainer').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</div>')
  ⚠️ 第930行: 发现jQuery依赖: $('#inspectionItemsContainer').html('')
  ⚠️ 第931行: 发现jQuery依赖: $('#normalItemsContainer').html('')
  ⚠️ 第934行: 发现jQuery依赖: $('#noInspectionItemsMsg').show()
  ⚠️ 第935行: 发现jQuery依赖: $('#noNormalItemsMsg').show()
  ⚠️ 第946行: 发现jQuery依赖: $('#batch_production_date').val(todayStr)
  ⚠️ 第947行: 发现jQuery依赖: $('#batch_expiry_date').val(expiryDateStr)
  ⚠️ 第950行: 发现jQuery依赖: $('#default_storage_location').val()
  ⚠️ 第952行: 发现jQuery依赖: $('#batch_storage_location').val(defaultStorageLocation)
  ⚠️ 第958行: 发现jQuery依赖: $('#importFromPurchaseBtn').on('click', function()
  ⚠️ 第962行: 发现jQuery依赖: $('#purchaseOrderModal').modal('show')
  ⚠️ 第980行: 发现jQuery依赖: $('#purchaseOrdersTable')
  ⚠️ 第1073行: 发现jQuery依赖: $('#purchaseOrdersTable').html(html)
  ⚠️ 第1079行: 发现jQuery依赖: $('#purchaseOrdersTable')
  ⚠️ 第1105行: 发现jQuery依赖: $('#purchaseOrdersTable')
  ⚠️ 第1154行: 发现jQuery依赖: $('.import-purchase-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 导入中...')
  ⚠️ 第1163行: 发现jQuery依赖: $('.import-purchase-btn').prop('disabled', false).html('<i class="fas fa-file-import"></i> 导入')
  ⚠️ 第1167行: 发现jQuery依赖: $('#purchaseOrderModal').modal('hide')
  ⚠️ 第1180行: 发现jQuery依赖: $('.import-purchase-btn').prop('disabled', false).html('<i class="fas fa-file-import"></i> 导入')
  ⚠️ 第1192行: 发现jQuery依赖: $('#addIngredientBtn').on('click', function()
  ⚠️ 第1199行: 发现jQuery依赖: $('#applyBatchSettingsBtn').on('click', function()
  ⚠️ 第1200行: 发现jQuery依赖: $('#batch_storage_location').val()
  ⚠️ 第1201行: 发现jQuery依赖: $('#batch_production_date').val()
  ⚠️ 第1202行: 发现jQuery依赖: $('#batch_expiry_date').val()
  ⚠️ 第1203行: 发现jQuery依赖: $('#batch_supplier').val()
  ⚠️ 第1207行: 发现jQuery依赖: $('.item-storage-location').val(storageLocationId)
  ⚠️ 第1210行: 发现jQuery依赖: $('.item-production-date').val(productionDate)
  ⚠️ 第1213行: 发现jQuery依赖: $('.item-expiry-date').val(expiryDate)
  ⚠️ 第1216行: 发现jQuery依赖: $('.item-supplier').val(supplierId)
  ⚠️ 第1237行: 发现jQuery依赖: $('#inspectionItemsContainer .item-card')
  ⚠️ 第1238行: 发现jQuery依赖: $('#normalItemsContainer .item-card')
  ⚠️ 第1239行: 发现jQuery依赖: $('#noInspectionItemsMsg').toggle(inspectionItemsCount === 0)
  ⚠️ 第1240行: 发现jQuery依赖: $('#noNormalItemsMsg').toggle(normalItemsCount === 0)
  ⚠️ 第1257行: 发现jQuery依赖: $('#step2PrevBtn').on('click', function()
  ⚠️ 第1262行: 发现jQuery依赖: $('#step2NextBtn').on('click', function()
  ⚠️ 第1270行: 发现jQuery依赖: $('.item-card')
  ⚠️ 第1287行: 发现jQuery依赖: $('#noInspectionNeededMsg').show()
  ⚠️ 第1288行: 发现jQuery依赖: $('#inspectionItemsList').hide()
  ⚠️ 第1292行: 发现jQuery依赖: $('#step3PrevBtn').on('click', function()
  ⚠️ 第1297行: 发现jQuery依赖: $('#step3NextBtn').on('click', function()
  ⚠️ 第1304行: 发现jQuery依赖: $('#uploadDocumentBtn').on('click', function()
  ⚠️ 第1305行: 发现jQuery依赖: $('#document_type').val()
  ⚠️ 第1306行: 发现jQuery依赖: $('#document_file')
  ⚠️ 第1322行: 发现jQuery依赖: $('#document_notes').val('')
  ⚠️ 第1323行: 发现jQuery依赖: $('#document_file').val('')
  ⚠️ 第1324行: 发现jQuery依赖: $('.custom-file-label').text('选择文件')
  ⚠️ 第1334行: 发现jQuery依赖: $('#basicInfoSummary')
  ⚠️ 第1341行: 发现jQuery依赖: $('#stock_in_type').val()
  ⚠️ 第1345行: 发现jQuery依赖: $('#stock_in_date').val()
  ⚠️ 第1349行: 发现jQuery依赖: $('#default_storage_location option:selected').text()
  ⚠️ 第1357行: 发现jQuery依赖: $('#notes').val()
  ⚠️ 第1362行: 发现jQuery依赖: $('#itemsSummary').html('<tr><td colspan="9" class="text-center text-muted">暂无食材信息</td></tr>')
  ⚠️ 第1363行: 发现jQuery依赖: $('#inspectionSummarySection').hide()
  ⚠️ 第1364行: 发现jQuery依赖: $('#documentsSummary').html('<p class="text-muted">暂无上传的文档</p>')
  ⚠️ 第1369行: 发现jQuery依赖: $('#step4PrevBtn').on('click', function()
  ⚠️ 第1374行: 发现jQuery依赖: $('#submitStockInBtn').on('click', function()
  ❌ 第745行: Bootstrap 4 API调用: .modal('show'
  ❌ 第962行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\stock_in\wizard_simple.html:
  ⚠️ 第99行: 发现jQuery依赖: $('#testApiBtn').on('click', function()
  ⚠️ 第105行: 发现jQuery依赖: $('#showModalBtn').on('click', function()
  ⚠️ 第107行: 发现jQuery依赖: $('#purchaseOrderModal').modal('show')
  ⚠️ 第123行: 发现jQuery依赖: $('#testApiBtn').prop('disabled', true).text('测试中...')
  ⚠️ 第127行: 发现jQuery依赖: $('#testApiBtn').prop('disabled', false).text('测试API连接')
  ⚠️ 第144行: 发现jQuery依赖: $('#testApiBtn').prop('disabled', false).text('测试API连接')
  ⚠️ 第178行: 发现jQuery依赖: $('#purchaseOrdersTable').html(html)
  ⚠️ 第184行: 发现jQuery依赖: $('#purchaseOrderModal').modal('hide')
  ⚠️ 第189行: 发现jQuery依赖: $('#testResult')
  ❌ 第107行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\category_index.html:
  ⚠️ 第103行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第105行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第108行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第122行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第126行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第105行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\certificate_form.html:
  ⚠️ 第96行: 发现jQuery依赖: $('.datepicker')
  ⚠️ 第104行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第113行: 发现jQuery依赖: $('#supplier_id').val(supplierIdParam)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\certificate_index.html:
  ⚠️ 第216行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第218行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第221行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第235行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第239行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第218行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\certificate_view.html:
  ⚠️ 第165行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第167行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第170行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第184行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第188行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第197行: 发现jQuery依赖: $('#modalImage').attr('src', imageSrc)
  ⚠️ 第198行: 发现jQuery依赖: $('#downloadImage').attr('href', imageSrc)
  ⚠️ 第199行: 发现jQuery依赖: $('#imageModal').modal('show')
  ❌ 第167行: Bootstrap 4 API调用: .modal('show'
  ❌ 第199行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\form.html:
  ⚠️ 第259行: 发现jQuery依赖: $('.datepicker')
  ⚠️ 第282行: 发现jQuery依赖: $('#generateContractBtn').click(function()
  ⚠️ 第283行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  ⚠️ 第287行: 发现jQuery依赖: $('#contract_number').length && !$('#contract_number').val())
  ⚠️ 第288行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  ⚠️ 第292行: 发现jQuery依赖: $('#area_id').change(function()
  ⚠️ 第295行: 发现jQuery依赖: $('#contract_number').val()
  ⚠️ 第297行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\index.html:
  ⚠️ 第347行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第349行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第352行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第366行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第370行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ❌ 第349行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\product_form.html:
  ⚠️ 第249行: 发现jQuery依赖: $('.select2')
  ⚠️ 第255行: 发现jQuery依赖: $('.categorized-ingredient-select').categorizedIngredientSelect()
  ⚠️ 第258行: 发现jQuery依赖: $('.custom-file-input').on('change', function()
  ⚠️ 第267行: 发现jQuery依赖: $('#supplier_id').val(supplierIdParam).trigger('change')
  ⚠️ 第270行: 发现jQuery依赖: $('#product_code').val() && window.location.href.indexOf('/edit') === -1)
  ⚠️ 第272行: 发现jQuery依赖: $('#generateCodeBtn').click()
  ⚠️ 第278行: 发现jQuery依赖: $('#supplier_id').on('change', function()
  ⚠️ 第279行: 发现jQuery依赖: $('#product_code').val())
  ⚠️ 第285行: 发现jQuery依赖: $('#ingredient_id').on('change', function()
  ⚠️ 第289行: 发现jQuery依赖: $('#ingredientPreview').removeClass('d-none')
  ⚠️ 第290行: 发现jQuery依赖: $('#ingredientDetails').html('<p class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</p>')
  ⚠️ 第315行: 发现jQuery依赖: $('#ingredientDetails').html(html)
  ⚠️ 第318行: 发现jQuery依赖: $('#product_name').val())
  ⚠️ 第319行: 发现jQuery依赖: $('#product_name').val(data.name)
  ⚠️ 第322行: 发现jQuery依赖: $('#specification').val() && data.specification)
  ⚠️ 第323行: 发现jQuery依赖: $('#specification').val(data.specification)
  ⚠️ 第327行: 发现jQuery依赖: $('#ingredientDetails').html('<p class="text-danger"><i class="fas fa-exclamation-triangle"></i> 获取食材信息失败，请重试或手动填写信息。</p>')
  ⚠️ 第331行: 发现jQuery依赖: $('#ingredientPreview').addClass('d-none')
  ⚠️ 第336行: 发现jQuery依赖: $('#generateCodeBtn').on('click', function()
  ⚠️ 第337行: 发现jQuery依赖: $('#supplier_id').val()
  ⚠️ 第359行: 发现jQuery依赖: $('#product_code').val(data.product_code)
  ⚠️ 第377行: 发现jQuery依赖: $('#productForm').on('submit', function(e)
  ⚠️ 第381行: 发现jQuery依赖: $('#supplier_id').val() == '0')
  ⚠️ 第383行: 发现jQuery依赖: $('#supplier_id').addClass('is-invalid')
  ⚠️ 第384行: 发现jQuery依赖: $('#supplier_id').after('<div class="invalid-feedback">请选择供应商</div>')
  ⚠️ 第386行: 发现jQuery依赖: $('#supplier_id').removeClass('is-invalid')
  ⚠️ 第390行: 发现jQuery依赖: $('#ingredient_id').val() == '0')
  ⚠️ 第392行: 发现jQuery依赖: $('#ingredient_id').addClass('is-invalid')
  ⚠️ 第393行: 发现jQuery依赖: $('#ingredient_id').after('<div class="invalid-feedback">请选择食材</div>')
  ⚠️ 第395行: 发现jQuery依赖: $('#ingredient_id').removeClass('is-invalid')
  ⚠️ 第399行: 发现jQuery依赖: $('#price').val() || parseFloat($('#price').val()) <= 0)
  ⚠️ 第401行: 发现jQuery依赖: $('#price').addClass('is-invalid')
  ⚠️ 第402行: 发现jQuery依赖: $('#price').after('<div class="invalid-feedback">请输入有效的价格</div>')
  ⚠️ 第404行: 发现jQuery依赖: $('#price').removeClass('is-invalid')
  ⚠️ 第409行: 发现jQuery依赖: $('html, body')
  ⚠️ 第410行: 发现jQuery依赖: $('.is-invalid:first').offset()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\product_index.html:
  ⚠️ 第360行: 发现jQuery依赖: $('.product-checkbox:checked').each(function()
  ⚠️ 第369行: 发现jQuery依赖: $('#selectedCount').text(count)
  ⚠️ 第373行: 发现jQuery依赖: $('#batchApproveBtn, #batchRejectBtn, #batchShelfBtn, #batchUnshelfBtn, #batchDeleteBtn').prop('disabled', !hasSelection)
  ⚠️ 第382行: 发现jQuery依赖: $('#batchApproveBtn').prop('disabled', !canApprove)
  ⚠️ 第383行: 发现jQuery依赖: $('#batchRejectBtn').prop('disabled', !canReject)
  ⚠️ 第384行: 发现jQuery依赖: $('#batchShelfBtn').prop('disabled', !canShelf)
  ⚠️ 第385行: 发现jQuery依赖: $('#batchUnshelfBtn').prop('disabled', !canUnshelf)
  ⚠️ 第390行: 发现jQuery依赖: $('#selectAll').change(function()
  ⚠️ 第391行: 发现jQuery依赖: $('.product-checkbox').prop('checked', this.checked)
  ⚠️ 第395行: 发现jQuery依赖: $('#selectAllBtn').click(function()
  ⚠️ 第396行: 发现jQuery依赖: $('.product-checkbox').prop('checked', true)
  ⚠️ 第397行: 发现jQuery依赖: $('#selectAll').prop('checked', true)
  ⚠️ 第401行: 发现jQuery依赖: $('#clearSelectionBtn').click(function()
  ⚠️ 第402行: 发现jQuery依赖: $('.product-checkbox').prop('checked', false)
  ⚠️ 第403行: 发现jQuery依赖: $('#selectAll').prop('checked', false)
  ⚠️ 第412行: 发现jQuery依赖: $('.product-checkbox')
  ⚠️ 第413行: 发现jQuery依赖: $('.product-checkbox:checked')
  ⚠️ 第414行: 发现jQuery依赖: $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes)
  ⚠️ 第420行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第422行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第425行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第439行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第443行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第450行: 发现jQuery依赖: $('.shelf-btn').click(function()
  ⚠️ 第472行: 发现jQuery依赖: $('.unshelf-btn').click(function()
  ⚠️ 第494行: 发现jQuery依赖: $('.approve-btn').click(function()
  ⚠️ 第516行: 发现jQuery依赖: $('#batchApproveBtn').click(function()
  ⚠️ 第526行: 发现jQuery依赖: $('#batchOperationModalLabel').text('批量审核通过')
  ⚠️ 第527行: 发现jQuery依赖: $('#batchOperationContent')
  ⚠️ 第531行: 发现jQuery依赖: $('#batchRejectReasonGroup').hide()
  ⚠️ 第532行: 发现jQuery依赖: $('#confirmBatchOperation').removeClass('btn-danger').addClass('btn-success').text('确认审核通过')
  ⚠️ 第533行: 发现jQuery依赖: $('#batchOperationModal').modal('show')
  ⚠️ 第536行: 发现jQuery依赖: $('#batchRejectBtn').click(function()
  ⚠️ 第546行: 发现jQuery依赖: $('#batchOperationModalLabel').text('批量拒绝')
  ⚠️ 第547行: 发现jQuery依赖: $('#batchOperationContent')
  ⚠️ 第551行: 发现jQuery依赖: $('#batchRejectReasonGroup').show()
  ⚠️ 第552行: 发现jQuery依赖: $('#confirmBatchOperation').removeClass('btn-success').addClass('btn-danger').text('确认拒绝')
  ⚠️ 第553行: 发现jQuery依赖: $('#batchOperationModal').modal('show')
  ⚠️ 第556行: 发现jQuery依赖: $('#batchShelfBtn').click(function()
  ⚠️ 第566行: 发现jQuery依赖: $('#batchOperationModalLabel').text('批量上架')
  ⚠️ 第567行: 发现jQuery依赖: $('#batchOperationContent')
  ⚠️ 第571行: 发现jQuery依赖: $('#batchRejectReasonGroup').hide()
  ⚠️ 第572行: 发现jQuery依赖: $('#confirmBatchOperation').removeClass('btn-danger').addClass('btn-info').text('确认上架')
  ⚠️ 第573行: 发现jQuery依赖: $('#batchOperationModal').modal('show')
  ⚠️ 第576行: 发现jQuery依赖: $('#batchUnshelfBtn').click(function()
  ⚠️ 第586行: 发现jQuery依赖: $('#batchOperationModalLabel').text('批量下架')
  ⚠️ 第587行: 发现jQuery依赖: $('#batchOperationContent')
  ⚠️ 第591行: 发现jQuery依赖: $('#batchRejectReasonGroup').hide()
  ⚠️ 第592行: 发现jQuery依赖: $('#confirmBatchOperation').removeClass('btn-success').addClass('btn-warning').text('确认下架')
  ⚠️ 第593行: 发现jQuery依赖: $('#batchOperationModal').modal('show')
  ⚠️ 第596行: 发现jQuery依赖: $('#batchDeleteBtn').click(function()
  ⚠️ 第600行: 发现jQuery依赖: $('#batchOperationModalLabel').text('批量删除')
  ⚠️ 第601行: 发现jQuery依赖: $('#batchOperationContent')
  ⚠️ 第608行: 发现jQuery依赖: $('#batchRejectReasonGroup').hide()
  ⚠️ 第609行: 发现jQuery依赖: $('#confirmBatchOperation').removeClass('btn-success btn-info btn-warning').addClass('btn-danger').text('确认删除')
  ⚠️ 第610行: 发现jQuery依赖: $('#batchOperationModal').modal('show')
  ⚠️ 第616行: 发现jQuery依赖: $('.reject-btn').click(function()
  ⚠️ 第618行: 发现jQuery依赖: $('#rejectModal').modal('show')
  ⚠️ 第621行: 发现jQuery依赖: $('#confirmReject').click(function()
  ⚠️ 第623行: 发现jQuery依赖: $('#rejectReason').val()
  ⚠️ 第644行: 发现jQuery依赖: $('#rejectModal').modal('hide')
  ⚠️ 第648行: 发现jQuery依赖: $('#rejectModal').modal('hide')
  ⚠️ 第655行: 发现jQuery依赖: $('#confirmBatchOperation').click(function()
  ⚠️ 第663行: 发现jQuery依赖: $('#batchRejectReason').val()
  ⚠️ 第696行: 发现jQuery依赖: $('#batchOperationModal').modal('hide')
  ⚠️ 第700行: 发现jQuery依赖: $('#batchOperationModal').modal('hide')
  ⚠️ 第706行: 发现jQuery依赖: $('#batchOperationModal').on('hidden.bs.modal', function()
  ⚠️ 第708行: 发现jQuery依赖: $('#batchRejectReason').val('')
  ❌ 第422行: Bootstrap 4 API调用: .modal('show'
  ❌ 第533行: Bootstrap 4 API调用: .modal('show'
  ❌ 第553行: Bootstrap 4 API调用: .modal('show'
  ❌ 第573行: Bootstrap 4 API调用: .modal('show'
  ❌ 第593行: Bootstrap 4 API调用: .modal('show'
  ❌ 第610行: Bootstrap 4 API调用: .modal('show'
  ❌ 第618行: Bootstrap 4 API调用: .modal('show'
  ⚠️ 第706行: 过时的事件名: hidden.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\product_view.html:
  ⚠️ 第329行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第331行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第334行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第348行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第352行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第361行: 发现jQuery依赖: $('.delete-param-btn').click(function()
  ⚠️ 第363行: 发现jQuery依赖: $('#deleteParamModal').modal('show')
  ⚠️ 第366行: 发现jQuery依赖: $('#confirmDeleteParam').click(function()
  ⚠️ 第380行: 发现jQuery依赖: $('#deleteParamModal').modal('hide')
  ⚠️ 第384行: 发现jQuery依赖: $('#deleteParamModal').modal('hide')
  ⚠️ 第391行: 发现jQuery依赖: $('.shelf-btn').click(function()
  ⚠️ 第413行: 发现jQuery依赖: $('.unshelf-btn').click(function()
  ⚠️ 第435行: 发现jQuery依赖: $('.approve-btn').click(function()
  ⚠️ 第459行: 发现jQuery依赖: $('.reject-btn').click(function()
  ⚠️ 第461行: 发现jQuery依赖: $('#rejectModal').modal('show')
  ⚠️ 第464行: 发现jQuery依赖: $('#confirmReject').click(function()
  ⚠️ 第466行: 发现jQuery依赖: $('#rejectReason').val()
  ⚠️ 第487行: 发现jQuery依赖: $('#rejectModal').modal('hide')
  ⚠️ 第491行: 发现jQuery依赖: $('#rejectModal').modal('hide')
  ❌ 第331行: Bootstrap 4 API调用: .modal('show'
  ❌ 第363行: Bootstrap 4 API调用: .modal('show'
  ❌ 第461行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\school_form.html:
  ⚠️ 第113行: 发现jQuery依赖: $('.datepicker')
  ⚠️ 第124行: 发现jQuery依赖: $('#supplier_id').val(supplierIdParam)
  ⚠️ 第128行: 发现jQuery依赖: $('#area_id').val() == 0)
  ⚠️ 第130行: 发现jQuery依赖: $('#area_id option:eq(1)').val()
  ⚠️ 第132行: 发现jQuery依赖: $('#area_id').val(firstOption)
  ⚠️ 第134行: 发现jQuery依赖: $('#area_id').trigger('change')
  ⚠️ 第154行: 发现jQuery依赖: $('#contract_number').val())
  ⚠️ 第155行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  ⚠️ 第159行: 发现jQuery依赖: $('#generateContractBtn').click(function()
  ⚠️ 第160行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  ⚠️ 第164行: 发现jQuery依赖: $('#supplier_id, #area_id').change(function()
  ⚠️ 第166行: 发现jQuery依赖: $('#supplier_id').val()
  ⚠️ 第167行: 发现jQuery依赖: $('#area_id').val()
  ⚠️ 第170行: 发现jQuery依赖: $('#contract_number').val()
  ⚠️ 第172行: 发现jQuery依赖: $('#contract_number').val(generateContractNumber())
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\supplier\school_index.html:
  ⚠️ 第274行: 发现jQuery依赖: $('meta[name=csrf-token]').attr('content')
  ⚠️ 第288行: 发现jQuery依赖: $('.delete-btn').click(function()
  ⚠️ 第290行: 发现jQuery依赖: $('#deleteModal').modal('show')
  ⚠️ 第293行: 发现jQuery依赖: $('#confirmDelete').click(function()
  ⚠️ 第307行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第315行: 发现jQuery依赖: $('#deleteModal').modal('hide')
  ⚠️ 第324行: 发现jQuery依赖: $('.terminate-btn').click(function()
  ⚠️ 第326行: 发现jQuery依赖: $('#terminateModal').modal('show')
  ⚠️ 第329行: 发现jQuery依赖: $('#confirmTerminate').click(function()
  ⚠️ 第343行: 发现jQuery依赖: $('#terminateModal').modal('hide')
  ⚠️ 第351行: 发现jQuery依赖: $('#terminateModal').modal('hide')
  ⚠️ 第360行: 发现jQuery依赖: $('.activate-btn').click(function()
  ⚠️ 第362行: 发现jQuery依赖: $('#activateModal').modal('show')
  ⚠️ 第365行: 发现jQuery依赖: $('#confirmActivate').click(function()
  ⚠️ 第379行: 发现jQuery依赖: $('#activateModal').modal('hide')
  ⚠️ 第387行: 发现jQuery依赖: $('#activateModal').modal('hide')
  ❌ 第290行: Bootstrap 4 API调用: .modal('show'
  ❌ 第326行: Bootstrap 4 API调用: .modal('show'
  ❌ 第362行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\traceability\index.html:
  ⚠️ 第134行: 发现jQuery依赖: $('#search_type').change(function()
  ⚠️ 第146行: 发现jQuery依赖: $('.search-hint').text(hint)
  ⚠️ 第150行: 发现jQuery依赖: $('#ingredient_search_btn').click(function()
  ⚠️ 第151行: 发现jQuery依赖: $('#ingredient_select').val()
  ⚠️ 第160行: 发现jQuery依赖: $('#supplier_search_btn').click(function()
  ⚠️ 第161行: 发现jQuery依赖: $('#supplier_select').val()
  ⚠️ 第171行: 发现jQuery依赖: $('#area_search_btn').click(function()
  ⚠️ 第172行: 发现jQuery依赖: $('#area_select').val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\traceability\trace_interface.html:
  ⚠️ 第202行: 发现jQuery依赖: $('#traceForm').on('submit', function(e)
  ⚠️ 第206行: 发现jQuery依赖: $('#loading').show()
  ⚠️ 第207行: 发现jQuery依赖: $('#traceResult').hide()
  ⚠️ 第219行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第225行: 发现jQuery依赖: $('#traceResult').show()
  ⚠️ 第229行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第244行: 发现jQuery依赖: $('.trace-chain')
  ⚠️ 第247行: 发现jQuery依赖: $('#traceType').val()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\traceability\trace_interface_new.html:
  ⚠️ 第218行: 发现jQuery依赖: $('#nextStep1').on('click', function()
  ⚠️ 第219行: 发现jQuery依赖: $('#traceType').val()
  ⚠️ 第226行: 发现jQuery依赖: $('#traceType option:selected').text()
  ⚠️ 第227行: 发现jQuery依赖: $('#traceTypeText').text(traceTypeText)
  ⚠️ 第228行: 发现jQuery依赖: $('#idSelectorLabel').text('选择' + traceTypeText)
  ⚠️ 第234行: 发现jQuery依赖: $('.step-1').hide()
  ⚠️ 第235行: 发现jQuery依赖: $('.step-2').show()
  ⚠️ 第239行: 发现jQuery依赖: $('#prevStep2').on('click', function()
  ⚠️ 第240行: 发现jQuery依赖: $('.step-2').hide()
  ⚠️ 第241行: 发现jQuery依赖: $('.step-1').show()
  ⚠️ 第245行: 发现jQuery依赖: $('#nextStep2').on('click', function()
  ⚠️ 第246行: 发现jQuery依赖: $('#traceType').val()
  ⚠️ 第247行: 发现jQuery依赖: $('#idSelector').val()
  ⚠️ 第255行: 发现jQuery依赖: $('#loading').show()
  ⚠️ 第258行: 发现jQuery依赖: $('#traceType option:selected').text()
  ⚠️ 第259行: 发现jQuery依赖: $('#resultTypeText').text(resultTypeText)
  ⚠️ 第271行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第277行: 发现jQuery依赖: $('.step-2').hide()
  ⚠️ 第278行: 发现jQuery依赖: $('.step-3').show()
  ⚠️ 第282行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第297行: 发现jQuery依赖: $('#idSelector').empty().append('<option value="">请选择</option>')
  ⚠️ 第298行: 发现jQuery依赖: $('#loading').show()
  ⚠️ 第321行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第326行: 发现jQuery依赖: $('#idSelector')
  ⚠️ 第327行: 发现jQuery依赖: $('<option></option>').val(option.id).text(option.text)
  ⚠️ 第332行: 发现jQuery依赖: $('#idSelector')
  ⚠️ 第333行: 发现jQuery依赖: $('<option></option>').val('').text('没有可用数据')
  ⚠️ 第338行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第349行: 发现jQuery依赖: $('#idSelector')
  ⚠️ 第350行: 发现jQuery依赖: $('<option></option>').val('').text('加载失败，请重试')
  ⚠️ 第361行: 发现jQuery依赖: $('#traceChain')
  ⚠️ 第605行: 发现jQuery依赖: $('#backToSearch').on('click', function()
  ⚠️ 第606行: 发现jQuery依赖: $('.step-3').hide()
  ⚠️ 第607行: 发现jQuery依赖: $('.step-1').show()
  ⚠️ 第609行: 发现jQuery依赖: $('#idSelector').empty()
  ⚠️ 第611行: 发现jQuery依赖: $('#traceType').val('')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\traceability\trace_interface_simple.html:
  ⚠️ 第189行: 发现jQuery依赖: $('#traceType').on('change', function()
  ⚠️ 第193行: 发现jQuery依赖: $('#idSelector').empty().append('<option value="">请选择</option>').prop('disabled', true)
  ⚠️ 第196行: 发现jQuery依赖: $('#searchButton').prop('disabled', true)
  ⚠️ 第203行: 发现jQuery依赖: $('#traceType option:selected').text()
  ⚠️ 第204行: 发现jQuery依赖: $('#idSelectorLabel').text('选择' + traceTypeText)
  ⚠️ 第207行: 发现jQuery依赖: $('#loading').show()
  ⚠️ 第232行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第235行: 发现jQuery依赖: $('#idSelector').prop('disabled', false)
  ⚠️ 第240行: 发现jQuery依赖: $('#idSelector')
  ⚠️ 第241行: 发现jQuery依赖: $('<option></option>').val(option.id).text(option.text)
  ⚠️ 第246行: 发现jQuery依赖: $('#idSelector')
  ⚠️ 第247行: 发现jQuery依赖: $('<option></option>').val('').text('没有可用数据')
  ⚠️ 第253行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第264行: 发现jQuery依赖: $('#idSelector').prop('disabled', false)
  ⚠️ 第265行: 发现jQuery依赖: $('<option></option>').val('').text('加载失败，请重试')
  ⚠️ 第272行: 发现jQuery依赖: $('#idSelector').on('change', function()
  ⚠️ 第273行: 发现jQuery依赖: $('#searchButton').prop('disabled', !$(this).val())
  ⚠️ 第277行: 发现jQuery依赖: $('#searchButton').on('click', function()
  ⚠️ 第278行: 发现jQuery依赖: $('#traceType').val()
  ⚠️ 第279行: 发现jQuery依赖: $('#idSelector').val()
  ⚠️ 第287行: 发现jQuery依赖: $('#loading').show()
  ⚠️ 第288行: 发现jQuery依赖: $('#traceSearchForm').hide()
  ⚠️ 第291行: 发现jQuery依赖: $('#traceType option:selected').text()
  ⚠️ 第292行: 发现jQuery依赖: $('#resultTypeText').text(resultTypeText)
  ⚠️ 第304行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第310行: 发现jQuery依赖: $('#traceResult').show()
  ⚠️ 第314行: 发现jQuery依赖: $('#loading').hide()
  ⚠️ 第315行: 发现jQuery依赖: $('#traceSearchForm').show()
  ⚠️ 第329行: 发现jQuery依赖: $('#backToSearch').on('click', function()
  ⚠️ 第330行: 发现jQuery依赖: $('#traceResult').hide()
  ⚠️ 第331行: 发现jQuery依赖: $('#traceSearchForm').show()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\trace_document\upload.html:
  ⚠️ 第105行: 发现jQuery依赖: $('#document_file')
  ⚠️ 第106行: 发现jQuery依赖: $('.custom-file-label')
  ⚠️ 第121行: 发现jQuery依赖: $('.custom-file').off('click').on('click', function(e)
  ⚠️ 第150行: 发现jQuery依赖: $('form').submit(function(e)
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\warehouse\form_wtf.html:
  ⚠️ 第153行: 发现jQuery依赖: $('.form-control')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\warehouse_new\form.html:
  ⚠️ 第153行: 发现jQuery依赖: $('.form-control')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\warehouse_new\index.html:
  ⚠️ 第167行: 发现jQuery依赖: $('.form-control')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\weekly_menu\1.html:
  ⚠️ 第642行: 发现jQuery依赖: $('.go-to-purchase-plan').click(function()
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\weekly_menu\index_v2.html:
  ⚠️ 第323行: 发现jQuery依赖: $('.datepicker')
  💡 建议:
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

📄 app\templates\weekly_menu\plan.html:
  ❌ 第1690行: Bootstrap 4 API调用: .modal('show'
  💡 建议:
    - 更新为Bootstrap 5的API调用方式

📄 app\templates\weekly_menu\plan_v2.html:
  ⚠️ 第696行: 发现jQuery依赖: $('#schoolRecipesBtn, #systemRecipesBtn').on('click', function()
  ⚠️ 第698行: 发现jQuery依赖: $('#schoolRecipesBtn, #systemRecipesBtn').removeClass('active')
  ⚠️ 第724行: 发现jQuery依赖: $('#recipeCategories')
  ⚠️ 第738行: 发现jQuery依赖: $('.tab-content .row')
  ⚠️ 第782行: 发现jQuery依赖: $('#print-date').text(printDate)
  ⚠️ 第796行: 发现jQuery依赖: $('#menuModal').on('shown.bs.modal', function()
  ⚠️ 第798行: 发现jQuery依赖: $('#schoolRecipesBtn').click()
  ⚠️ 第803行: 发现jQuery依赖: $('.action-buttons .btn').on('click', function()
  ⚠️ 第811行: 发现jQuery依赖: $('<style>')
  ⚠️ 第796行: 过时的事件名: shown.bs.
  💡 建议:
    - 更新事件名为Bootstrap 5的格式
    - 将jQuery代码重写为原生JavaScript或使用Bootstrap 5的API

🔧 修复建议:
1. 更新bootstrap-zh-CN.js为Bootstrap 5兼容版本
2. 将jQuery代码重写为原生JavaScript
3. 更新data-*属性为data-bs-*格式
4. 更新Bootstrap API调用方式
5. 测试所有交互功能

======================================================================