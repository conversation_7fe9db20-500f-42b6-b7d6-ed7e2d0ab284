{"timestamp": "2025-06-15T17:42:50.400423", "project_root": "C:\\StudentsCMSSP", "files_processed": [{"file": "app\\templates\\base.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\base_landing.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\base_public.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\base_widget.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\base_with_resources.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\mobile-demo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\mobile_menu_test.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\mobile_test.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\test_csp.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\theme_demo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\theme_test.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\_formhelpers.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\_pagination.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\carousel_batch_upload.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\carousel_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\carousel_list.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\data_management.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\fix_purchase.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\permission_help.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\roles.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第192行的表单存在CSRF令牌错误: 多个csrf_token()", "line": 192, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\admin\\role_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\role_permissions.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第38行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 38, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\admin\\role_templates.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\users.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\admin\\user_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\user_permissions.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第67行的表单可能需要CSRF保护 (请手动确认)", "line": 67, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\admin\\video_management.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\view_role.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\view_user.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\guide_management\\dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\guide_management\\demo_data.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\guide_management\\scenarios.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\guide_management\\users.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\super_delete\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\system\\backups.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\admin\\system\\base.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\system\\dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\system\\module_visibility.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\system\\monitor.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\system\\settings.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\video_guide\\create.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\admin\\video_guide\\edit.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\area\\area_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\area\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\area\\view_area.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\auth\\login.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\auth\\register.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\batch_flow\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\components\\homepage_carousel.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\components\\home_components.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\components\\process_navigation.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\consultation\\detail.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第106行的表单存在CSRF令牌错误: 重复的CSRF令牌", "line": 106, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\consultation\\list.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\consumption_plan\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第234行的表单可能需要CSRF保护 (请手动确认)", "line": 234, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\create_from_weekly.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\consumption_plan\\new.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第51行的表单可能需要CSRF保护 (请手动确认)", "line": 51, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\select_weekly_menu.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\consumption_plan\\super_editor.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\add_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_companion_iframe.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第42行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 42, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_event.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_issue.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\check_photos.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\companions.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第168行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 168, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\companion_entry.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\companion_qrcode.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\edit_companion.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\daily_management\\edit_event.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection_new.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第45行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 45, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_issue.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_log.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\events.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\fixed_inspection_qrcode.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections_card_layout.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections_category_cards.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections_new.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections_simple_table.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspections_table.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_demo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_display.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_qrcode.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_table_html.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_table_widget.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_templates.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_widget.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\inspection_widget_demo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\issues.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\logs.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\optimized_dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\photo_upload.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\photo_upload_qrcode.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print_companion.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print_inspection_photos.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print_inspection_photo_detail.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print_log.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print_training.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_add_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第102行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 102, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\public_error.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_inspection_select_date.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_inspection_select_type.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_rate_inspection_photos.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_rate_photo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_success.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_upload_inspection_photo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\public_upload_photo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\rate_inspection_photos.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\scan_upload_entry.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\school_qrcode.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\simplified_dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\simplified_inspection.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第234行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 234, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第313行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 313, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第392行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 392, "severity": "warning", "action": "fix_existing"}], "total_changes": 3}, {"file": "app\\templates\\daily_management\\trainings.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第142行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 142, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_event.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\view_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第100行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 100, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_issue.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\view_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\components\\data_visualization.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\components\\enhanced_image_uploader.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\components\\image_uploader.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\components\\navigation.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\base_print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\companions_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\companion_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\daily_summary.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\event_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\inspection_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\issue_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_companions.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_events.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_inspections.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_issues.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_log.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\print_trainings.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\trainings_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\print\\training_report.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\daily_management\\widgets\\image_widget.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\data_repair\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\data_repair\\tools.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\employee\\daily_health_check.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\employee\\employee_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\health_certificates.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\employee\\health_certificate_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\health_check_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\employee\\medical_examination_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\training_record_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\view_employee.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\errors\\404.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\errors\\500.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\examples\\categorized_ingredient_select.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\base.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\style_demo.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\accounting_subjects\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\accounting_subjects\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\accounting_subjects\\text_tree.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\ledgers\\balance.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\ledgers\\detail.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\ledgers\\general.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\payables\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\payables\\pending_stock_ins.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\payments\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\payments\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\balance_sheet.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\cost_analysis.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\income_statement.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\payables_aging.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\trial_balance.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\reports\\voucher_summary.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\create.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\edit.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\edit_professional.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\pending_stock_ins.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\text_view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\financial\\vouchers\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_sample\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第26行的表单可能需要CSRF保护 (请手动确认)", "line": 26, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\food_sample\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\food_sample\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_sample\\print_daily.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_sample\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\food_trace\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\print_samples.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\qr_diagnosis.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\qr_result.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\qr_scan.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\qr_test.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\recipe_trace_result.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\sample_management.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\food_trace\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\guide\\scenario_selection.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\guide\\step_modal.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\accounting_subjects.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\daily.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\financial.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\supply.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\system.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\help\\troubleshooting.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\includes\\form_helpers.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\includes\\modal.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\includes\\pagination.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\ingredient\\categories.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\ingredient\\category_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}], "total_changes": 0}, {"file": "app\\templates\\ingredient\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}], "total_changes": 0}, {"file": "app\\templates\\ingredient\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\ingredient\\index_category.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\ingredient\\turnover.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\ingredient\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inspection\\direct_index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inspection\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\inspection\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inspection\\simplified_index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\inspection\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\detail.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\expiry.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\ingredient.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\item_label_print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\print_inventory.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\statistics.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory\\summary.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory_alert\\batch_create_requisition.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\inventory_alert\\check.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\inventory_alert\\create_requisition.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第77行的表单可能需要CSRF保护 (请手动确认)", "line": 77, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\inventory_alert\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第150行的表单可能需要CSRF保护 (请手动确认)", "line": 150, "severity": "warning", "action": "manual_review"}], "total_changes": 2}, {"file": "app\\templates\\inventory_alert\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第123行的表单可能需要CSRF保护 (请手动确认)", "line": 123, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\macros\\progress_steps.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\macros\\purchase_order_status.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\canteen_dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\canteen_dashboard_new.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\check-resources.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\food_samples.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\help.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\ingredients.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\purchase_orders.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\recipes.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\main\\suppliers.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\material_batch\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\material_batch\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\material_batch\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\menu_sync\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\notification\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\notification\\send.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\notification\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\product_batch\\adjust_products.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第91行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 91, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\approve.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第93行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 93, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\confirm.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第72行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 72, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第48行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 48, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\product_batch\\select_ingredients.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\set_attributes.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\purchase_order\\create_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第262行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 262, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\purchase_order\\create_from_menu.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\purchase_order\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\purchase_order\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\purchase_order\\select_area.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\purchase_order\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\categories.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\category_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}], "total_changes": 0}, {"file": "app\\templates\\recipe\\create.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第95行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 95, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\recipe\\favorites.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\form_new.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\form_simplified.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\recipe\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\school_admin\\users.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\school_admin\\user_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\security\\dashboard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\batch_editor.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_simplified.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_simplified_scripts.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\batch_editor_step1.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_step2.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\by_ingredient.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\confirm.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\create_from_purchase.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\create_from_purchase_order.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 4, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 4}, {"file": "app\\templates\\stock_in\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第384行的表单可能需要CSRF保护 (请手动确认)", "line": 384, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\stock_in\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\labels_print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\redirect.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\trace.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\stock_in\\wizard.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in\\wizard_simple.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_in_detail\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_out\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 5, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 5}, {"file": "app\\templates\\stock_out\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\stock_out\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_out\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\stock_out\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 4, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 4}, {"file": "app\\templates\\stock_out_item\\detail.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\storage_location\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\storage_location\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\supplier\\category_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\category_index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\certificate_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\certificate_index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\certificate_view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\product_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\product_index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\product_view.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第179行的表单存在CSRF令牌错误: 重复的CSRF令牌", "line": 179, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\supplier\\school_form.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\school_index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\supplier\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\system_fix\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\system_fix\\permission_audit.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\system_fix\\permission_migration.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\traceability\\batch_trace.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\traceability\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\traceability\\ingredient_trace.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\traceability\\trace_interface.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\traceability\\trace_interface_new.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\traceability\\trace_interface_simple.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\trace_document\\upload.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\warehouse\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\warehouse\\form_wtf.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\warehouse\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\warehouse\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\warehouse_new\\form.html", "backup_created": false, "fixes_applied": [], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}], "total_changes": 0}, {"file": "app\\templates\\warehouse_new\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\warehouse_new\\view.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\1.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\copy_v2.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\index_v2.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\plan.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\weekly_menu\\plan_improved.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\plan_time_aware.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\plan_v2.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\print_v2.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\select_area.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\weekly_menu\\view_v2.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\index.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\plan_improved.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\print.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\select_area.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\select_menu.html", "backup_created": false, "fixes_applied": [], "issues_found": [], "total_changes": 0}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}], "fixes_applied": [{"file": "app\\templates\\admin\\role_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\users.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\admin\\user_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\user_permissions.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第67行的表单可能需要CSRF保护 (请手动确认)", "line": 67, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\admin\\view_user.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\admin\\system\\backups.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\admin\\system\\settings.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\area\\area_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\auth\\login.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\auth\\register.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\batch_flow\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第234行的表单可能需要CSRF保护 (请手动确认)", "line": 234, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\create_from_weekly.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\new.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第51行的表单可能需要CSRF保护 (请手动确认)", "line": 51, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\consumption_plan\\super_editor.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_companion_iframe.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第42行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 42, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_event.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_issue.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\add_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\companions.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第168行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 168, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_event.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection_new.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第45行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 45, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_issue.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_log.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\edit_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\events.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\issues.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\public_add_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第102行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 102, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\simplified_inspection.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第234行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 234, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第313行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 313, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第392行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 392, "severity": "warning", "action": "fix_existing"}], "total_changes": 3}, {"file": "app\\templates\\daily_management\\trainings.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_companion.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第142行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 142, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_inspection_photo.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第100行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 100, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\daily_management\\view_training.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\employee_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\health_certificate_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\health_check_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\medical_examination_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\employee\\training_record_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\food_sample\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第26行的表单可能需要CSRF保护 (请手动确认)", "line": 26, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\food_sample\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\food_sample\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\inspection\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\inspection\\simplified_index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\inventory_alert\\batch_create_requisition.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\inventory_alert\\create_requisition.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第77行的表单可能需要CSRF保护 (请手动确认)", "line": 77, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\inventory_alert\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第150行的表单可能需要CSRF保护 (请手动确认)", "line": 150, "severity": "warning", "action": "manual_review"}], "total_changes": 2}, {"file": "app\\templates\\inventory_alert\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第123行的表单可能需要CSRF保护 (请手动确认)", "line": 123, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\material_batch\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\menu_sync\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\product_batch\\adjust_products.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第91行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 91, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\approve.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第93行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 93, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\confirm.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第72行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 72, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\create.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第48行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 48, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\select_ingredients.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\product_batch\\set_attributes.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\purchase_order\\create_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第262行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 262, "severity": "warning", "action": "fix_existing"}], "total_changes": 1}, {"file": "app\\templates\\school_admin\\user_form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_simplified.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_step1.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\batch_editor_step2.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\confirm.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\create_from_purchase.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\create_from_purchase_order.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\stock_in\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 4, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 4}, {"file": "app\\templates\\stock_in\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第384行的表单可能需要CSRF保护 (请手动确认)", "line": 384, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\stock_in\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\stock_out\\edit.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 5, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 5}, {"file": "app\\templates\\stock_out\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\stock_out\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 4, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 4}, {"file": "app\\templates\\storage_location\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\storage_location\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\traceability\\index.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\trace_document\\upload.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\warehouse\\form.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [{"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}], "total_changes": 1}, {"file": "app\\templates\\warehouse\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\1.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\plan.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 3, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 3}, {"file": "app\\templates\\weekly_menu\\plan_improved.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\plan_time_aware.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\plan_improved.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 1, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 1}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\view.html", "backup_created": true, "fixes_applied": [{"type": "form_validation", "name": "添加novalidate属性", "count": 2, "description": "为POST表单添加novalidate属性"}], "issues_found": [], "total_changes": 2}], "issues_found": [{"type": "csrf_error", "name": "CSRF令牌错误", "message": "第192行的表单存在CSRF令牌错误: 多个csrf_token()", "line": 192, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第38行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 38, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第67行的表单可能需要CSRF保护 (请手动确认)", "line": 67, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第106行的表单存在CSRF令牌错误: 重复的CSRF令牌", "line": 106, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第234行的表单可能需要CSRF保护 (请手动确认)", "line": 234, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第51行的表单可能需要CSRF保护 (请手动确认)", "line": 51, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第42行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 42, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第37行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 37, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第168行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 168, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第45行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 45, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第102行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 102, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第234行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 234, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第313行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 313, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第392行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 392, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第142行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 142, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第100行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 100, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第26行的表单可能需要CSRF保护 (请手动确认)", "line": 26, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第77行的表单可能需要CSRF保护 (请手动确认)", "line": 77, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第150行的表单可能需要CSRF保护 (请手动确认)", "line": 150, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第123行的表单可能需要CSRF保护 (请手动确认)", "line": 123, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第91行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 91, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第93行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 93, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第72行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 72, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第48行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 48, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第44行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 44, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第262行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 262, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第16行的表单可能需要CSRF保护 (请手动确认)", "line": 16, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第95行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 95, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第384行的表单可能需要CSRF保护 (请手动确认)", "line": 384, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第179行的表单存在CSRF令牌错误: 重复的CSRF令牌", "line": 179, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_missing", "name": "缺少CSRF保护", "message": "第19行的表单可能需要CSRF保护 (请手动确认)", "line": 19, "severity": "warning", "action": "manual_review"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}, {"type": "csrf_error", "name": "CSRF令牌错误", "message": "第14行的表单存在CSRF令牌错误: csrf_token语法错误", "line": 14, "severity": "warning", "action": "fix_existing"}], "statistics": {"total_files": 362, "files_modified": 88, "total_fixes": 111, "csrf_fixes": 0, "bootstrap_fixes": 0, "html_fixes": 0, "form_fixes": 111, "security_issues": 41}}