================================================================================
🔧 模板修复专业工具报告
================================================================================
修复时间: 2025-06-15T17:39:18
项目路径: C:\StudentsCMSSP

📊 修复统计:
  • 处理文件: 362 个
  • 修改文件: 158 个
  • 总修复数: 432 处

🔍 修复分类:
  • CSRF令牌修复: 19 处
  • Bootstrap升级: 256 处
  • HTML结构优化: 46 处
  • 表单验证优化: 111 处
  • 安全问题发现: 65 个

✅ 修复的文件:

📄 app\templates\_formhelpers.html:
  • 修复空class属性: 2 处 - 移除空的class属性

📄 app\templates\admin\data_management.html:
  • custom-control-input到form-check-input: 3 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 3 处 - Bootstrap 5: custom-control-label → form-check-label

📄 app\templates\admin\permission_help.html:
  • btn-block到w-100: 5 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\admin\role_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\role_permissions.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label
  • 简化input-group结构: 2 处 - 移除input-group中的空div

📄 app\templates\admin\role_templates.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\admin\users.html:
  • custom-control到form-check: 4 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 3 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 6 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 3 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\user_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\user_permissions.html:
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\view_user.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\guide_management\dashboard.html:
  • btn-block到w-100: 6 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\admin\guide_management\users.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\admin\super_delete\index.html:
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label

📄 app\templates\admin\system\backups.html:
  • 添加novalidate属性: 2 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\system\dashboard.html:
  • btn-block到w-100: 9 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\admin\system\module_visibility.html:
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label

📄 app\templates\admin\system\settings.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\admin\video_guide\create.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\admin\video_guide\edit.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\area\area_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\auth\login.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\auth\register.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100
  • 简化input-group结构: 5 处 - 移除input-group中的空div
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\batch_flow\form.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\consultation\detail.html:
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\consumption_plan\create.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\consumption_plan\create_from_weekly.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\consumption_plan\edit.html:
  • custom-control-input到form-check-input: 4 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 4 处 - Bootstrap 5: custom-control-label → form-check-label
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\consumption_plan\new.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\consumption_plan\super_editor.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-label到form-check-label: 4 处 - Bootstrap 5: custom-control-label → form-check-label
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_companion.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_companion_iframe.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_event.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_inspection_photo.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_issue.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\add_training.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\companions.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_event.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_inspection.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_inspection_new.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_inspection_photo.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_issue.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_log.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\edit_training.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\events.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\index.html:
  • btn-block到w-100: 6 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections_card_layout.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections_category_cards.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections_new.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections_simple_table.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspections_table.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\inspection_form.html:
  • custom-control-input到form-check-input: 2 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label

📄 app\templates\daily_management\inspection_templates.html:
  • custom-control到form-check: 2 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 2 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 4 处 - Bootstrap 5: custom-control-label → form-check-label

📄 app\templates\daily_management\issues.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\photo_upload.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\public_add_companion.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\public_rate_photo.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\public_upload_inspection_photo.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\public_upload_photo.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\daily_management\simplified_inspection.html:
  • 添加novalidate属性: 3 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\trainings.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\view_companion.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\view_inspection_photo.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\view_training.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\daily_management\print\print_log.html:
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\data_repair\tools.html:
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\employee\employee_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\employee\health_certificate_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\employee\health_check_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\employee\medical_examination_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\employee\training_record_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\financial\accounting_subjects\form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\financial\payments\form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\financial\vouchers\edit_professional.html:
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\financial\vouchers\form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\food_sample\create.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\food_sample\index.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\food_sample\view.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\food_trace\qr_scan.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\food_trace\qr_test.html:
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\food_trace\sample_management.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100
  • 修复空class属性: 3 处 - 移除空的class属性

📄 app\templates\includes\form_helpers.html:
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\inspection\edit.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\inspection\simplified_index.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\inventory\expiry.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\inventory\index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\inventory\statistics.html:
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\inventory\summary.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\inventory_alert\batch_create_requisition.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\inventory_alert\check.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\inventory_alert\create_requisition.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\inventory_alert\index.html:
  • 添加novalidate属性: 2 处 - 为POST表单添加novalidate属性

📄 app\templates\inventory_alert\view.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\main\canteen_dashboard.html:
  • btn-block到w-100: 8 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\main\canteen_dashboard_new.html:
  • btn-block到w-100: 9 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\main\help.html:
  • btn-block到w-100: 8 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\main\index.html:
  • btn-block到w-100: 8 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\material_batch\form.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\menu_sync\index.html:
  • 添加novalidate属性: 3 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\adjust_products.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\approve.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\confirm.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\create.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\index.html:
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\product_batch\select_ingredients.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\product_batch\set_attributes.html:
  • 修复空class属性: 2 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\purchase_order\create_form.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\purchase_order\index.html:
  • btn-block到w-100: 4 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\purchase_order\view.html:
  • btn-block到w-100: 9 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\school_admin\user_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\security\dashboard.html:
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\stock_in\batch_editor.html:
  • custom-control到form-check: 2 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label
  • 修复空class属性: 2 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\batch_editor_simplified.html:
  • custom-control到form-check: 2 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100
  • 修复空class属性: 3 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\batch_editor_step1.html:
  • custom-control到form-check: 2 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label
  • 修复空class属性: 2 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\batch_editor_step2.html:
  • custom-control到form-check: 2 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 1 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 2 处 - Bootstrap 5: custom-control-label → form-check-label
  • 修复空class属性: 3 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\confirm.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\create_from_purchase.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\create_from_purchase_order.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\edit.html:
  • custom-control到form-check: 3 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 3 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 3 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 4 处 - Bootstrap 5: btn-block → w-100
  • 修复空class属性: 3 处 - 移除空的class属性
  • 添加novalidate属性: 4 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\form.html:
  • custom-control到form-check: 6 处 - Bootstrap 5: custom-control → form-check
  • custom-control-input到form-check-input: 6 处 - Bootstrap 5: custom-control-input → form-check-input
  • custom-control-label到form-check-label: 6 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\view.html:
  • custom-control到form-check: 1 处 - Bootstrap 5: custom-control → form-check
  • custom-control-label到form-check-label: 1 处 - Bootstrap 5: custom-control-label → form-check-label
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 2 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_in\wizard_simple.html:
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\stock_out\edit.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100
  • 添加novalidate属性: 5 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_out\form.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\stock_out\index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\stock_out\view.html:
  • 添加novalidate属性: 4 处 - 为POST表单添加novalidate属性

📄 app\templates\storage_location\form.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\storage_location\view.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\supplier\category_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\supplier\certificate_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()

📄 app\templates\supplier\certificate_index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\supplier\form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\supplier\index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\supplier\product_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 修复空class属性: 3 处 - 移除空的class属性

📄 app\templates\supplier\product_index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\supplier\product_view.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\supplier\school_form.html:
  • 重复CSRF令牌: 1 处 - 移除重复的csrf_token()，保留form.hidden_tag()
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\supplier\school_index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\system_fix\permission_audit.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\system_fix\permission_migration.html:
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\traceability\index.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\traceability\trace_interface.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\traceability\trace_interface_new.html:
  • btn-block到w-100: 3 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\traceability\trace_interface_simple.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\trace_document\upload.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\warehouse\form.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\warehouse\index.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\warehouse\view.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\warehouse_new\index.html:
  • btn-block到w-100: 2 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\weekly_menu\1.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\index_v2.html:
  • btn-block到w-100: 1 处 - Bootstrap 5: btn-block → w-100

📄 app\templates\weekly_menu\plan.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 3 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\plan_improved.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\plan_time_aware.html:
  • 修复空class属性: 1 处 - 移除空的class属性
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\plan_v2.html:
  • 修复空class属性: 1 处 - 移除空的class属性

📄 app\templates\weekly_menu\view.html:
  • 添加novalidate属性: 2 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\weekly_menu(new)\plan_improved.html:
  • 添加novalidate属性: 1 处 - 为POST表单添加novalidate属性

📄 app\templates\weekly_menu\weekly_menu(new)\view.html:
  • 添加novalidate属性: 2 处 - 为POST表单添加novalidate属性

⚠️ 发现的问题:
  ⚠️ 第296行: 检测缺少CSRF保护的POST表单
  ⚠️ 第323行: 检测缺少CSRF保护的POST表单
  ⚠️ 第374行: 检测缺少CSRF保护的POST表单
  ⚠️ 第67行: 检测缺少CSRF保护的POST表单
  ⚠️ 第455行: 检测缺少CSRF保护的POST表单
  ⚠️ 第42行: 检测缺少CSRF保护的POST表单
  ⚠️ 第117行: 检测缺少CSRF保护的POST表单
  ⚠️ 第62行: 检测缺少CSRF保护的POST表单
  ⚠️ 第59行: 检测缺少CSRF保护的POST表单
  ⚠️ 第234行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第51行: 检测缺少CSRF保护的POST表单
  ⚠️ 第24行: 检测缺少CSRF保护的POST表单
  ⚠️ 第24行: 检测缺少CSRF保护的POST表单
  ⚠️ 第131行: 检测缺少CSRF保护的POST表单
  ⚠️ 第47行: 检测缺少CSRF保护的POST表单
  ⚠️ 第71行: 检测缺少CSRF保护的POST表单
  ⚠️ 第116行: 检测缺少CSRF保护的POST表单
  ⚠️ 第47行: 检测缺少CSRF保护的POST表单
  ⚠️ 第110行: 检测缺少CSRF保护的POST表单
  ⚠️ 第130行: 检测缺少CSRF保护的POST表单
  ⚠️ 第167行: 检测缺少CSRF保护的POST表单
  ⚠️ 第26行: 检测缺少CSRF保护的POST表单
  ⚠️ 第164行: 检测缺少CSRF保护的POST表单
  ⚠️ 第158行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第77行: 检测缺少CSRF保护的POST表单
  ⚠️ 第69行: 检测缺少CSRF保护的POST表单
  ⚠️ 第150行: 检测缺少CSRF保护的POST表单
  ⚠️ 第123行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第125行: 检测缺少CSRF保护的POST表单
  ⚠️ 第470行: 检测缺少CSRF保护的POST表单
  ⚠️ 第192行: 检测缺少CSRF保护的POST表单
  ⚠️ 第249行: 检测缺少CSRF保护的POST表单
  ⚠️ 第468行: 检测缺少CSRF保护的POST表单
  ⚠️ 第534行: 检测缺少CSRF保护的POST表单
  ⚠️ 第384行: 检测缺少CSRF保护的POST表单
  ⚠️ 第667行: 检测缺少CSRF保护的POST表单
  ⚠️ 第689行: 检测缺少CSRF保护的POST表单
  ⚠️ 第160行: 检测缺少CSRF保护的POST表单
  ⚠️ 第182行: 检测缺少CSRF保护的POST表单
  ⚠️ 第187行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第782行: 检测缺少CSRF保护的POST表单
  ⚠️ 第787行: 检测缺少CSRF保护的POST表单
  ⚠️ 第793行: 检测缺少CSRF保护的POST表单
  ⚠️ 第799行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第161行: 检测缺少CSRF保护的POST表单
  ⚠️ 第21行: 检测缺少CSRF保护的POST表单
  ⚠️ 第45行: 检测缺少CSRF保护的POST表单
  ⚠️ 第19行: 检测缺少CSRF保护的POST表单
  ⚠️ 第148行: 检测缺少CSRF保护的POST表单
  ⚠️ 第415行: 检测缺少CSRF保护的POST表单
  ⚠️ 第403行: 检测缺少CSRF保护的POST表单
  ⚠️ 第461行: 检测缺少CSRF保护的POST表单
  ⚠️ 第490行: 检测缺少CSRF保护的POST表单
  ⚠️ 第50行: 检测缺少CSRF保护的POST表单
  ⚠️ 第245行: 检测缺少CSRF保护的POST表单
  ⚠️ 第135行: 检测缺少CSRF保护的POST表单
  ⚠️ 第160行: 检测缺少CSRF保护的POST表单
  ⚠️ 第51行: 检测缺少CSRF保护的POST表单
  ⚠️ 第183行: 检测缺少CSRF保护的POST表单
  ⚠️ 第214行: 检测缺少CSRF保护的POST表单

🎉 修复完成!
建议:
1. 测试所有修复的页面功能
2. 检查Bootstrap 5的新特性
3. 验证表单提交和CSRF保护
4. 检查响应式布局

================================================================================