#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap升级测试工具
==================

基于您的建议，提供系统性的功能测试和视觉回归测试

测试范围:
1. 功能测试 - 导航栏、模态框、表单等组件
2. 页面级测试 - 关键页面的布局和功能
3. 浏览器兼容性测试
4. 响应式布局测试
"""

import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging


class BootstrapUpgradeTester:
    """Bootstrap升级测试器"""
    
    def __init__(self, project_root: str = ".", base_url: str = "http://localhost:8080"):
        self.project_root = Path(project_root).resolve()
        self.base_url = base_url.rstrip('/')
        
        # 测试配置
        self.test_config = {
            "timeout": 10,
            "screenshot_dir": self.project_root / "test_screenshots",
            "test_data_file": self.project_root / "test_results.json"
        }
        
        # 功能测试清单
        self.component_tests = {
            "navbar": {
                "name": "导航栏",
                "tests": [
                    "下拉菜单展开/收起",
                    "移动端折叠菜单",
                    "主题切换功能",
                    "用户菜单功能"
                ],
                "selectors": {
                    "dropdown_toggle": ".navbar-nav .dropdown-toggle",
                    "mobile_toggle": ".navbar-toggler",
                    "theme_dropdown": "#themeDropdown",
                    "user_dropdown": "#userDropdown"
                }
            },
            "modal": {
                "name": "模态框",
                "tests": [
                    "模态框打开",
                    "模态框关闭",
                    "背景点击关闭",
                    "ESC键关闭"
                ],
                "selectors": {
                    "modal_trigger": "[data-bs-toggle='modal']",
                    "modal": ".modal",
                    "close_button": ".btn-close"
                }
            },
            "forms": {
                "name": "表单",
                "tests": [
                    "表单样式正确",
                    "验证状态显示",
                    "错误提示样式",
                    "成功提示样式"
                ],
                "selectors": {
                    "form_group": ".mb-3",
                    "form_control": ".form-control",
                    "invalid_feedback": ".invalid-feedback",
                    "valid_feedback": ".valid-feedback"
                }
            },
            "buttons": {
                "name": "按钮",
                "tests": [
                    "主要按钮样式",
                    "次要按钮样式",
                    "按钮组功能",
                    "禁用状态"
                ],
                "selectors": {
                    "btn_primary": ".btn-primary",
                    "btn_secondary": ".btn-secondary",
                    "btn_group": ".btn-group",
                    "btn_disabled": ".btn:disabled"
                }
            },
            "tables": {
                "name": "数据表格",
                "tests": [
                    "表格样式正确",
                    "DataTables功能",
                    "排序功能",
                    "分页功能"
                ],
                "selectors": {
                    "table": ".table",
                    "datatable": ".dataTable",
                    "sort_header": ".sorting",
                    "pagination": ".pagination"
                }
            },
            "cards": {
                "name": "卡片",
                "tests": [
                    "卡片布局正确",
                    "响应式网格",
                    "卡片组功能",
                    "卡片头部/底部"
                ],
                "selectors": {
                    "card": ".card",
                    "card_header": ".card-header",
                    "card_body": ".card-body",
                    "card_footer": ".card-footer"
                }
            }
        }
        
        # 页面测试清单
        self.page_tests = {
            "home": {
                "name": "首页",
                "url": "/",
                "tests": [
                    "页面加载正常",
                    "主要布局正确",
                    "响应式设计",
                    "主题切换"
                ]
            },
            "login": {
                "name": "登录页面",
                "url": "/auth/login",
                "tests": [
                    "表单样式正确",
                    "验证功能",
                    "错误提示",
                    "移动端适配"
                ]
            },
            "dashboard": {
                "name": "仪表板",
                "url": "/main/",
                "tests": [
                    "卡片布局",
                    "图表显示",
                    "数据表格",
                    "导航功能"
                ],
                "requires_auth": True
            },
            "financial": {
                "name": "财务模块",
                "url": "/financial/",
                "tests": [
                    "用友样式主题",
                    "表格功能",
                    "表单组件",
                    "专业布局"
                ],
                "requires_auth": True
            }
        }
        
        # 响应式断点测试
        self.responsive_breakpoints = {
            "mobile": {"width": 375, "height": 667, "name": "移动端"},
            "tablet": {"width": 768, "height": 1024, "name": "平板"},
            "desktop": {"width": 1200, "height": 800, "name": "桌面端"},
            "large": {"width": 1920, "height": 1080, "name": "大屏幕"}
        }
        
        # 测试结果
        self.test_results = {
            "test_info": {
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url,
                "bootstrap_version": "5.3.6"
            },
            "component_tests": {},
            "page_tests": {},
            "responsive_tests": {},
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "warnings": []
            }
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def check_and_install_dependencies(self):
        """检查并提示安装必要的依赖"""
        missing_deps = []

        # 检查Selenium
        try:
            import selenium
            selenium_version = selenium.__version__
            self.logger.info(f"✅ Selenium版本: {selenium_version}")

            # 检查是否为推荐版本（4.6+）
            major, minor = map(int, selenium_version.split('.')[:2])
            if major < 4 or (major == 4 and minor < 6):
                self.logger.warning(f"⚠️ 建议升级Selenium到4.6+以使用Selenium Manager")
                self.logger.info("💡 升级命令: pip install --upgrade selenium")
        except ImportError:
            missing_deps.append("selenium")

        # 检查webdriver-manager（可选但推荐）
        try:
            import webdriver_manager
            self.logger.info(f"✅ webdriver-manager已安装")
        except ImportError:
            self.logger.info("💡 建议安装webdriver-manager作为备选方案")
            self.logger.info("💡 安装命令: pip install webdriver-manager")

        if missing_deps:
            self.logger.error(f"❌ 缺少必要依赖: {', '.join(missing_deps)}")
            self.logger.error("💡 安装命令:")
            for dep in missing_deps:
                self.logger.error(f"  pip install {dep}")
            return False

        return True

    def print_driver_installation_guide(self):
        """打印ChromeDriver安装指南"""
        print("\n" + "="*60)
        print("🔧 ChromeDriver安装指南")
        print("="*60)
        print("\n📋 推荐解决方案（按优先级排序）:")
        print("\n1️⃣ 升级Selenium到最新版本（推荐）")
        print("   pip install --upgrade selenium")
        print("   # Selenium 4.6+自带Selenium Manager，自动管理驱动")

        print("\n2️⃣ 安装webdriver-manager库")
        print("   pip install webdriver-manager")
        print("   # 自动下载和管理ChromeDriver")

        print("\n3️⃣ 手动下载ChromeDriver")
        print("   • 访问: https://chromedriver.chromium.org/downloads")
        print("   • 下载与Chrome版本匹配的驱动")
        print("   • 解压到系统PATH目录")

        print("\n4️⃣ 添加到PATH环境变量")
        print("   Windows:")
        print("     setx PATH \"%PATH%;C:\\path\\to\\chromedriver\"")
        print("   Linux/Mac:")
        print("     export PATH=$PATH:/path/to/chromedriver")

        print("\n💡 验证安装:")
        print("   chromedriver --version")

        print("\n🔗 参考文档:")
        print("   https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location/")
        print("="*60)
    
    def check_selenium_available(self) -> bool:
        """检查Selenium是否可用"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            return True
        except ImportError:
            self.logger.warning("⚠️ 未安装Selenium，将跳过自动化测试")
            self.logger.info("💡 安装命令: pip install selenium")
            return False
    
    def create_webdriver(self):
        """创建WebDriver实例"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # 配置Chrome选项
            options = Options()
            options.add_argument('--headless')  # 无头模式
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--window-size=1920,1080')

            # 根据Selenium文档建议，优先使用Selenium Manager（Selenium 4.6+）
            # 如果失败，则尝试其他方法
            driver = None

            try:
                # 方法1: 使用Selenium Manager（推荐，Selenium 4.6+自动管理驱动）
                self.logger.info("🔄 尝试使用Selenium Manager自动管理ChromeDriver...")
                driver = webdriver.Chrome(options=options)
                self.logger.info("✅ 成功使用Selenium Manager创建WebDriver")

            except Exception as e1:
                self.logger.warning(f"⚠️ Selenium Manager失败: {str(e1)}")

                try:
                    # 方法2: 尝试使用webdriver-manager库
                    self.logger.info("🔄 尝试使用webdriver-manager库...")
                    from webdriver_manager.chrome import ChromeDriverManager

                    service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=options)
                    self.logger.info("✅ 成功使用webdriver-manager创建WebDriver")

                except ImportError:
                    self.logger.warning("⚠️ webdriver-manager未安装")
                    self.logger.info("💡 安装命令: pip install webdriver-manager")

                    try:
                        # 方法3: 尝试从PATH环境变量中查找
                        self.logger.info("🔄 尝试从PATH环境变量查找ChromeDriver...")
                        driver = webdriver.Chrome(options=options)
                        self.logger.info("✅ 成功从PATH创建WebDriver")

                    except Exception as e3:
                        self.logger.error(f"❌ 从PATH创建WebDriver失败: {str(e3)}")

                        # 方法4: 提供详细的错误信息和解决方案
                        self.logger.error("❌ 所有WebDriver创建方法都失败了")
                        self.print_driver_installation_guide()
                        return None

                except Exception as e2:
                    self.logger.error(f"❌ webdriver-manager创建失败: {str(e2)}")
                    return None

            if driver:
                driver.implicitly_wait(self.test_config["timeout"])
                return driver
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ 创建WebDriver失败: {e}")
            self.logger.error("💡 请参考Selenium文档: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location/")
            return None
    
    def test_component_functionality(self, driver, component_name: str, component_config: Dict) -> Dict:
        """测试组件功能"""
        results = {
            "name": component_config["name"],
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            for test_name in component_config["tests"]:
                test_result = {
                    "name": test_name,
                    "status": "unknown",
                    "message": ""
                }
                
                try:
                    # 根据测试类型执行相应检查
                    if "下拉菜单" in test_name:
                        dropdown = driver.find_element(By.CSS_SELECTOR, component_config["selectors"]["dropdown_toggle"])
                        if dropdown.is_displayed():
                            test_result["status"] = "passed"
                            test_result["message"] = "下拉菜单元素存在且可见"
                        else:
                            test_result["status"] = "failed"
                            test_result["message"] = "下拉菜单元素不可见"
                    
                    elif "模态框" in test_name:
                        modal_triggers = driver.find_elements(By.CSS_SELECTOR, component_config["selectors"]["modal_trigger"])
                        if modal_triggers:
                            test_result["status"] = "passed"
                            test_result["message"] = f"找到 {len(modal_triggers)} 个模态框触发器"
                        else:
                            test_result["status"] = "warning"
                            test_result["message"] = "未找到模态框触发器"
                    
                    elif "表单" in test_name:
                        form_groups = driver.find_elements(By.CSS_SELECTOR, component_config["selectors"]["form_group"])
                        if form_groups:
                            test_result["status"] = "passed"
                            test_result["message"] = f"找到 {len(form_groups)} 个表单组"
                        else:
                            test_result["status"] = "warning"
                            test_result["message"] = "未找到表单组元素"
                    
                    else:
                        # 通用检查：查找相关元素
                        selector = list(component_config["selectors"].values())[0]
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            test_result["status"] = "passed"
                            test_result["message"] = f"找到 {len(elements)} 个相关元素"
                        else:
                            test_result["status"] = "warning"
                            test_result["message"] = "未找到相关元素"
                
                except Exception as e:
                    test_result["status"] = "failed"
                    test_result["message"] = f"测试执行失败: {str(e)}"
                
                results["tests"].append(test_result)
                
                if test_result["status"] == "passed":
                    results["passed"] += 1
                elif test_result["status"] == "failed":
                    results["failed"] += 1
        
        except Exception as e:
            self.logger.error(f"❌ 测试组件 {component_name} 失败: {e}")
        
        return results
    
    def test_page_layout(self, driver, page_name: str, page_config: Dict) -> Dict:
        """测试页面布局"""
        results = {
            "name": page_config["name"],
            "url": page_config["url"],
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # 访问页面
            full_url = f"{self.base_url}{page_config['url']}"
            driver.get(full_url)
            time.sleep(2)  # 等待页面加载
            
            # 检查页面标题
            title = driver.title
            if title and title != "Error":
                results["tests"].append({
                    "name": "页面加载",
                    "status": "passed",
                    "message": f"页面标题: {title}"
                })
                results["passed"] += 1
            else:
                results["tests"].append({
                    "name": "页面加载",
                    "status": "failed",
                    "message": "页面加载失败或标题为空"
                })
                results["failed"] += 1
            
            # 检查Bootstrap类是否存在
            from selenium.webdriver.common.by import By
            
            bootstrap_elements = driver.find_elements(By.CSS_SELECTOR, "[class*='btn'], [class*='card'], [class*='container']")
            if bootstrap_elements:
                results["tests"].append({
                    "name": "Bootstrap样式",
                    "status": "passed",
                    "message": f"找到 {len(bootstrap_elements)} 个Bootstrap元素"
                })
                results["passed"] += 1
            else:
                results["tests"].append({
                    "name": "Bootstrap样式",
                    "status": "warning",
                    "message": "未找到明显的Bootstrap元素"
                })
            
            # 检查响应式布局
            original_size = driver.get_window_size()
            driver.set_window_size(375, 667)  # 移动端尺寸
            time.sleep(1)
            
            mobile_nav = driver.find_elements(By.CSS_SELECTOR, ".navbar-toggler")
            if mobile_nav:
                results["tests"].append({
                    "name": "移动端适配",
                    "status": "passed",
                    "message": "找到移动端导航切换按钮"
                })
                results["passed"] += 1
            else:
                results["tests"].append({
                    "name": "移动端适配",
                    "status": "warning",
                    "message": "未找到移动端导航元素"
                })
            
            # 恢复窗口尺寸
            driver.set_window_size(original_size["width"], original_size["height"])
        
        except Exception as e:
            results["tests"].append({
                "name": "页面测试",
                "status": "failed",
                "message": f"测试失败: {str(e)}"
            })
            results["failed"] += 1
        
        return results
    
    def run_automated_tests(self) -> bool:
        """运行自动化测试"""
        if not self.check_selenium_available():
            return False
        
        driver = self.create_webdriver()
        if not driver:
            return False
        
        try:
            self.logger.info("🚀 开始自动化测试...")
            
            # 测试组件功能
            self.logger.info("🔧 测试组件功能...")
            driver.get(self.base_url)
            
            for component_name, component_config in self.component_tests.items():
                self.logger.info(f"  测试 {component_config['name']}...")
                result = self.test_component_functionality(driver, component_name, component_config)
                self.test_results["component_tests"][component_name] = result
                
                self.test_results["summary"]["total_tests"] += len(result["tests"])
                self.test_results["summary"]["passed_tests"] += result["passed"]
                self.test_results["summary"]["failed_tests"] += result["failed"]
            
            # 测试页面布局
            self.logger.info("📄 测试页面布局...")
            for page_name, page_config in self.page_tests.items():
                if page_config.get("requires_auth") and "/auth/" not in page_config["url"]:
                    self.logger.info(f"  跳过需要认证的页面: {page_config['name']}")
                    continue
                
                self.logger.info(f"  测试 {page_config['name']}...")
                result = self.test_page_layout(driver, page_name, page_config)
                self.test_results["page_tests"][page_name] = result
                
                self.test_results["summary"]["total_tests"] += len(result["tests"])
                self.test_results["summary"]["passed_tests"] += result["passed"]
                self.test_results["summary"]["failed_tests"] += result["failed"]
            
            self.logger.info("✅ 自动化测试完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 自动化测试失败: {e}")
            return False
        finally:
            driver.quit()
    
    def run_manual_tests(self):
        """运行手动测试（生成测试清单）"""
        self.logger.info("📋 生成手动测试清单...")
        
        manual_tests = {
            "browser_compatibility": [
                "Chrome (最新版本)",
                "Firefox (最新版本)",
                "Safari (最新版本)",
                "Edge (最新版本)",
                "移动端浏览器"
            ],
            "visual_regression": [
                "对比升级前后的页面截图",
                "检查字体和颜色是否一致",
                "验证图标和按钮样式",
                "确认表格和表单布局"
            ],
            "functionality": [
                "测试所有交互功能",
                "验证JavaScript事件",
                "检查AJAX请求",
                "测试文件上传功能"
            ]
        }
        
        self.test_results["manual_tests"] = manual_tests
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        report = []
        report.append("# Bootstrap 5.3.6 升级测试报告")
        report.append("")
        report.append(f"**测试时间**: {self.test_results['test_info']['timestamp'][:19]}")
        report.append(f"**测试地址**: {self.test_results['test_info']['base_url']}")
        report.append("")
        
        # 测试摘要
        summary = self.test_results["summary"]
        report.append("## 📊 测试摘要")
        report.append("")
        report.append(f"- **总测试数**: {summary['total_tests']}")
        report.append(f"- **通过测试**: {summary['passed_tests']}")
        report.append(f"- **失败测试**: {summary['failed_tests']}")
        
        if summary['total_tests'] > 0:
            pass_rate = (summary['passed_tests'] / summary['total_tests']) * 100
            report.append(f"- **通过率**: {pass_rate:.1f}%")
        
        report.append("")
        
        # 组件测试结果
        if self.test_results["component_tests"]:
            report.append("## 🔧 组件测试结果")
            report.append("")
            
            for component_name, result in self.test_results["component_tests"].items():
                report.append(f"### {result['name']}")
                report.append("")
                
                for test in result["tests"]:
                    status_icon = {"passed": "✅", "failed": "❌", "warning": "⚠️"}.get(test["status"], "❓")
                    report.append(f"- {status_icon} {test['name']}: {test['message']}")
                
                report.append("")
        
        # 页面测试结果
        if self.test_results["page_tests"]:
            report.append("## 📄 页面测试结果")
            report.append("")
            
            for page_name, result in self.test_results["page_tests"].items():
                report.append(f"### {result['name']} ({result['url']})")
                report.append("")
                
                for test in result["tests"]:
                    status_icon = {"passed": "✅", "failed": "❌", "warning": "⚠️"}.get(test["status"], "❓")
                    report.append(f"- {status_icon} {test['name']}: {test['message']}")
                
                report.append("")
        
        # 手动测试清单
        if "manual_tests" in self.test_results:
            report.append("## 📋 手动测试清单")
            report.append("")
            
            manual_tests = self.test_results["manual_tests"]
            
            report.append("### 浏览器兼容性测试")
            for browser in manual_tests["browser_compatibility"]:
                report.append(f"- [ ] {browser}")
            report.append("")
            
            report.append("### 视觉回归测试")
            for test in manual_tests["visual_regression"]:
                report.append(f"- [ ] {test}")
            report.append("")
            
            report.append("### 功能测试")
            for test in manual_tests["functionality"]:
                report.append(f"- [ ] {test}")
            report.append("")
        
        return "\n".join(report)
    
    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON数据
        json_file = self.project_root / f"bootstrap_test_results_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        # 保存Markdown报告
        report_file = self.project_root / f"bootstrap_test_report_{timestamp}.md"
        report_content = self.generate_test_report()
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.info(f"📊 测试结果已保存: {json_file}")
        self.logger.info(f"📊 测试报告已保存: {report_file}")
    
    def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🧪 开始Bootstrap升级测试...")

        # 检查依赖
        if not self.check_and_install_dependencies():
            self.logger.warning("⚠️ 依赖检查失败，将跳过自动化测试")
            automated_success = False
        else:
            # 运行自动化测试
            automated_success = self.run_automated_tests()
        
        # 生成手动测试清单
        self.run_manual_tests()
        
        # 保存结果
        self.save_results()
        
        # 显示摘要
        summary = self.test_results["summary"]
        print(f"\n📊 测试完成摘要:")
        print(f"  - 总测试数: {summary['total_tests']}")
        print(f"  - 通过测试: {summary['passed_tests']}")
        print(f"  - 失败测试: {summary['failed_tests']}")
        
        if summary['total_tests'] > 0:
            pass_rate = (summary['passed_tests'] / summary['total_tests']) * 100
            print(f"  - 通过率: {pass_rate:.1f}%")
        
        if not automated_success:
            print("\n⚠️ 自动化测试未能完全执行，请参考手动测试清单")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Bootstrap升级测试工具")
    parser.add_argument("--url", default="http://localhost:8080", help="测试网站URL")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--install-guide", action="store_true", help="显示ChromeDriver安装指南")

    args = parser.parse_args()

    tester = BootstrapUpgradeTester(args.project_root, args.url)

    if args.install_guide:
        tester.print_driver_installation_guide()
    else:
        tester.run_all_tests()


if __name__ == "__main__":
    main()
