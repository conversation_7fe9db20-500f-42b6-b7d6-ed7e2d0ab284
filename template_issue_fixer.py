#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板问题修复工具
===============

基于模板超级检查工具的结果，自动修复常见的模板问题

功能特性:
1. 修复Bootstrap版本不一致问题
2. 更新过时的Bootstrap类名和属性
3. 修复缺失的CSRF保护
4. 优化性能问题
5. 修复静态资源引用
6. 生成修复报告
"""

import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import logging
import shutil


class TemplateIssueFixer:
    """模板问题修复工具"""
    
    def __init__(self, project_root: str = ".", check_results: Dict = None):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        self.check_results = check_results or {}
        
        # 修复规则
        self.bootstrap_fixes = {
            # Bootstrap 4 到 5 的属性映射
            "attributes": {
                r'\bdata-toggle\b': 'data-bs-toggle',
                r'\bdata-target\b': 'data-bs-target',
                r'\bdata-dismiss\b': 'data-bs-dismiss',
                r'\bdata-slide\b': 'data-bs-slide',
                r'\bdata-slide-to\b': 'data-bs-slide-to',
                r'\bdata-ride\b': 'data-bs-ride',
            },
            # Bootstrap 4 到 5 的类名映射
            "classes": {
                r'\btext-left\b': 'text-start',
                r'\btext-right\b': 'text-end',
                r'\bfloat-left\b': 'float-start',
                r'\bfloat-right\b': 'float-end',
                r'\bml-(\d+)\b': r'ms-\1',
                r'\bmr-(\d+)\b': r'me-\1',
                r'\bpl-(\d+)\b': r'ps-\1',
                r'\bpr-(\d+)\b': r'pe-\1',
                r'\bform-group\b': 'mb-3',
                r'\bsr-only\b': 'visually-hidden',
                r'\bno-gutters\b': 'g-0',
            },
            # CSS文件更新
            "css_files": {
                r'bootstrap/css/bootstrap\.min\.css': 'bootstrap/css/bootstrap.min.css',
                r'dataTables\.bootstrap4\.min\.css': 'dataTables.bootstrap5.min.css',
                r'select2-bootstrap4\.min\.css': 'select2-bootstrap5.min.css',
            },
            # JS文件更新
            "js_files": {
                r'dataTables\.bootstrap4\.min\.js': 'dataTables.bootstrap5.min.js',
            }
        }
        
        # 修复统计
        self.fix_stats = {
            "files_processed": 0,
            "files_modified": 0,
            "bootstrap_attributes_fixed": 0,
            "bootstrap_classes_fixed": 0,
            "css_files_updated": 0,
            "js_files_updated": 0,
            "csrf_tokens_added": 0,
            "performance_optimizations": 0,
            "errors": []
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def create_backup(self, file_path: Path) -> Path:
        """创建文件备份"""
        backup_dir = self.project_root / "template_backups" / datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        relative_path = file_path.relative_to(self.templates_root)
        backup_file = backup_dir / relative_path
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(file_path, backup_file)
        return backup_file
    
    def fix_bootstrap_attributes(self, content: str) -> Tuple[str, int]:
        """修复Bootstrap属性"""
        modified_content = content
        fixes_count = 0
        
        for old_attr, new_attr in self.bootstrap_fixes["attributes"].items():
            new_content = re.sub(old_attr, new_attr, modified_content)
            if new_content != modified_content:
                count = len(re.findall(old_attr, modified_content))
                fixes_count += count
                modified_content = new_content
                self.logger.debug(f"🔄 替换属性: {old_attr} -> {new_attr} ({count}次)")
        
        return modified_content, fixes_count
    
    def fix_bootstrap_classes(self, content: str) -> Tuple[str, int]:
        """修复Bootstrap类名"""
        modified_content = content
        fixes_count = 0
        
        for old_class, new_class in self.bootstrap_fixes["classes"].items():
            new_content = re.sub(old_class, new_class, modified_content)
            if new_content != modified_content:
                count = len(re.findall(old_class, modified_content))
                fixes_count += count
                modified_content = new_content
                self.logger.debug(f"🔄 替换类名: {old_class} -> {new_class} ({count}次)")
        
        return modified_content, fixes_count
    
    def fix_css_js_references(self, content: str) -> Tuple[str, int, int]:
        """修复CSS和JS文件引用"""
        modified_content = content
        css_fixes = 0
        js_fixes = 0
        
        # 修复CSS文件引用
        for old_css, new_css in self.bootstrap_fixes["css_files"].items():
            new_content = re.sub(old_css, new_css, modified_content)
            if new_content != modified_content:
                count = len(re.findall(old_css, modified_content))
                css_fixes += count
                modified_content = new_content
                self.logger.debug(f"🔄 替换CSS: {old_css} -> {new_css}")
        
        # 修复JS文件引用
        for old_js, new_js in self.bootstrap_fixes["js_files"].items():
            new_content = re.sub(old_js, new_js, modified_content)
            if new_content != modified_content:
                count = len(re.findall(old_js, modified_content))
                js_fixes += count
                modified_content = new_content
                self.logger.debug(f"🔄 替换JS: {old_js} -> {new_js}")
        
        return modified_content, css_fixes, js_fixes
    
    def add_csrf_protection(self, content: str) -> Tuple[str, int]:
        """添加CSRF保护"""
        modified_content = content
        csrf_added = 0
        
        # 查找POST表单但没有CSRF保护的
        form_pattern = r'(<form[^>]*method\s*=\s*["\']post["\'][^>]*>)'
        csrf_pattern = r'csrf_token|{{ csrf_token() }}'
        
        def add_csrf_to_form(match):
            form_tag = match.group(1)
            # 检查表单后面是否已有CSRF token
            form_start = match.end()
            form_content = content[form_start:form_start+500]  # 检查表单开始后500字符
            
            if not re.search(csrf_pattern, form_content, re.IGNORECASE):
                return form_tag + '\n        {{ csrf_token() }}'
            return form_tag
        
        new_content = re.sub(form_pattern, add_csrf_to_form, modified_content, flags=re.IGNORECASE)
        if new_content != modified_content:
            csrf_added = len(re.findall(form_pattern, modified_content, re.IGNORECASE))
            modified_content = new_content
        
        return modified_content, csrf_added
    
    def optimize_performance(self, content: str) -> Tuple[str, int]:
        """性能优化"""
        modified_content = content
        optimizations = 0
        
        # 移除多余的空白行（连续3个以上的空行）
        new_content = re.sub(r'\n\s*\n\s*\n\s*\n+', '\n\n\n', modified_content)
        if new_content != modified_content:
            optimizations += 1
            modified_content = new_content
        
        # 优化内联样式（提示性修复，不自动修改）
        inline_styles = re.findall(r'style\s*=\s*["\'][^"\']+["\']', modified_content)
        if len(inline_styles) > 10:
            self.logger.warning(f"⚠️ 发现{len(inline_styles)}个内联样式，建议提取到CSS文件")
        
        return modified_content, optimizations
    
    def fix_template_file(self, file_path: Path) -> Dict:
        """修复单个模板文件"""
        fix_result = {
            "file": str(file_path.relative_to(self.project_root)),
            "backup_created": False,
            "modifications": {
                "bootstrap_attributes": 0,
                "bootstrap_classes": 0,
                "css_files": 0,
                "js_files": 0,
                "csrf_tokens": 0,
                "performance": 0
            },
            "total_changes": 0,
            "errors": []
        }
        
        try:
            self.logger.info(f"🔧 修复文件: {file_path.relative_to(self.templates_root)}")
            
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            
            # 1. 修复Bootstrap属性
            modified_content, attr_fixes = self.fix_bootstrap_attributes(modified_content)
            fix_result["modifications"]["bootstrap_attributes"] = attr_fixes
            self.fix_stats["bootstrap_attributes_fixed"] += attr_fixes
            
            # 2. 修复Bootstrap类名
            modified_content, class_fixes = self.fix_bootstrap_classes(modified_content)
            fix_result["modifications"]["bootstrap_classes"] = class_fixes
            self.fix_stats["bootstrap_classes_fixed"] += class_fixes
            
            # 3. 修复CSS/JS引用
            modified_content, css_fixes, js_fixes = self.fix_css_js_references(modified_content)
            fix_result["modifications"]["css_files"] = css_fixes
            fix_result["modifications"]["js_files"] = js_fixes
            self.fix_stats["css_files_updated"] += css_fixes
            self.fix_stats["js_files_updated"] += js_fixes
            
            # 4. 添加CSRF保护
            modified_content, csrf_fixes = self.add_csrf_protection(modified_content)
            fix_result["modifications"]["csrf_tokens"] = csrf_fixes
            self.fix_stats["csrf_tokens_added"] += csrf_fixes
            
            # 5. 性能优化
            modified_content, perf_fixes = self.optimize_performance(modified_content)
            fix_result["modifications"]["performance"] = perf_fixes
            self.fix_stats["performance_optimizations"] += perf_fixes
            
            # 计算总变更数
            fix_result["total_changes"] = sum(fix_result["modifications"].values())
            
            # 如果有修改，创建备份并保存
            if modified_content != original_content:
                # 创建备份
                backup_file = self.create_backup(file_path)
                fix_result["backup_created"] = True
                self.logger.debug(f"📋 备份创建: {backup_file}")
                
                # 保存修改后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                self.fix_stats["files_modified"] += 1
                self.logger.info(f"✅ 文件已修复: {fix_result['total_changes']} 处修改")
            else:
                self.logger.info("ℹ️ 文件无需修复")
        
        except Exception as e:
            error_msg = f"修复文件失败: {str(e)}"
            fix_result["errors"].append(error_msg)
            self.fix_stats["errors"].append(f"{file_path}: {error_msg}")
            self.logger.error(f"❌ {error_msg}")
        
        finally:
            self.fix_stats["files_processed"] += 1
        
        return fix_result
    
    def run_fixes_from_check_results(self) -> List[Dict]:
        """基于检查结果运行修复"""
        if not self.check_results or "files" not in self.check_results:
            self.logger.error("❌ 没有检查结果数据")
            return []
        
        fix_results = []
        files_with_issues = []
        
        # 找出有问题的文件
        for file_path, file_data in self.check_results["files"].items():
            if file_data["issues"]:
                files_with_issues.append(self.project_root / file_path)
        
        if not files_with_issues:
            self.logger.info("✅ 没有发现需要修复的问题")
            return []
        
        self.logger.info(f"🔧 开始修复 {len(files_with_issues)} 个有问题的文件...")
        
        for file_path in files_with_issues:
            if file_path.exists():
                fix_result = self.fix_template_file(file_path)
                fix_results.append(fix_result)
        
        return fix_results
    
    def run_fixes_all_templates(self) -> List[Dict]:
        """修复所有模板文件"""
        fix_results = []
        
        # 扫描所有模板文件
        template_files = list(self.templates_root.rglob("*.html"))
        
        if not template_files:
            self.logger.warning("⚠️ 未找到模板文件")
            return []
        
        self.logger.info(f"🔧 开始修复 {len(template_files)} 个模板文件...")
        
        for file_path in template_files:
            fix_result = self.fix_template_file(file_path)
            fix_results.append(fix_result)
        
        return fix_results
    
    def generate_fix_report(self, fix_results: List[Dict]) -> str:
        """生成修复报告"""
        report = []
        report.append("=" * 80)
        report.append("🔧 模板问题修复报告")
        report.append("=" * 80)
        
        # 修复统计
        stats = self.fix_stats
        report.append(f"📊 修复统计:")
        report.append(f"  • 处理文件: {stats['files_processed']} 个")
        report.append(f"  • 修改文件: {stats['files_modified']} 个")
        report.append(f"  • Bootstrap属性修复: {stats['bootstrap_attributes_fixed']} 处")
        report.append(f"  • Bootstrap类名修复: {stats['bootstrap_classes_fixed']} 处")
        report.append(f"  • CSS文件更新: {stats['css_files_updated']} 处")
        report.append(f"  • JS文件更新: {stats['js_files_updated']} 处")
        report.append(f"  • CSRF保护添加: {stats['csrf_tokens_added']} 处")
        report.append(f"  • 性能优化: {stats['performance_optimizations']} 处")
        report.append("")
        
        # 修改详情
        modified_files = [r for r in fix_results if r["total_changes"] > 0]
        if modified_files:
            report.append(f"📝 修改的文件 ({len(modified_files)} 个):")
            for result in modified_files:
                report.append(f"  • {result['file']}: {result['total_changes']} 处修改")
                mods = result["modifications"]
                details = []
                if mods["bootstrap_attributes"]: details.append(f"属性{mods['bootstrap_attributes']}")
                if mods["bootstrap_classes"]: details.append(f"类名{mods['bootstrap_classes']}")
                if mods["css_files"]: details.append(f"CSS{mods['css_files']}")
                if mods["js_files"]: details.append(f"JS{mods['js_files']}")
                if mods["csrf_tokens"]: details.append(f"CSRF{mods['csrf_tokens']}")
                if mods["performance"]: details.append(f"性能{mods['performance']}")
                if details:
                    report.append(f"    ({', '.join(details)})")
            report.append("")
        
        # 错误信息
        if stats["errors"]:
            report.append(f"❌ 错误 ({len(stats['errors'])} 个):")
            for error in stats["errors"]:
                report.append(f"  • {error}")
            report.append("")
        
        # 建议
        report.append("💡 后续建议:")
        report.append("  1. 测试修复后的页面功能")
        report.append("  2. 检查Bootstrap 5的新特性")
        report.append("  3. 验证CSRF保护是否正常工作")
        report.append("  4. 考虑进一步的性能优化")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_fix_report(self, fix_results: List[Dict]):
        """保存修复报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"template_fix_report_{timestamp}.json"
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "statistics": self.fix_stats,
            "fix_results": fix_results
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 保存摘要报告
        summary_file = self.project_root / f"template_fix_summary_{timestamp}.txt"
        summary_report = self.generate_fix_report(fix_results)
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        self.logger.info(f"📊 修复报告已保存: {json_file}")
        self.logger.info(f"📊 摘要报告已保存: {summary_file}")
        
        # 显示摘要
        print(summary_report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模板问题修复工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--check-results", help="检查结果JSON文件路径")
    parser.add_argument("--fix-all", action="store_true", help="修复所有模板文件")
    
    args = parser.parse_args()
    
    # 加载检查结果
    check_results = None
    if args.check_results:
        try:
            with open(args.check_results, 'r', encoding='utf-8') as f:
                check_results = json.load(f)
        except Exception as e:
            print(f"❌ 加载检查结果失败: {e}")
            return
    
    # 创建修复器
    fixer = TemplateIssueFixer(args.project_root, check_results)
    
    # 运行修复
    if args.fix_all:
        fix_results = fixer.run_fixes_all_templates()
    else:
        fix_results = fixer.run_fixes_from_check_results()
    
    # 保存报告
    fixer.save_fix_report(fix_results)


if __name__ == "__main__":
    main()
