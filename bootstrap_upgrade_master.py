#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5.3.6 升级主控制器
==========================

基于您的专业建议，整合所有升级工具的主控制脚本

完整的升级流程:
1. 兼容性检查
2. 第三方库更新
3. 执行迁移
4. 功能测试
5. 生成升级日志

使用方法:
python bootstrap_upgrade_master.py --mode full
"""

import sys
import os
import argparse
from pathlib import Path
from datetime import datetime
import logging


class BootstrapUpgradeMaster:
    """Bootstrap升级主控制器"""
    
    def __init__(self, project_root: str = ".", mode: str = "preview"):
        self.project_root = Path(project_root).resolve()
        self.mode = mode
        
        # 设置日志
        self.setup_logging()
        
        # 导入工具模块
        self.import_tools()
        
        # 升级状态
        self.upgrade_status = {
            "start_time": datetime.now(),
            "steps_completed": [],
            "errors": [],
            "warnings": []
        }
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.project_root / f"bootstrap_upgrade_master_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def import_tools(self):
        """导入升级工具"""
        try:
            # 添加当前目录到Python路径
            sys.path.insert(0, str(self.project_root))
            
            # 导入工具模块
            from bootstrap_compatibility_checker import BootstrapCompatibilityChecker
            from third_party_compatibility_updater import ThirdPartyCompatibilityUpdater
            from bootstrap_5_3_6_migration_tool import Bootstrap536MigrationTool
            from bootstrap_upgrade_tester import BootstrapUpgradeTester
            from upgrade_log_generator import UpgradeLogGenerator
            
            self.compatibility_checker = BootstrapCompatibilityChecker(str(self.project_root))
            self.third_party_updater = ThirdPartyCompatibilityUpdater(str(self.project_root))
            self.migration_tool = Bootstrap536MigrationTool(str(self.project_root))
            self.tester = BootstrapUpgradeTester(str(self.project_root), "http://localhost:8080")
            self.log_generator = UpgradeLogGenerator(str(self.project_root))
            
            self.logger.info("✅ 所有升级工具已加载")
            
        except ImportError as e:
            self.logger.error(f"❌ 导入工具失败: {e}")
            self.logger.error("请确保所有工具文件都在项目目录中")
            sys.exit(1)
    
    def print_banner(self):
        """打印横幅"""
        print("=" * 80)
        print("🚀 StudentsCMSSP Bootstrap 5.3.6 升级主控制器")
        print("=" * 80)
        print("📋 完整升级流程:")
        print("  1. 兼容性检查 - 分析当前项目状态")
        print("  2. 第三方库更新 - 处理依赖库兼容性")
        print("  3. 执行迁移 - 自动替换Bootstrap引用")
        print("  4. 功能测试 - 验证升级结果")
        print("  5. 生成升级日志 - 记录所有变更")
        print("=" * 80)
        print(f"🔧 运行模式: {self.mode}")
        print(f"📁 项目目录: {self.project_root}")
        print("=" * 80)
    
    def step_1_compatibility_check(self) -> bool:
        """步骤1: 兼容性检查"""
        try:
            print("\n🔍 步骤1: 兼容性检查")
            print("-" * 40)
            
            # 运行兼容性检查
            results = self.compatibility_checker.run_compatibility_check()
            
            # 生成报告
            report = self.compatibility_checker.generate_report()
            print(report)
            
            # 保存报告
            self.compatibility_checker.save_report(report)
            
            # 检查兼容性分数
            score = results["summary"]["compatibility_score"]
            if score >= 90:
                print(f"✅ 兼容性检查通过 (分数: {score}/100)")
                self.upgrade_status["steps_completed"].append("compatibility_check")
                return True
            elif score >= 70:
                print(f"⚠️ 兼容性检查发现问题 (分数: {score}/100)")
                if self.mode == "preview":
                    print("💡 建议先解决兼容性问题再进行升级")
                    return False
                else:
                    print("⚠️ 继续升级，但请注意兼容性问题")
                    self.upgrade_status["warnings"].append(f"兼容性分数较低: {score}/100")
                    self.upgrade_status["steps_completed"].append("compatibility_check")
                    return True
            else:
                print(f"❌ 兼容性检查失败 (分数: {score}/100)")
                print("💡 建议先解决兼容性问题再进行升级")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 兼容性检查失败: {e}")
            self.upgrade_status["errors"].append(f"兼容性检查失败: {str(e)}")
            return False
    
    def step_2_third_party_update(self) -> bool:
        """步骤2: 第三方库更新"""
        try:
            print("\n🔧 步骤2: 第三方库更新")
            print("-" * 40)
            
            if self.mode == "preview":
                print("🔍 预览模式: 检查第三方库兼容性...")
            
            # 运行第三方库兼容性检查和更新
            results = self.third_party_updater.run_compatibility_check()
            
            # 显示结果
            print(f"📊 检查结果:")
            print(f"  - 兼容库: {len(results['compatible_libraries'])}")
            print(f"  - 需要处理: {len(results['incompatible_libraries'])}")
            print(f"  - 已更新: {len(results['updated_libraries'])}")
            
            if results['manual_tasks']:
                print(f"  - 手动任务: {len(results['manual_tasks'])}")
                print("📋 手动任务:")
                for task in results['manual_tasks'][:3]:
                    print(f"    • {task}")
                if len(results['manual_tasks']) > 3:
                    print(f"    ... 还有 {len(results['manual_tasks']) - 3} 个任务")
            
            # 保存报告
            self.third_party_updater.save_report()
            
            self.upgrade_status["steps_completed"].append("third_party_update")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 第三方库更新失败: {e}")
            self.upgrade_status["errors"].append(f"第三方库更新失败: {str(e)}")
            return False
    
    def step_3_migration(self) -> bool:
        """步骤3: 执行迁移"""
        try:
            print("\n🔄 步骤3: 执行迁移")
            print("-" * 40)
            
            dry_run = (self.mode == "preview")
            
            if dry_run:
                print("🔍 预览模式: 不会修改任何文件")
            else:
                print("✏️ 执行模式: 将修改项目文件")
            
            # 运行迁移
            self.migration_tool.run_migration(
                dry_run=dry_run,
                download_bootstrap=not dry_run
            )
            
            self.upgrade_status["steps_completed"].append("migration")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移失败: {e}")
            self.upgrade_status["errors"].append(f"迁移失败: {str(e)}")
            return False
    
    def step_4_testing(self) -> bool:
        """步骤4: 功能测试"""
        try:
            print("\n🧪 步骤4: 功能测试")
            print("-" * 40)
            
            if self.mode == "preview":
                print("🔍 预览模式: 生成测试清单...")
                # 只生成手动测试清单
                self.tester.run_manual_tests()
                print("📋 已生成手动测试清单")
            else:
                print("🚀 执行自动化测试...")
                # 运行完整测试
                self.tester.run_all_tests()
            
            self.upgrade_status["steps_completed"].append("testing")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 功能测试失败: {e}")
            self.upgrade_status["errors"].append(f"功能测试失败: {str(e)}")
            return False
    
    def step_5_generate_log(self) -> bool:
        """步骤5: 生成升级日志"""
        try:
            print("\n📊 步骤5: 生成升级日志")
            print("-" * 40)
            
            # 添加升级信息到日志生成器
            self.log_generator.add_manual_task(
                "检查所有页面的布局和功能", 
                "high", 
                "页面测试"
            )
            
            if self.upgrade_status["warnings"]:
                for warning in self.upgrade_status["warnings"]:
                    self.log_generator.add_warning(warning)
            
            if self.upgrade_status["errors"]:
                for error in self.upgrade_status["errors"]:
                    self.log_generator.add_warning(f"错误: {error}")
            
            # 生成并保存日志
            self.log_generator.save_log("UPGRADE_LOG.md")
            
            print("✅ 升级日志已生成")
            self.upgrade_status["steps_completed"].append("generate_log")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 生成升级日志失败: {e}")
            self.upgrade_status["errors"].append(f"生成升级日志失败: {str(e)}")
            return False
    
    def print_summary(self):
        """打印升级摘要"""
        end_time = datetime.now()
        duration = end_time - self.upgrade_status["start_time"]
        
        print("\n" + "=" * 80)
        print("📊 Bootstrap 5.3.6 升级完成摘要")
        print("=" * 80)
        
        print(f"⏱️ 升级耗时: {duration}")
        print(f"✅ 完成步骤: {len(self.upgrade_status['steps_completed'])}/5")
        
        if self.upgrade_status["steps_completed"]:
            print("📋 已完成步骤:")
            step_names = {
                "compatibility_check": "兼容性检查",
                "third_party_update": "第三方库更新",
                "migration": "执行迁移",
                "testing": "功能测试",
                "generate_log": "生成升级日志"
            }
            for step in self.upgrade_status["steps_completed"]:
                print(f"  ✅ {step_names.get(step, step)}")
        
        if self.upgrade_status["warnings"]:
            print(f"⚠️ 警告数量: {len(self.upgrade_status['warnings'])}")
            for warning in self.upgrade_status["warnings"][:3]:
                print(f"  • {warning}")
            if len(self.upgrade_status["warnings"]) > 3:
                print(f"  ... 还有 {len(self.upgrade_status['warnings']) - 3} 个警告")
        
        if self.upgrade_status["errors"]:
            print(f"❌ 错误数量: {len(self.upgrade_status['errors'])}")
            for error in self.upgrade_status["errors"][:3]:
                print(f"  • {error}")
            if len(self.upgrade_status["errors"]) > 3:
                print(f"  ... 还有 {len(self.upgrade_status['errors']) - 3} 个错误")
        
        print("\n💡 后续建议:")
        if self.mode == "preview":
            print("  1. 查看生成的兼容性报告")
            print("  2. 解决发现的兼容性问题")
            print("  3. 运行完整升级: python bootstrap_upgrade_master.py --mode full")
        else:
            print("  1. 查看升级日志 (UPGRADE_LOG.md)")
            print("  2. 执行手动测试清单")
            print("  3. 在测试环境验证所有功能")
            print("  4. 部署到生产环境前进行完整测试")
        
        print("=" * 80)
    
    def run_upgrade(self):
        """运行完整升级流程"""
        self.print_banner()
        
        try:
            # 步骤1: 兼容性检查
            if not self.step_1_compatibility_check():
                print("\n❌ 升级中止: 兼容性检查未通过")
                return False
            
            # 步骤2: 第三方库更新
            if not self.step_2_third_party_update():
                print("\n❌ 升级中止: 第三方库更新失败")
                return False
            
            # 步骤3: 执行迁移
            if not self.step_3_migration():
                print("\n❌ 升级中止: 迁移失败")
                return False
            
            # 步骤4: 功能测试
            if not self.step_4_testing():
                print("\n⚠️ 功能测试失败，但继续执行")
            
            # 步骤5: 生成升级日志
            if not self.step_5_generate_log():
                print("\n⚠️ 生成升级日志失败，但升级已完成")
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n❌ 用户中断升级")
            return False
        except Exception as e:
            self.logger.error(f"❌ 升级过程中发生未知错误: {e}")
            return False
        finally:
            self.print_summary()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Bootstrap 5.3.6 升级主控制器")
    parser.add_argument("--mode", choices=["preview", "full"], default="preview",
                       help="运行模式: preview(预览) 或 full(完整升级)")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 检查项目结构
    project_root = Path(args.project_root).resolve()
    if not (project_root / "app").exists():
        print("❌ 错误: 未找到 'app' 目录")
        print("💡 请确保在StudentsCMSSP项目根目录中运行此脚本")
        sys.exit(1)
    
    # 创建升级控制器
    master = BootstrapUpgradeMaster(args.project_root, args.mode)
    
    # 运行升级
    success = master.run_upgrade()
    
    # 退出状态
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
