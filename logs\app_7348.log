2025-06-19 21:27:00,834 INFO: 应用启动 - PID: 7348 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 21:45:46,949 ERROR: 创建付款记录失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO payment_records (payment_number, area_id, payment_date, amount, payment_method, payable_id, supplier_id, bank_account, reference_number, voucher_id, summary, status, created_by, reviewed_by, reviewed_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PAY20250619001', 44, datetime.datetime(2025, 6, 19, 0, 0), Decimal('12376.00'), '现金', 12, 27, '6666666666666666666666', 'DDD', None, 'SSSS', '已确认', 38, None, None, '', datetime.datetime(2025, 6, 19, 21, 45, 46), datetime.datetime(2025, 6, 19, 21, 45, 46))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\payments.py:293]
