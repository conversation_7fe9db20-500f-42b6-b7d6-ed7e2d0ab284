#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 兼容性检查工具
======================

在执行迁移前检查项目的Bootstrap兼容性，识别潜在问题

功能:
1. 检查当前Bootstrap版本
2. 扫描不兼容的类名和属性
3. 检查第三方库兼容性
4. 生成兼容性报告
"""

import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import logging


class BootstrapCompatibilityChecker:
    """Bootstrap兼容性检查器"""
    
    def __init__(self, project_root: str = "."):
        """初始化检查器"""
        self.project_root = Path(project_root).resolve()
        self.app_root = self.project_root / "app"
        self.templates_root = self.app_root / "templates"
        self.static_root = self.app_root / "static"
        
        # 不兼容的类名模式
        self.incompatible_classes = {
            # 文本对齐
            r'\btext-left\b': 'text-start',
            r'\btext-right\b': 'text-end',
            
            # 间距类
            r'\bml-(\d+)\b': 'ms-\\1',
            r'\bmr-(\d+)\b': 'me-\\1',
            r'\bpl-(\d+)\b': 'ps-\\1',
            r'\bpr-(\d+)\b': 'pe-\\1',
            
            # 表单类
            r'\bform-group\b': 'mb-3',
            r'\bform-row\b': 'row g-3',
            
            # 工具类
            r'\bsr-only\b': 'visually-hidden',
            r'\bfont-weight-(\w+)\b': 'fw-\\1',
            r'\bfont-italic\b': 'fst-italic',
            
            # 网格系统
            r'\bno-gutters\b': 'g-0',
            r'\bcard-deck\b': 'row row-cols-1 row-cols-md-3 g-4',
        }
        
        # 不兼容的属性
        self.incompatible_attributes = {
            r'\bdata-toggle=': 'data-bs-toggle=',
            r'\bdata-target=': 'data-bs-target=',
            r'\bdata-dismiss=': 'data-bs-dismiss=',
            r'\bdata-slide=': 'data-bs-slide=',
            r'\bdata-slide-to=': 'data-bs-slide-to=',
        }
        
        # 需要检查的第三方库
        self.third_party_libraries = {
            'datatables': {
                'bootstrap4_file': 'vendor/datatables/css/dataTables.bootstrap4.min.css',
                'bootstrap5_file': 'vendor/datatables/css/dataTables.bootstrap5.min.css',
                'description': 'DataTables Bootstrap兼容性'
            },
            'select2': {
                'bootstrap4_file': 'vendor/select2/css/select2-bootstrap4.min.css',
                'bootstrap5_file': 'vendor/select2/css/select2-bootstrap5.min.css',
                'description': 'Select2 Bootstrap兼容性'
            }
        }
        
        # 检查结果
        self.results = {
            'bootstrap_version': None,
            'incompatible_classes': [],
            'incompatible_attributes': [],
            'third_party_issues': [],
            'file_issues': [],
            'summary': {}
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def detect_bootstrap_version(self) -> str:
        """检测当前Bootstrap版本"""
        bootstrap_css = self.static_root / "bootstrap" / "css" / "bootstrap.min.css"
        
        if not bootstrap_css.exists():
            return "未找到Bootstrap文件"
        
        try:
            with open(bootstrap_css, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 只读前1000字符
            
            # 查找版本信息
            version_match = re.search(r'Bootstrap v(\d+\.\d+\.\d+)', content)
            if version_match:
                return version_match.group(1)
            
            # 如果没找到版本信息，通过特征判断
            if '--bs-' in content:
                return "5.x (推测)"
            elif '--blue:' in content:
                return "4.x (推测)"
            else:
                return "未知版本"
                
        except Exception as e:
            self.logger.error(f"检测Bootstrap版本失败: {e}")
            return "检测失败"
    
    def scan_template_files(self) -> List[Path]:
        """扫描模板文件"""
        if not self.templates_root.exists():
            return []
        
        return list(self.templates_root.rglob("*.html"))
    
    def check_file_compatibility(self, file_path: Path) -> Dict:
        """检查单个文件的兼容性"""
        issues = {
            'file': str(file_path.relative_to(self.project_root)),
            'incompatible_classes': [],
            'incompatible_attributes': [],
            'line_numbers': {}
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # 检查不兼容的类名
                for pattern, replacement in self.incompatible_classes.items():
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        issue = {
                            'pattern': pattern,
                            'found': match.group(),
                            'replacement': replacement,
                            'line': line_num,
                            'context': line.strip()
                        }
                        issues['incompatible_classes'].append(issue)
                
                # 检查不兼容的属性
                for pattern, replacement in self.incompatible_attributes.items():
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        issue = {
                            'pattern': pattern,
                            'found': match.group(),
                            'replacement': replacement,
                            'line': line_num,
                            'context': line.strip()
                        }
                        issues['incompatible_attributes'].append(issue)
        
        except Exception as e:
            self.logger.error(f"检查文件 {file_path} 失败: {e}")
            issues['error'] = str(e)
        
        return issues
    
    def check_third_party_compatibility(self):
        """检查第三方库兼容性"""
        for lib_name, lib_info in self.third_party_libraries.items():
            issue = {
                'library': lib_name,
                'description': lib_info['description'],
                'status': 'unknown',
                'bootstrap4_exists': False,
                'bootstrap5_exists': False,
                'recommendation': ''
            }
            
            # 检查Bootstrap 4版本文件
            bs4_file = self.static_root / lib_info['bootstrap4_file']
            issue['bootstrap4_exists'] = bs4_file.exists()
            
            # 检查Bootstrap 5版本文件
            bs5_file = self.static_root / lib_info['bootstrap5_file']
            issue['bootstrap5_exists'] = bs5_file.exists()
            
            # 确定状态和建议
            if issue['bootstrap4_exists'] and issue['bootstrap5_exists']:
                issue['status'] = 'ready'
                issue['recommendation'] = '已准备好Bootstrap 5迁移'
            elif issue['bootstrap4_exists'] and not issue['bootstrap5_exists']:
                issue['status'] = 'needs_update'
                issue['recommendation'] = f'需要下载Bootstrap 5兼容版本: {lib_info["bootstrap5_file"]}'
            elif not issue['bootstrap4_exists'] and not issue['bootstrap5_exists']:
                issue['status'] = 'not_found'
                issue['recommendation'] = '未找到相关文件，可能不使用此库'
            else:
                issue['status'] = 'bootstrap5_only'
                issue['recommendation'] = '只有Bootstrap 5版本，可能已经升级'
            
            self.results['third_party_issues'].append(issue)
    
    def run_compatibility_check(self) -> Dict:
        """运行完整的兼容性检查"""
        self.logger.info("🔍 开始Bootstrap兼容性检查...")
        
        # 1. 检测Bootstrap版本
        self.results['bootstrap_version'] = self.detect_bootstrap_version()
        self.logger.info(f"📦 当前Bootstrap版本: {self.results['bootstrap_version']}")
        
        # 2. 扫描模板文件
        template_files = self.scan_template_files()
        self.logger.info(f"📁 找到 {len(template_files)} 个模板文件")
        
        # 3. 检查每个文件
        total_class_issues = 0
        total_attr_issues = 0
        
        for file_path in template_files:
            file_issues = self.check_file_compatibility(file_path)
            
            if file_issues['incompatible_classes'] or file_issues['incompatible_attributes']:
                self.results['file_issues'].append(file_issues)
                total_class_issues += len(file_issues['incompatible_classes'])
                total_attr_issues += len(file_issues['incompatible_attributes'])
        
        # 4. 检查第三方库
        self.check_third_party_compatibility()
        
        # 5. 生成摘要
        self.results['summary'] = {
            'total_files_scanned': len(template_files),
            'files_with_issues': len(self.results['file_issues']),
            'total_class_issues': total_class_issues,
            'total_attribute_issues': total_attr_issues,
            'third_party_libraries': len(self.third_party_libraries),
            'libraries_need_update': len([lib for lib in self.results['third_party_issues'] 
                                        if lib['status'] == 'needs_update']),
            'compatibility_score': self.calculate_compatibility_score()
        }
        
        self.logger.info("✅ 兼容性检查完成")
        return self.results
    
    def calculate_compatibility_score(self) -> float:
        """计算兼容性分数 (0-100)"""
        total_issues = (
            self.results['summary']['total_class_issues'] +
            self.results['summary']['total_attribute_issues'] +
            self.results['summary']['libraries_need_update']
        )
        
        if total_issues == 0:
            return 100.0
        
        # 基于问题数量计算分数
        max_issues = 100  # 假设最大问题数
        score = max(0, 100 - (total_issues / max_issues * 100))
        return round(score, 1)
    
    def generate_report(self) -> str:
        """生成兼容性报告"""
        report = []
        report.append("=" * 60)
        report.append("🔍 Bootstrap 兼容性检查报告")
        report.append("=" * 60)
        
        # 基本信息
        report.append(f"📦 当前Bootstrap版本: {self.results['bootstrap_version']}")
        report.append(f"📊 兼容性分数: {self.results['summary']['compatibility_score']}/100")
        report.append("")
        
        # 摘要统计
        summary = self.results['summary']
        report.append("📋 检查摘要:")
        report.append(f"  • 扫描文件数: {summary['total_files_scanned']}")
        report.append(f"  • 有问题文件: {summary['files_with_issues']}")
        report.append(f"  • 类名问题: {summary['total_class_issues']}")
        report.append(f"  • 属性问题: {summary['total_attribute_issues']}")
        report.append(f"  • 需要更新的库: {summary['libraries_need_update']}")
        report.append("")
        
        # 第三方库状态
        if self.results['third_party_issues']:
            report.append("📚 第三方库兼容性:")
            for lib in self.results['third_party_issues']:
                status_icon = {
                    'ready': '✅',
                    'needs_update': '⚠️',
                    'not_found': 'ℹ️',
                    'bootstrap5_only': '🔄'
                }.get(lib['status'], '❓')
                
                report.append(f"  {status_icon} {lib['library']}: {lib['recommendation']}")
            report.append("")
        
        # 详细问题列表
        if self.results['file_issues']:
            report.append("🔧 需要修复的问题:")
            for file_issue in self.results['file_issues'][:5]:  # 只显示前5个文件
                report.append(f"\n📄 {file_issue['file']}:")
                
                for issue in file_issue['incompatible_classes'][:3]:  # 每个文件只显示前3个问题
                    report.append(f"  • 第{issue['line']}行: {issue['found']} → {issue['replacement']}")
                
                for issue in file_issue['incompatible_attributes'][:3]:
                    report.append(f"  • 第{issue['line']}行: {issue['found']} → {issue['replacement']}")
            
            if len(self.results['file_issues']) > 5:
                report.append(f"\n... 还有 {len(self.results['file_issues']) - 5} 个文件有问题")
        
        # 建议
        report.append("\n💡 建议:")
        if summary['compatibility_score'] >= 90:
            report.append("  ✅ 项目兼容性良好，可以安全迁移")
        elif summary['compatibility_score'] >= 70:
            report.append("  ⚠️ 有少量兼容性问题，建议先修复后迁移")
        else:
            report.append("  ❌ 存在较多兼容性问题，建议详细检查后再迁移")
        
        report.append("  📖 详细迁移指南请参考: BOOTSTRAP_5_3_6_UPGRADE_GUIDE.md")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def save_report(self, report_content: str):
        """保存报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.project_root / f"bootstrap_compatibility_report_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            # 同时保存JSON格式的详细数据
            json_file = self.project_root / f"bootstrap_compatibility_data_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 报告已保存: {report_file}")
            self.logger.info(f"📊 详细数据已保存: {json_file}")
            
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")


def main():
    """主函数"""
    print("🔍 Bootstrap 兼容性检查工具")
    print("=" * 40)
    
    checker = BootstrapCompatibilityChecker(".")
    results = checker.run_compatibility_check()
    
    # 生成并显示报告
    report = checker.generate_report()
    print(report)
    
    # 保存报告
    checker.save_report(report)


if __name__ == "__main__":
    main()
