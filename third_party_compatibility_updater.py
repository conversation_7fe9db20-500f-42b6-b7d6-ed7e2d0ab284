#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三方库Bootstrap兼容性更新工具
=============================

基于您的建议，专门处理第三方库的Bootstrap 5兼容性问题

支持的库:
1. DataTables - 数据表格
2. jQuery UI - UI组件
3. Select2 - 下拉选择器
4. FontAwesome - 图标库
5. Toastr - 通知组件
"""

import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging


class ThirdPartyCompatibilityUpdater:
    """第三方库兼容性更新器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.static_root = self.project_root / "app" / "static"
        self.vendor_root = self.static_root / "vendor"
        
        # 第三方库配置
        self.libraries = {
            "datatables": {
                "name": "DataTables",
                "description": "数据表格组件",
                "bootstrap4_files": [
                    "vendor/datatables/css/dataTables.bootstrap4.min.css",
                    "vendor/datatables/js/dataTables.bootstrap4.min.js"
                ],
                "bootstrap5_files": [
                    "vendor/datatables/css/dataTables.bootstrap5.min.css",
                    "vendor/datatables/js/dataTables.bootstrap5.min.js"
                ],
                "cdn_urls": {
                    "css": "https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css",
                    "js": "https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"
                },
                "template_updates": [
                    {
                        "old": "dataTables.bootstrap4.min.css",
                        "new": "dataTables.bootstrap5.min.css"
                    },
                    {
                        "old": "dataTables.bootstrap4.min.js",
                        "new": "dataTables.bootstrap5.min.js"
                    }
                ]
            },
            "select2": {
                "name": "Select2",
                "description": "下拉选择器组件",
                "bootstrap4_files": [
                    "vendor/select2/css/select2-bootstrap4.min.css"
                ],
                "bootstrap5_files": [
                    "vendor/select2/css/select2-bootstrap5.min.css"
                ],
                "cdn_urls": {
                    "css": "https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"
                },
                "template_updates": [
                    {
                        "old": "select2-bootstrap4.min.css",
                        "new": "select2-bootstrap5.min.css"
                    }
                ]
            },
            "fontawesome": {
                "name": "FontAwesome",
                "description": "图标库",
                "current_version": "5.15.4",
                "recommended_version": "6.5.0",
                "bootstrap5_compatible": True,
                "upgrade_notes": "FontAwesome 6.x 完全兼容 Bootstrap 5，建议升级以获得更多图标和更好的性能"
            },
            "jquery_ui": {
                "name": "jQuery UI",
                "description": "UI组件库",
                "bootstrap5_compatible": "partial",
                "issues": [
                    "日期选择器样式可能与Bootstrap 5冲突",
                    "对话框组件建议替换为Bootstrap 5原生模态框",
                    "拖拽功能需要额外的CSS调整"
                ],
                "recommendations": [
                    "考虑使用Bootstrap 5原生组件替代",
                    "如必须使用，需要自定义CSS覆盖冲突样式"
                ]
            },
            "toastr": {
                "name": "Toastr",
                "description": "通知组件",
                "bootstrap5_compatible": True,
                "notes": "Toastr与Bootstrap 5完全兼容，无需特殊处理"
            }
        }
        
        # 检查结果
        self.results = {
            "libraries_checked": 0,
            "compatible_libraries": [],
            "incompatible_libraries": [],
            "updated_libraries": [],
            "manual_tasks": [],
            "warnings": []
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def check_library_status(self, lib_name: str, lib_config: Dict) -> Dict:
        """检查单个库的状态"""
        status = {
            "name": lib_config["name"],
            "description": lib_config["description"],
            "status": "unknown",
            "bootstrap4_exists": False,
            "bootstrap5_exists": False,
            "needs_update": False,
            "actions": []
        }
        
        # 检查Bootstrap 4文件
        if "bootstrap4_files" in lib_config:
            for file_path in lib_config["bootstrap4_files"]:
                full_path = self.static_root / file_path
                if full_path.exists():
                    status["bootstrap4_exists"] = True
                    break
        
        # 检查Bootstrap 5文件
        if "bootstrap5_files" in lib_config:
            for file_path in lib_config["bootstrap5_files"]:
                full_path = self.static_root / file_path
                if full_path.exists():
                    status["bootstrap5_exists"] = True
                    break
        
        # 确定状态
        if lib_config.get("bootstrap5_compatible") == True:
            status["status"] = "compatible"
            status["actions"].append("无需特殊处理，完全兼容Bootstrap 5")
        elif lib_config.get("bootstrap5_compatible") == "partial":
            status["status"] = "partial"
            status["needs_update"] = True
            status["actions"].extend(lib_config.get("recommendations", []))
        elif status["bootstrap4_exists"] and not status["bootstrap5_exists"]:
            status["status"] = "needs_update"
            status["needs_update"] = True
            status["actions"].append("需要下载Bootstrap 5兼容版本")
        elif status["bootstrap5_exists"]:
            status["status"] = "ready"
            status["actions"].append("已准备好Bootstrap 5")
        else:
            status["status"] = "not_found"
            status["actions"].append("未找到相关文件")
        
        return status
    
    def download_bootstrap5_files(self, lib_name: str, lib_config: Dict) -> bool:
        """下载Bootstrap 5兼容文件"""
        if "cdn_urls" not in lib_config:
            self.logger.warning(f"⚠️ {lib_config['name']}: 没有配置CDN下载地址")
            return False
        
        try:
            import requests
            
            for file_type, url in lib_config["cdn_urls"].items():
                # 确定本地文件路径
                if "bootstrap5_files" in lib_config:
                    local_files = [f for f in lib_config["bootstrap5_files"] if file_type in f]
                    if local_files:
                        local_path = self.static_root / local_files[0]
                        
                        # 创建目录
                        local_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 下载文件
                        self.logger.info(f"📥 下载 {lib_config['name']} {file_type.upper()}: {url}")
                        response = requests.get(url, timeout=30)
                        response.raise_for_status()
                        
                        with open(local_path, 'wb') as f:
                            f.write(response.content)
                        
                        self.logger.info(f"✅ 已保存: {local_path}")
            
            return True
            
        except ImportError:
            self.logger.warning("⚠️ 未安装requests库，请手动下载文件")
            return False
        except Exception as e:
            self.logger.error(f"❌ 下载失败: {e}")
            return False
    
    def create_compatibility_files(self, lib_name: str, lib_config: Dict) -> bool:
        """创建兼容性文件（复制Bootstrap 4版本作为临时解决方案）"""
        if "bootstrap4_files" not in lib_config or "bootstrap5_files" not in lib_config:
            return False
        
        try:
            for bs4_file, bs5_file in zip(lib_config["bootstrap4_files"], lib_config["bootstrap5_files"]):
                bs4_path = self.static_root / bs4_file
                bs5_path = self.static_root / bs5_file
                
                if bs4_path.exists() and not bs5_path.exists():
                    # 创建目录
                    bs5_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(bs4_path, bs5_path)
                    self.logger.info(f"📋 创建兼容文件: {bs5_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 创建兼容文件失败: {e}")
            return False
    
    def update_template_references(self, lib_name: str, lib_config: Dict):
        """更新模板文件中的引用"""
        if "template_updates" not in lib_config:
            return
        
        templates_root = self.project_root / "app" / "templates"
        if not templates_root.exists():
            return
        
        updated_files = []
        
        for html_file in templates_root.rglob("*.html"):
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 应用更新
                for update in lib_config["template_updates"]:
                    if update["old"] in content:
                        content = content.replace(update["old"], update["new"])
                
                # 如果有变更，保存文件
                if content != original_content:
                    with open(html_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    updated_files.append(str(html_file.relative_to(self.project_root)))
                    self.logger.info(f"📝 更新模板引用: {html_file.relative_to(self.project_root)}")
            
            except Exception as e:
                self.logger.error(f"❌ 更新模板文件失败 {html_file}: {e}")
        
        return updated_files
    
    def run_compatibility_check(self) -> Dict:
        """运行兼容性检查和更新"""
        self.logger.info("🔍 开始第三方库兼容性检查...")
        
        for lib_name, lib_config in self.libraries.items():
            self.logger.info(f"📦 检查 {lib_config['name']}...")
            
            # 检查库状态
            status = self.check_library_status(lib_name, lib_config)
            self.results["libraries_checked"] += 1
            
            # 根据状态采取行动
            if status["status"] == "compatible":
                self.results["compatible_libraries"].append(status)
                self.logger.info(f"✅ {lib_config['name']}: 完全兼容")
                
            elif status["status"] == "needs_update":
                self.results["incompatible_libraries"].append(status)
                self.logger.warning(f"⚠️ {lib_config['name']}: 需要更新")
                
                # 尝试下载Bootstrap 5版本
                if self.download_bootstrap5_files(lib_name, lib_config):
                    self.results["updated_libraries"].append(lib_config["name"])
                    # 更新模板引用
                    updated_files = self.update_template_references(lib_name, lib_config)
                    if updated_files:
                        self.logger.info(f"📝 已更新 {len(updated_files)} 个模板文件")
                else:
                    # 创建兼容性文件作为备选方案
                    if self.create_compatibility_files(lib_name, lib_config):
                        self.results["updated_libraries"].append(f"{lib_config['name']} (兼容版本)")
                
            elif status["status"] == "partial":
                self.results["incompatible_libraries"].append(status)
                self.results["manual_tasks"].extend([
                    f"{lib_config['name']}: {action}" for action in status["actions"]
                ])
                self.logger.warning(f"⚠️ {lib_config['name']}: 部分兼容，需要手动处理")
                
            elif status["status"] == "ready":
                self.results["compatible_libraries"].append(status)
                self.logger.info(f"✅ {lib_config['name']}: 已准备好")
                
            else:
                self.logger.info(f"ℹ️ {lib_config['name']}: 未找到相关文件")
        
        self.logger.info("✅ 第三方库兼容性检查完成")
        return self.results
    
    def generate_report(self) -> str:
        """生成兼容性报告"""
        report = []
        report.append("# 第三方库Bootstrap 5兼容性报告")
        report.append("")
        report.append(f"**检查时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"**检查库数量**: {self.results['libraries_checked']}")
        report.append("")
        
        # 兼容库列表
        if self.results["compatible_libraries"]:
            report.append("## ✅ 兼容的库")
            report.append("")
            for lib in self.results["compatible_libraries"]:
                report.append(f"- **{lib['name']}**: {lib['description']}")
                for action in lib["actions"]:
                    report.append(f"  - {action}")
            report.append("")
        
        # 不兼容库列表
        if self.results["incompatible_libraries"]:
            report.append("## ⚠️ 需要处理的库")
            report.append("")
            for lib in self.results["incompatible_libraries"]:
                report.append(f"- **{lib['name']}**: {lib['description']}")
                report.append(f"  - 状态: {lib['status']}")
                for action in lib["actions"]:
                    report.append(f"  - {action}")
            report.append("")
        
        # 已更新库列表
        if self.results["updated_libraries"]:
            report.append("## 🔄 已更新的库")
            report.append("")
            for lib_name in self.results["updated_libraries"]:
                report.append(f"- {lib_name}")
            report.append("")
        
        # 手动任务
        if self.results["manual_tasks"]:
            report.append("## 📋 手动任务")
            report.append("")
            for task in self.results["manual_tasks"]:
                report.append(f"- [ ] {task}")
            report.append("")
        
        return "\n".join(report)
    
    def save_report(self):
        """保存报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.project_root / f"third_party_compatibility_report_{timestamp}.md"
        
        try:
            report_content = self.generate_report()
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"📊 兼容性报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存报告失败: {e}")


def main():
    """主函数"""
    print("🔧 第三方库Bootstrap 5兼容性更新工具")
    print("=" * 50)
    
    updater = ThirdPartyCompatibilityUpdater(".")
    results = updater.run_compatibility_check()
    
    # 显示结果摘要
    print(f"\n📊 检查结果摘要:")
    print(f"  - 检查库数量: {results['libraries_checked']}")
    print(f"  - 兼容库数量: {len(results['compatible_libraries'])}")
    print(f"  - 需要处理: {len(results['incompatible_libraries'])}")
    print(f"  - 已更新: {len(results['updated_libraries'])}")
    print(f"  - 手动任务: {len(results['manual_tasks'])}")
    
    # 保存报告
    updater.save_report()


if __name__ == "__main__":
    main()
