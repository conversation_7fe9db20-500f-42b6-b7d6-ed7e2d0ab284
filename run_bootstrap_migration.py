#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5.3.6 迁移执行脚本
==========================

简化的执行脚本，用于快速运行Bootstrap迁移工具

使用方法:
1. 预览模式: python run_bootstrap_migration.py preview
2. 执行迁移: python run_bootstrap_migration.py migrate
3. 回滚操作: python run_bootstrap_migration.py rollback
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from bootstrap_5_3_6_migration_tool import Bootstrap536MigrationTool
except ImportError as e:
    print(f"❌ 导入迁移工具失败: {e}")
    print("请确保 bootstrap_5_3_6_migration_tool.py 文件存在")
    sys.exit(1)


def print_banner():
    """打印工具横幅"""
    print("=" * 70)
    print("🚀 StudentsCMSSP Bootstrap 5.3.6 迁移工具")
    print("=" * 70)
    print("📋 功能: 将项目从Bootstrap 4.6.2升级到Bootstrap 5.3.6")
    print("🔧 特性: 自动检测、批量替换、备份保护、回滚支持")
    print("=" * 70)


def print_usage():
    """打印使用说明"""
    print("\n📖 使用方法:")
    print("  python run_bootstrap_migration.py <command>")
    print("\n🔧 可用命令:")
    print("  preview  - 预览模式，检查将要进行的更改（推荐先运行）")
    print("  migrate  - 执行实际迁移")
    print("  rollback - 回滚到最新备份")
    print("  help     - 显示此帮助信息")
    print("\n💡 示例:")
    print("  python run_bootstrap_migration.py preview   # 预览更改")
    print("  python run_bootstrap_migration.py migrate   # 执行迁移")
    print("  python run_bootstrap_migration.py rollback  # 回滚更改")


def run_preview():
    """运行预览模式"""
    print("\n🔍 运行预览模式...")
    print("📝 注意: 预览模式不会修改任何文件，只显示将要进行的更改")
    
    try:
        tool = Bootstrap536MigrationTool(".")
        tool.run_migration(dry_run=True, download_bootstrap=False)
        
        print("\n✅ 预览完成！")
        print("💡 如果预览结果满意，请运行: python run_bootstrap_migration.py migrate")
        
    except Exception as e:
        print(f"❌ 预览失败: {e}")
        return False
    
    return True


def run_migration():
    """运行实际迁移"""
    print("\n⚠️  即将执行实际迁移操作！")
    
    # 确认操作
    confirm = input("🤔 确定要继续吗？这将修改项目文件。(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return False
    
    print("\n🚀 开始执行迁移...")
    
    try:
        tool = Bootstrap536MigrationTool(".")
        tool.run_migration(dry_run=False, download_bootstrap=True)
        
        print("\n✅ 迁移完成！")
        print("📋 后续步骤:")
        print("  1. 检查生成的迁移报告")
        print("  2. 测试应用程序功能")
        print("  3. 如有问题，可使用回滚功能")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        print("💡 建议检查错误日志，或使用回滚功能恢复")
        return False
    
    return True


def run_rollback():
    """运行回滚操作"""
    print("\n🔄 准备回滚操作...")
    
    # 查找备份目录
    backup_dirs = list(Path(".").glob("bootstrap_migration_backup_*"))
    
    if not backup_dirs:
        print("❌ 未找到备份目录")
        print("💡 请确保之前已运行过迁移操作")
        return False
    
    # 显示可用备份
    print(f"\n📁 找到 {len(backup_dirs)} 个备份:")
    for i, backup_dir in enumerate(sorted(backup_dirs, reverse=True)):
        print(f"  {i+1}. {backup_dir.name}")
    
    # 确认回滚
    confirm = input("\n🤔 确定要回滚到最新备份吗？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 回滚已取消")
        return False
    
    try:
        tool = Bootstrap536MigrationTool(".")
        tool.rollback()
        
        print("\n✅ 回滚完成！")
        print("📋 项目已恢复到迁移前的状态")
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False
    
    return True


def check_environment():
    """检查运行环境"""
    # 检查是否在正确的目录
    if not Path("app").exists():
        print("❌ 错误: 未找到 'app' 目录")
        print("💡 请确保在StudentsCMSSP项目根目录中运行此脚本")
        return False
    
    if not Path("app/templates").exists():
        print("❌ 错误: 未找到 'app/templates' 目录")
        print("💡 请确保项目结构完整")
        return False
    
    return True


def main():
    """主函数"""
    print_banner()
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print_usage()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    # 显示帮助
    if command in ['help', '-h', '--help']:
        print_usage()
        sys.exit(0)
    
    # 检查运行环境
    if not check_environment():
        sys.exit(1)
    
    # 执行相应命令
    success = False
    
    if command == 'preview':
        success = run_preview()
    elif command == 'migrate':
        success = run_migration()
    elif command == 'rollback':
        success = run_rollback()
    else:
        print(f"❌ 未知命令: {command}")
        print_usage()
        sys.exit(1)
    
    # 退出状态
    if success:
        print(f"\n🎉 {command} 操作成功完成！")
        sys.exit(0)
    else:
        print(f"\n💥 {command} 操作失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
