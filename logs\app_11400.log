2025-06-19 13:41:51,314 INFO: 应用启动 - PID: 11400 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 18:11:33,035 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:11:37,503 ERROR: 查看库存详情失败: 'Inventory' object has no attribute 'warehouse' [in C:\StudentsCMSSP\app\routes\inventory.py:457]
2025-06-19 18:11:37,551 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:12:00,231 ERROR: Exception on /inventory/item/81/label/print [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\inventory.py", line 897, in print_item_label
    if not current_user.can_access_area_by_id(inventory.warehouse.area_id):
AttributeError: 'Inventory' object has no attribute 'warehouse'
2025-06-19 18:12:33,000 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-19 18:12:33,001 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-19 18:12:33,001 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-19 18:12:33,010 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-19 18:12:33,011 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-19 18:12:33,018 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-19 18:12:33,018 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,019 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,019 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,019 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,020 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,020 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,020 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,021 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,021 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,021 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,022 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,022 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,023 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,023 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,023 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,023 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,024 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,024 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,024 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,024 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,025 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,025 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,025 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,026 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,026 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,027 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,027 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,027 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,027 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,028 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,028 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,028 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,029 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,029 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,029 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,029 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,030 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,030 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,030 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,035 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,097 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,266 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,350 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,352 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,353 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,354 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,355 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,355 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,356 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,356 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,356 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,357 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,357 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,358 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,358 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,358 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,359 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:12:33,366 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-19 18:12:33,366 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-19 18:12:33,367 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-19 18:12:33,374 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-19 18:12:33,385 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-19 18:12:33,389 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,399 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,401 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,405 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,405 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,409 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,412 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,417 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,422 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,423 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,426 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,427 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,430 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,432 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,435 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,437 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,439 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,442 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,444 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,445 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,446 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:12:33,447 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-19 18:13:44,019 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-19 18:13:44,020 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-19 18:13:44,020 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-19 18:13:44,022 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-19 18:13:44,023 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-19 18:13:44,027 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-19 18:13:44,028 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,028 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,028 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,028 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,028 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,029 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,029 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,029 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,030 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,030 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,030 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,030 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,031 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,031 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,031 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,031 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,032 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,032 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,032 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,032 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,033 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,033 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,033 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,033 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,034 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,034 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,034 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,034 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,034 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,035 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,035 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,037 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,039 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,039 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,039 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,040 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,040 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,040 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,040 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,041 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,041 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,041 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,041 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,042 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,042 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,042 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,043 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,043 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,043 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,044 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,044 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,044 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,044 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,044 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,045 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,045 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,045 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-19 18:13:44,052 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-19 18:13:44,053 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-19 18:13:44,055 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-19 18:13:44,067 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-19 18:13:44,084 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-19 18:13:44,090 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,095 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,096 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,100 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,102 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,109 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,115 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,117 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,118 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,119 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,120 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,122 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,123 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,124 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,126 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,127 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,128 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,130 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,132 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,148 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,161 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-19 18:13:44,162 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-19 18:14:00,904 INFO: 开始溯源出库单: CK20250613215921 [in C:\StudentsCMSSP\app\routes\food_trace.py:131]
2025-06-19 18:14:00,907 INFO: 查询菜谱：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-19 18:14:00,909 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-19 18:14:00,911 INFO: 匹配条件的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-19 18:14:00,911 INFO: 未找到 2025-06-09 早餐+午餐+晚餐 的菜谱信息 [in C:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-19 18:14:01,058 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=18 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-19 18:14:03,426 INFO: 查询菜谱：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-19 18:14:03,428 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-19 18:14:03,432 INFO: 匹配条件的食谱有 3 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-19 18:14:03,456 INFO:   - 食谱: 包子 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:03,463 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:03,479 INFO:   - 食谱: 爽口面条（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:03,487 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=4, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-19 18:14:25,892 INFO: 查询菜谱：日期=2025-06-19, 星期=3(0=周一), day_of_week=4, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-19 18:14:25,894 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-19 18:14:25,895 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-19 18:14:25,900 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:25,909 INFO:   - 食谱: 炒包菜（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:25,924 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:25,932 INFO:   - 食谱: 西红柿炒蛋（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-19 18:14:25,938 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=7, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-19 18:14:39,237 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-19 18:14:41,626 ERROR: 查看库存详情失败: 'Inventory' object has no attribute 'warehouse' [in C:\StudentsCMSSP\app\routes\inventory.py:457]
2025-06-19 18:14:41,676 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
